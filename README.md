# Heat Pump Compressor Diagnostic Tool

A portable diagnostic system for quick assessment of heat pump compressor health using vibration analysis and machine learning.

## 🎯 Overview

This project develops a handheld diagnostic tool for service technicians to quickly assess compressor defects in heat pumps during service calls. The system uses vibration analysis with ADXL345 accelerometers, signal processing, and machine learning to provide instant classification of compressor health status.

The research addresses a critical need in the HVAC industry where countless compressors are unnecessarily replaced due to lack of effective field diagnostic tools. This portable solution enables technicians to make data-driven decisions on-site, representing significant cost savings potential for service providers and end users.

## 🏗️ System Architecture

### Hardware Components

- **ESP32-S3 DevKitC-1**: High-performance microcontroller for real-time data acquisition
- **Dual ADXL345 Accelerometers**: 3-axis MEMS sensors on GY-291 breakouts
- **Data Collection**: 3,200 Hz sampling rate with interrupt-driven acquisition
- **Communication**: High-speed UART (460.8k baud) + SD card logging for field deployment

### Sensor Configuration

```
SPI Bus (Sensors):     SCLK=15, MOSI=17, MISO=18
Sensor 1:              CS=5, INT=12
Sensor 2:              CS=6, INT=11
SD Card (SPI2):        SCLK=14, MOSI=13, MISO=10, CS=9
```

## 📁 Project Structure

### `/firmware` - ESP32-S3 Embedded System
- **Real-time data acquisition** from dual ADXL345 sensors
- **Interrupt-driven sampling** at 3,200 Hz per sensor
- **Binary UART streaming** with packet synchronization
- **SD card logging** for field deployments
- **LED status indicators** for operational feedback

### `/importer` - Rust Data Processing Pipeline
- **High-performance CSV/Parquet processing** using custom built DataFrame
- **Feature extraction** including spectral analysis and time-domain statistics
- **Data merging** with heat pump operational parameters
- **DuckDB export** for efficient data storage and querying of partial data

### `/labrecord` - Data Collection Utility
- **TCP/Serial data capture** from ESP32 firmware
- **Real-time FFT visualization** and monitoring
- **Robust and self-healing** for continous data
- **CSV export** with structured metadata

### `/eda` - Exploratory Data Analysis
- **Python/Jupyter environment** for data visualization
- **Time-frequency analysis** and spectrogram generation
- **Feature correlation analysis** and pattern discovery
- **Statistical modeling** and preliminary classification

### `/thesis` - Academic Documentation
- **Typst-based thesis compilation** with automatic daily builds
- **Comprehensive literature review** and methodology documentation
- **Results analysis** and future research directions

## 🚀 Quick Start

### Prerequisites

- **ESP-IDF v5.x** for firmware development
- **Rust toolchain** for data processing components  
- **Python 3.8+** with scientific computing stack
- **VS Code** with recommended extensions

### Firmware Setup

```bash
cd firmware
idf.py build flash monitor
```

### Data Collection

Copy the `config.toml` from `labrecord/config.toml`
and update the com port and other settings as needed.

```toml
port = "/dev/cu.usbserial-113230"
scenario = "my-cool-recoding"
defect = false
# defect_kind = "unknown"
```

Then run the data collection utility:

```bash
cd labrecord
cargo run -r  # Replace with ESP32 IP
```

### Data Processing

This will import the raw sensor data and measurements from the testbench into raw parquet files and then extract features from 5s windows of the data.

```bash
cd importer
cargo run -- -i /path/to/raw/data dataset_name
```

The data can then be imported into DuckDB for further analysis:

```sql
SELECT * FROM w5
WHERE scenario = (SELECT id FROM scenarios WHERE name = 'my-cool-recoding');
```

```sql
SELECT * FROM w5
WHERE defect = true;
```

```sql
SELECT * FROM w5
WHERE timestamp > '2025-06-01 00:00:00' AND timestamp < '2025-06-02 00:00:00';
```

### Analysis

```bash
cd eda
pip install uv
uv sync
jupyter lab
```

## 📊 Technical Specifications

### Data Acquisition
- **Sampling Rate**: 3,200 Hz per sensor (6,400 Hz combined)
- **Resolution**: 13-bit accelerometer data (±16g full-resolution)
- **Data Rate**: ~173 KB/s binary stream
- **Latency**: <1ms interrupt response time

### Signal Processing
- **Window Size**: 5-second with 20% overlap
- **Features**: Time-domain statistics, spectral descriptors, band-power ratios
- **Frequency Analysis**: 0-1600 Hz with dominant peak detection
- **Preprocessing**: None

### Machine Learning Pipeline

TODO

## 🔧 Development

### Building Firmware
```bash
cd firmware
idf.py set-target esp32s3
idf.py build
```


### Thesis Compilation
```bash
cd thesis
typst compile content/thesis.typ out/thesis.pdf --root .
# or for development
typst watch content/thesis.typ out/thesis.pdf --root .
```

## 📄 License

This project is part of a master's thesis research. Please refer to individual component licenses for usage rights.

---

*This research aims to reduce unnecessary compressor replacements in the HVAC industry through reliable, cost-effective diagnostic technology.*
