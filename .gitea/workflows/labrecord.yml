name: Build labrecord tool

env:
  GITEA_HOST: https://git.torkleyy.com
  CARGO_TERM_COLOR: always

on:
  push:
    branches: [ "main" ]
    paths:
      - 'labrecord/**'
      - '.gitea/workflows/labrecord.yml'
    tags:
      - 'labrecord-*'
  pull_request:
    branches: [ "main" ]
    paths:
      - 'labrecord/**'

jobs:
  build:
    runs-on: ubuntu-arm64

    steps:
    - uses: actions/checkout@v4
    - name: Install mingw-w64
      run: |
        sudo apt-get update
        sudo apt-get install -y mingw-w64 clang
    - name: Set up Rust
      uses: actions-rust-lang/setup-rust-toolchain@v1
      with:
        toolchain: stable
        target: x86_64-pc-windows-gnu

    - name: Build Windows binary
      run: |
        cd labrecord
        cargo build --release --target x86_64-pc-windows-gnu

    - name: Upload artifact
      uses: actions/upload-artifact@v3
      with:
        name: labrecord-windows
        path: labrecord/target/x86_64-pc-windows-gnu/release/labrecord.exe
        if-no-files-found: error

    - name: Create release for labrecord tags
      if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/labrecord-')
      uses: actions/create-release@v1
      id: create-release
      env:
        GITHUB_TOKEN: ${{ secrets.GITEA_TOKEN }}
      with:
        tag_name: ${{ github.ref_name }}
        release_name: Labrecord ${{ github.ref_name }}
        body: |
          # Labrecord ${{ github.ref_name }}
          
          ## Build Information
          - Commit: ${{ github.sha }}
          - Built at: ${{ github.run_started_at }}
          - Target: x86_64-pc-windows-gnu
          
          ## Downloads
          - labrecord.exe: Windows binary for the labrecord tool
        draft: false
        prerelease: false

    - name: Upload binary to release
      if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/labrecord-')
      run: |
        # Gitea expects the upload URL in the form:
        #   ${UPLOAD_URL}?name=<asset_name>
        UPLOAD_URL="${{ steps.create-release.outputs.upload_url }}?name=labrecord.exe"
        curl -X POST "$UPLOAD_URL" \
          -H "Authorization: token ${{ secrets.GITEA_TOKEN }}" \
          -H "Content-Type: application/octet-stream" \
          --data-binary @labrecord/target/x86_64-pc-windows-gnu/release/labrecord.exe
