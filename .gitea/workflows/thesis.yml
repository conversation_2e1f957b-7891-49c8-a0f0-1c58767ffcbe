name: Daily Thesis Release

env:
  GITEA_HOST: https://git.torkleyy.com

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    tags:
      - 'thesis-*'
  workflow_dispatch:
    inputs:
      tag:
        description: 'Override the release tag (e.g. thesis-special-version).'
        required: false
        default: ''
      release_name:
        description: 'Override the release name.'
        required: false
        default: ''

jobs:
  check-thesis-commits:
    runs-on: ubuntu-arm64
    # Only run commit checking for scheduled builds, not for tag pushes
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    outputs:
      should-release: ${{ steps.check.outputs.should-release }}
      last-release-date: ${{ steps.check.outputs.last-release-date }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check for thesis commits since last release
        id: check
        run: |
          LATEST_RELEASE=$(curl -s "${{ env.GITEA_HOST }}/api/v1/repos/${{ github.repository }}/releases/latest" \
            -H "Authorization: token ${{ secrets.GITEA_TOKEN }}" | jq -r '.created_at // empty')

          if [ -z "$LATEST_RELEASE" ]; then
            echo "should-release=true" >> $GITHUB_OUTPUT
            echo "last-release-date=none" >> $GITHUB_OUTPUT
          else
            echo "last-release-date=$LATEST_RELEASE" >> $GITHUB_OUTPUT

            RELEASE_TS=$(date -d "$LATEST_RELEASE" +%s)
            CUTOFF_TS=$((RELEASE_TS - 3600))
            COUNT=$(git rev-list --count --since="@$CUTOFF_TS" HEAD -- thesis/)
            if [ "$COUNT" -gt 0 ]; then
              echo "should-release=true" >> $GITHUB_OUTPUT
            else
              echo "should-release=false" >> $GITHUB_OUTPUT
            fi
          fi

  build-and-release-thesis:
    needs: check-thesis-commits
    # Always run for tag pushes, run for scheduled/manual if commits were found
    if: |
      github.event_name == 'push' ||
      (needs.check-thesis-commits.result != 'skipped' && needs.check-thesis-commits.outputs.should-release == 'true')
    runs-on: ubuntu-arm64
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install New Computer Modern Sans font
        run: |
          apt update -y && apt install -y fonts-cmu

      - name: Install Typst (ARM64)
        run: |
          curl -fsSL "https://github.com/typst/typst/releases/latest/download/typst-aarch64-unknown-linux-musl.tar.xz" \
            | tar -xJ --strip-components=1
          sudo mv typst /usr/local/bin/
          typst --version

      - name: Set release tag & name
        id: vars
        run: |
          if [ -n "${{ github.event.inputs.tag }}" ]; then
            # Manual workflow dispatch with custom tag
            echo "RELEASE_TAG=${{ github.event.inputs.tag }}" >> $GITHUB_ENV
          elif [ "${{ github.event_name }}" = "push" ]; then
            # Tag push - use the pushed tag
            echo "RELEASE_TAG=${{ github.ref_name }}" >> $GITHUB_ENV
          else
            # Scheduled build - generate daily tag
            echo "RELEASE_TAG=thesis-$(date +%Y%m%d)" >> $GITHUB_ENV
          fi

          if [ -n "${{ github.event.inputs.release_name }}" ]; then
            # Manual workflow dispatch with custom release name
            echo "RELEASE_NAME=${{ github.event.inputs.release_name }}" >> $GITHUB_ENV
          elif [ "${{ github.event_name }}" = "push" ]; then
            # Tag push - use "Thesis <tag>"
            TAG_NAME="${{ github.ref_name }}"
            echo "RELEASE_NAME=Thesis ${TAG_NAME}" >> $GITHUB_ENV
          else
            # Scheduled build - use "Thesis Daily Build"
            echo "RELEASE_NAME=Thesis Daily Build $(date +%Y-%m-%d)" >> $GITHUB_ENV
          fi

      - name: Compile thesis
        run: |
          cd thesis
          typst compile --root . content/thesis.typ out/thesis.pdf

      - name: Generate thesis changelog
        id: changelog
        run: |
          RELEASE_DATE=$(date +%Y-%m-%d)
          
          # Determine last release date based on trigger type
          if [ "${{ github.event_name }}" = "push" ]; then
            # For tag pushes, get the last release from API
            LATEST_RELEASE=$(curl -s "${{ env.GITEA_HOST }}/api/v1/repos/${{ github.repository }}/releases/latest" \
              -H "Authorization: token ${{ secrets.GITEA_TOKEN }}" | jq -r '.created_at // empty')
            if [ -n "$LATEST_RELEASE" ]; then
              LAST_RELEASE="$LATEST_RELEASE"
            else
              LAST_RELEASE="none"
            fi
          else
            # For scheduled/manual builds, use the output from check job
            LAST_RELEASE="${{ needs.check-thesis-commits.outputs.last-release-date }}"
          fi

          if [ "$LAST_RELEASE" != "none" ]; then
            SINCE_DATE=$(date -d "$LAST_RELEASE" +%Y-%m-%d)
            RANGE="--since=\"$SINCE_DATE\""
          else
            RANGE="--all"
          fi

          cat > thesis-release-notes.md << EOF
          # $RELEASE_NAME - $RELEASE_DATE

          ## Changes since last release
          $(git log $RANGE --oneline --no-merges -- thesis/ | head -20)

          ## Stats
          - Word count: $(typst query thesis/content/thesis.typ --field text | wc -w)
          - Commit: $(git rev-parse --short HEAD)
          - Built at: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          EOF

          echo "RELEASE_TAG=$RELEASE_TAG" >> $GITHUB_OUTPUT

      - name: Create thesis release
        uses: actions/create-release@v1
        id: create-release
        env:
          GITHUB_TOKEN: ${{ secrets.GITEA_TOKEN }}
        with:
          tag_name: ${{ env.RELEASE_TAG }}
          release_name: ${{ env.RELEASE_NAME }}
          body_path: thesis-release-notes.md
          draft: false
          prerelease: false

      - name: Upload thesis PDF to Gitea
        run: |
          # Gitea expects the upload URL in the form:
          #   ${UPLOAD_URL}?name=<asset_name>
          UPLOAD_URL="${{ steps.create-release.outputs.upload_url }}?name=${{ env.RELEASE_TAG }}.pdf"
          curl -X POST "$UPLOAD_URL" \
            -H "Authorization: token ${{ secrets.GITEA_TOKEN }}" \
            -H "Content-Type: application/pdf" \
            --data-binary @thesis/out/thesis.pdf
