#import "../utils/todo.typ": TODO

= Introduction

== Background and Motivation

Heat pumps are increasingly recognized as pivotal technologies for sustainable climate control in both residential and
commercial sectors. Their adoption dramatically reduces CO₂ emissions and significantly improves energy efficiency
compared to traditional boilers or air conditioners. However, the heart of any heat pump system—its compressor—is
particularly vulnerable to mechanical wear and tear, leading to performance degradation or outright failure. When a heat
pump malfunctions, pinpointing the root cause, especially whether it originates from the compressor, is often a complex
and ambiguous task.

This diagnostic ambiguity frequently results in the premature and unnecessary replacement of compressors. Given that
compressors are among the most expensive components within a heat pump system, this practice incurs substantial
financial waste. At Wolf GmbH, a leading manufacturer of heating, ventilation, and air conditioning (HVAC) systems, this
issue is a significant concern, driving the need for more precise diagnostic capabilities. The core issue lies in the
pervasive lack of reliable, on-site diagnostic tools capable of accurately identifying compressor health states.
Addressing this diagnostic gap presents a significant opportunity for cost savings and enhanced operational efficiency
within the HVAC industry.

#TODO("Insert brief statistics on how often compressor faults trigger service calls")

#TODO("Add a figure that locates the compressor in a typical domestic heat-pump schematic")

== Problem Statement

Despite the critical role of compressors in heat pump efficiency and the high costs associated with their failure or
premature replacement, there remains a significant challenge in reliably and cost-effectively diagnosing their health
status in situ. Current diagnostic practices often lack the precision to differentiate between a defect sensor and
critical faults of the compressor, leading to reactive maintenance or wasteful component swaps. This problem is
exacerbated by the often harsh operating conditions of heat pump compressors, which can involve extreme temperatures and
continuous mechanical stress. Therefore, there is a need for an advanced diagnostic solution that can provide actionable
insights into compressor health in real-time, thereby enabling predictive maintenance and preventing unnecessary
expenditures.

== Objective

This thesis aims to design, develop, and evaluate an end-to-end predictive maintenance solution for heat pump
compressors. This solution will leverage vibration sensing and advanced machine learning algorithms to accurately
classify the compressor's health state. Recognizing the demanding operational environment, particularly the high surface
temperatures (up to 120°C) of the compressor, the practical work will focus exclusively on a dedicated, externally
mounted sensor node paired with a microcontroller. An early idea, to try and utilize a smartphone with its integrated
sensors, was ultimately discarded. Ultimately, this research seeks to demonstrate a reliable, field-deployable system
capable of providing precise, real-time diagnostic information to prevent costly failures and optimize compressor
lifespan.

== Research Questions

Based on the identified problem and the outlined objective, and in cooperation with Wolf GmbH, this thesis will
rigorously investigate the following research questions to achieve its goals within the context of Wolf GmbH's
operational needs:

How can a robust and temperature-resilient sensor node effectively acquire high-fidelity vibration data from a heat pump
compressor operating at temperatures up to 120°C? This question probes your hardware and firmware design capabilities.
What machine learning model architecture demonstrates optimal performance for classifying diverse compressor health
states (e.g., healthy, minor wear, specific fault conditions) from vibration data, while also being suitable for
deployment on resource-constrained edge devices? This focuses on your data science expertise, particularly model
selection and optimization for embedded systems. How can an efficient and reliable communication protocol be established
between the dedicated sensor node and a remote processing unit (e.g., a service technician's diagnostic tool or gateway)
to enable real-time data transfer for immediate health assessment? This addresses the crucial link between your hardware
and the diagnostic application, considering real-world deployment. What is the overall diagnostic accuracy, latency, and
real-world applicability of the complete end-to-end solution in identifying and predicting compressor anomalies within
an operational HVAC system? This evaluates the practical utility and performance of your integrated solution, essential
for its adoption by Wolf GmbH.

== Thesis Structure

The remainder of the document is organized as follows. @background introduces the thermodynamic principles of heat
pumps, describes compressor defects and reviews existing literature on the topic. Chapter 3 surveys vibration-sensing
technologies and outlines machine-learning techniques suitable for embedded implementation. Chapter 4 presents the
hardware prototypes, firmware architecture, and data-acquisition pipeline. Chapter 5 details the experimental protocol
and the resulting dataset, while Chapter 6 explains preprocessing, feature-engineering, and model-selection procedures.
Chapter 7 compares deployment strategies across microcontroller and cloud targets. Hardware trade-offs in precision,
cost, and usability are discussed alongside the sensor and deployment chapters. Finally, Chapter 8 summarises the key
findings and proposes avenues for future work.

#TODO("Replace this overview with refined one-sentence summaries once the chapter contents solidify")

= Background (Fundamentals) <background>

== Heat Pumps and Compressors

The vapour-compression heat-pump cycle relies on the compressor to raise the refrigerant pressure, enabling heat to be
transferred from a low-temperature source to a high-temperature sink. Wear mechanisms such as bearing degradation, vane
chipping, or rotor imbalance can disturb this process, degrading efficiency and eventually leading to catastrophic
failure. Understanding the physical manifestations of these faults—audible noise, elevated vibration amplitudes, and
abnormal harmonics—is essential for designing a diagnostic system.

#TODO("create a table of common compressor failure modes, their vibration signatures, and operational impact")

#TODO("Add a cut-away figure highlighting the moving parts inside the compressor")

== Vibration Sensing

Vibration signals are characterised by amplitude, frequency content, and temporal patterns. Analysing them in both time
and frequency domains reveals periodicities linked to mechanical components. MEMS accelerometers provide cost-effective,
compact sensing at kilohertz sampling rates, whereas piezoelectric probes offer superior sensitivity and wider
bandwidth. The smartphone option, while attractive for its ubiquity, is limited to lower sampling rates and cannot
tolerate the high temperatures encountered on compressor housings, making it unsuitable for the present application.

#TODO("Insert a time-domain waveform comparing healthy and faulty compressors")

#TODO("create a comparison table of MEMS, piezo, and smartphone sensors")

== Machine Learning for Fault Diagnosis

Modern fault-diagnosis pipelines extract statistical or spectral features from vibration windows and feed them into
supervised or unsupervised algorithms. Classical models such as Support-Vector Machines and Random Forests are
lightweight and interpretable but may struggle with complex patterns. Deep-learning architectures—compact
one-dimensional convolutional networks or gated recurrent units—learn hierarchical features directly from raw data yet
demand more memory and computation. Selecting an architecture therefore hinges on the available on-device resources and
the required inference latency.

#TODO("summarise pros and cons of classical ML versus deep learning for embedded use")

#TODO("cite two representative papers on vibration-based compressor diagnosis")

= Design and Methodology

== Research Approach

== Requirements

== Architecture

= Implementation

== External Sensor + Microcontroller

The current prototype employs an ESP32-S3 microcontroller connected to an ADXL345 accelerometer on a GY-291 breakout
board via SPI, sampling at the chip's maximum rate of 3 200 Hz. The ADXL345 is widely available, inexpensive, and
supported by numerous libraries, making it a practical choice for rapid prototyping. Each `DATA_READY` interrupt
triggers the firmware to read a single sample, ensuring deterministic timing. An earlier revision relied on a GRV 3AXIS
ACC16 over I²C at 200 Hz, but batch reads occasionally returned out-of-range values, which motivated the switch to SPI.

Firmware development began in Rust with the `esp-hal` ecosystem; however, intermittent UART framing errors prompted a
migration to ESP-IDF. The new C-based environment supports DMA-backed UART streaming at high baud rates and parallel
logging to a micro-SD card, the latter being indispensable during field deployments where wireless links are unreliable.
A companion Rust command-line utility, `labrecord`, parses the UART stream, validates checksums, and writes structured
records to disk.

Power is currently supplied via laboratory bench supplies, yet the microcontroller's low-power modes and the sensor’s
duty-cycling capabilities are under investigation for future battery-powered versions. Possible communication channels
for in-situ inference results include Bluetooth Low Energy, Wi-Fi, or hard-wired serial links.

Compared with repurposing a smartphone, this external node offers a lower noise floor, deterministic timing, and a
rugged housing. Its bill of materials, including the ADXL345 module and ESP32-S3 board, remains around \$Z, making it
cheaper to deploy at scale than sacrificing consumer phones. While firmware development and enclosure design demand
specialised effort, the finished node can be mounted quickly via a magnetic or clamped fixture and offers simple status
indicators tailored for technicians.

#TODO("Add a block diagram of the sensor, microcontroller, preprocessing, inference, and output pathway")

#TODO("List microcontrollers that support TensorFlow Lite or Edge Impulse")

== Smartphone Sensors (Excluded Option)

#TODO("seems misplaced here")

Early desk research considered leveraging smartphone accelerometers but was abandoned because compressor housings can
reach temperatures around 120 °C—far beyond the safe limits of consumer devices. All practical work therefore focuses on
the external sensor node.

#TODO("Briefly justify temperature data with manufacturer specs in a side note or citation")

== Data Processing and Machine Learning

=== Data Collection and Labeling

Experiments are conducted in two environments. The laboratory test bench couples a brine heat pump to programmable load
modules, providing fine-grained control over temperature, pressure, and rotational speed. In contrast, field studies on
air-source heat pumps capture realistic but uncontrollable conditions. Two ADXL345 units are mounted simultaneously: a
conveniently accessible location and a position that maximises mechanical coupling to the compressor shell. Data are
streamed over UART in the lab and buffered to the micro-SD card in the field. Each run spans several operating points
and is labelled as “Healthy”, “Minor fault”, or “Severe fault” depending on induced defects.

=== Test bench

copy from Kilian

#TODO("create a table summarising compressors, fault types, sample counts, and durations")

#TODO("explain the labelling procedure and reference standards")

Important covariates include sensor location, angular orientation, compressor speed, suction and discharge pressures,
and ambient temperature. Synthetic augmentation of slight sensor rotations is envisaged to assess robustness.

#TODO("measure and log ambient temperature per recording")

#TODO("add current or voltage monitoring as auxiliary features")

== Preprocessing and Feature Engineering

Raw data are first validated, trimmed to remove corrupted frames, and resampled if necessary. Exploratory plots and
spectrograms reveal outliers caused by loose mounting or electrical noise. Cleaned signals are divided into one-second
windows with 50 % overlap. For each window, time-domain statistics (root-mean-square, crest factor, skewness, kurtosis)
and frequency-domain descriptors (dominant peaks, band-power ratios) are computed. These features form the input to the
classical machine-learning models, whereas the deep-learning pipeline may ingest normalised raw waveforms directly.

#TODO("insert a spectrogram comparing healthy and faulty signatures")

#TODO("store cleaned logs in the Parquet format for efficient processing")

#TODO("specify the exact feature vector extracted per window")

== Model Comparison

Candidate models encompass SVMs with radial-basis kernels, Random Forests, Gradient-Boosted Trees, 1-D CNNs, and
lightweight LSTMs. A k-fold cross-validation scheme partitions data by compressor unit to minimise potential leakage.
Performance is reported in terms of accuracy, precision, recall, F1-score, model size, and inference latency.

#TODO("create a results table for all models")

#TODO("discuss trade-offs between accuracy and footprint")

== Model Deployment

Models can be deployed directly on the ESP32-S3 via TensorFlow Lite-Micro or Edge Impulse, or in the cloud where
computational budgets are less constrained. On-device deployment requires integer quantisation and flash-resident weight
arrays, whereas cloud inference enables larger architectures but incurs network latency and privacy concerns.

#TODO("add a flowchart of the two deployment pathways")

#TODO("compare latency, connectivity, complexity, and power consumption in a table")

= Potential Extensions

== Additional Sensor Data

Temperature sensors, current-draw monitors, and pressure transducers can supplement vibration data, enabling multi-modal
fault detection and prognostics.

#TODO("outline data-fusion strategies in prose or diagram")

== Advanced ML Techniques

Semi-supervised anomaly detection can flag novel failure modes without explicit labels. Transfer learning allows a model
trained on one compressor platform to adapt to another with minimal additional data. Online learning modules could
revise decision boundaries as new field data become available, thereby mitigating model drift over the lifecycle of the
device.

#TODO("autoencoders for novelty detection?")

= Evaluation and Results

= Conclusion

== Summary of Findings

A vibration-sensor node built around an ESP32-S3 microcontroller and dual ADXL345 accelerometers achieves reliable data
capture at 3 200 Hz, even under the elevated temperatures present on compressor housings. Preliminary analyses suggest
that classical frequency-domain features coupled with ensemble tree models deliver competitive accuracy while
maintaining real-time inference capability on the microcontroller. Thermal constraints rule out smartphone-based sensing
but do not preclude using a phone as an optional user interface in future iterations.

== Potential Future Developments

Future work will extend testing to a larger fleet of compressors and incorporate semi-supervised techniques for
detecting unseen faults. Planned field trials with service technicians will assess mounting repeatability and user
acceptance. Finally, integrating the diagnostic node into a predictive-maintenance dashboard could enable fleet-wide
monitoring and automatic scheduling of service interventions.