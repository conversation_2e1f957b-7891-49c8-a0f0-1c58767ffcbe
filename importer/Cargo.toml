[package]
name = "importer"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
arrow = "55"
encoding_rs = "0.8"
clap = { version = "4.0", features = ["derive"] }
chrono = { version = "0.4", features = ["serde"] }
chrono-tz = "0.10"
duckdb = { version = "1.3", features = ["bundled", "chrono"] }
indexmap = "2.9"
glob = "0.3"
rustfft = "6.2"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
rayon = "1.10.0"
parking_lot = "0.12.4"
parquet = "55.1.0"
eframe = "0.24"
egui = "0.24"
egui_plot = "0.24"

[dev-dependencies]
tempfile = "3"
approx = "0.5"
