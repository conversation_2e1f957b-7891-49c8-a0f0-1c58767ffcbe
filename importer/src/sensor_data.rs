// {"batch_start":"2025-06-04_14-09-31","defect":false,"defect_kind":null,"hostname":"thomas-mbp","scenario":"dev","scenario_start":"2025-06-04_14-09-30","sensor1_file":"readings-2025-06-04_14-09-31-s1.csv","sensor2_file":"readings-2025-06-04_14-09-31-s2.csv","source":"/dev/cu.usbserial-113230"}

use std::{fs, path::PathBuf};

use chrono::{DateTime, Utc};
use indexmap::IndexMap;
use serde::{Deserialize, Serialize};

use crate::df::DataFrame;

#[derive(Debug, Serialize, Deserialize)]
pub struct SensorReadingsMeta {
    pub scenario: String,
    pub defect: bool,
    pub defect_kind: Option<String>,
    pub batch_start: String,
    /// %Y-%m-%d_%H-%M-%S
    pub scenario_start: String,
    pub hostname: String,
    pub source: String,
    pub sensor1_file: String,
    pub sensor2_file: String,
}

pub struct SensorData {
    pub sensor: u8,
    pub readings: DataFrame,
    pub defect: bool,
}

pub fn import_meta(
    dir: &std::path::Path,
) -> Result<Vec<SensorReadingsMeta>, Box<dyn std::error::Error>> {
    let mut metas = Vec::new();
    for entry in fs::read_dir(dir)? {
        let entry = entry?;
        if entry.path().extension().and_then(|s| s.to_str()) == Some("json") {
            let meta = serde_json::from_reader(fs::File::open(entry.path())?)?;
            metas.push(meta);
        }
    }
    Ok(metas)
}

pub fn import_all(dir: PathBuf) -> (usize, impl Iterator<Item = SensorData> + 'static) {
    let mut metas = import_meta(&dir).unwrap();
    metas.sort_by_key(|meta| meta.scenario_start.clone());

    let count = metas.len();
    let iter = metas.into_iter().flat_map(move |meta| {
        let dir = dir.clone();

        [meta.sensor1_file, meta.sensor2_file]
            .into_iter()
            .enumerate()
            .flat_map(move |(i, file_name)| {
                let path = dir.join(&file_name);
                if path.exists() {
                    match import_sensor(&path) {
                        Ok(readings) => Some(SensorData {
                            sensor: i as u8 + 1,
                            readings,
                            defect: meta.defect,
                        }),
                        Err(e) => {
                            eprintln!("Failed to import {}: {}", file_name, e);
                            None
                        }
                    }
                } else {
                    eprintln!("File not found: {}", path.display());
                    None
                }
            })
    });

    (count * 2, iter)
}

pub fn import_sensor(path: &std::path::Path) -> Result<DataFrame, Box<dyn std::error::Error>> {
    let text = fs::read_to_string(path)?;
    let mut lines = text.lines();
    let header = lines.next().ok_or("CSV file is empty")?; // Get the first line as header
    let columns: Vec<&str> = header.split(',').collect();
    let mut timestamps = Vec::new();
    let mut features: IndexMap<String, Vec<f32>> = IndexMap::new();
    for column in columns.iter().skip(1) {
        features.insert(column.to_string(), Vec::new());
    }
    for line in lines {
        let values: Vec<&str> = line.split(',').collect();
        if values.len() < 2 {
            continue; // Skip lines that don't have enough data
        }

        // Parse the timestamp
        let unix_time_us = values[0];
        let timestamp = match unix_time_us.parse::<i64>() {
            Ok(ts) => {
                DateTime::<Utc>::from_timestamp(ts / 1_000_000, 1000 * (ts % 1_000_000) as u32)
            }
            Err(_) => {
                eprintln!("Failed to parse timestamp: {}", unix_time_us);
                continue; // Skip this line if timestamp parsing fails
            }
        };
        let Some(timestamp) = timestamp else {
            eprintln!("Invalid timestamp: {}", unix_time_us);
            continue; // Skip this line if timestamp is invalid
        };
        timestamps.push(timestamp);

        // Store the data in the features map
        for (i, column) in columns.iter().enumerate().skip(1) {
            if let Ok(value) = values[i].trim().replace(',', ".").parse::<f32>() {
                features.get_mut(*column).unwrap().push(value);
            } else {
                features.get_mut(*column).unwrap().push(f32::NAN);
            }
        }
    }

    Ok(DataFrame {
        timestamps,
        features,
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::{tempdir, NamedTempFile};

    #[test]
    fn import_simple_sensor_csv() {
        let csv = b"time_offset,x,y,z\n1000000,1,2,3\n2000000,4,5,6\n";
        let mut file = NamedTempFile::new().unwrap();
        std::io::Write::write_all(&mut file, csv).unwrap();

        let df = import_sensor(file.path()).unwrap();
        assert_eq!(df.len(), 2);
        assert_eq!(df.features["x"], vec![1.0, 4.0]);
    }

    #[test]
    fn import_meta_reads_json() {
        let dir = tempdir().unwrap();
        std::fs::write(
            dir.path().join("data.json"),
            r#"{
            "scenario": "s",
            "defect": false,
            "defect_kind": null,
            "batch_start": "b",
            "scenario_start": "s",
            "hostname": "h",
            "source": "src",
            "sensor1_file": "s1.csv",
            "sensor2_file": "s2.csv"
        }"#,
        )
        .unwrap();

        let metas = import_meta(dir.path()).unwrap();
        assert_eq!(metas.len(), 1);
        assert_eq!(metas[0].sensor1_file, "s1.csv");
    }
}
