use std::path::PathBuf;

use clap::{Parser, Subcommand};

#[derive(Parse<PERSON>, Debug)]
#[clap(author, version, about, long_about = None)]
pub struct Args {
    #[command(subcommand)]
    pub cmd: Cmd,
}

#[derive(Subcommand, Debug)]
#[clap(author, version, about, long_about = None)]
pub enum Cmd {
    Import(Import),
    Aggregate(Aggregate),
}

#[derive(Parser, Debug)]
#[clap(author, version, about, long_about = None)]
/// Importer for heat pump and sensor data
pub struct Import {
    /// Directory containing heat pump data files
    #[clap(short)]
    pub in_dir: PathBuf,
    /// Name of the dataset (directory under `../datasets`)
    pub dataset_name: String,
}

#[derive(Parser, Debug)]
#[clap(author, version, about, long_about = None)]
pub struct Aggregate {
    /// Path to sensor data
    pub merged: PathBuf,
    /// Output file
    pub out_file: PathBuf,
    /// Window size for aggregation
    pub window: String,
    /// Interval for aggregation
    pub interval: String,
}
