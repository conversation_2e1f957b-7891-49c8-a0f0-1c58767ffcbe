use std::{
    fs,
    path::{Path, PathBuf},
    process::Command,
    time::Instant,
};

use anyhow::{bail, Context, Result};
use chrono::TimeDelta;

use crate::{
    df::{self, DfWriter},
    hp_data::process_heatpump_data,
    merge, sensor_data,
};

fn get_repo_root() -> anyhow::Result<PathBuf> {
    let output = Command::new("git")
        .args(&["rev-parse", "--show-toplevel"])
        .output()?;

    if !output.status.success() {
        bail!("Not in a git repository");
    }

    let path = String::from_utf8(output.stdout)?.trim().to_string();

    Ok(PathBuf::from(path))
}

fn prepare_output_dir(dataset_name: &str, indent: &str) -> Result<(PathBuf, PathBuf)> {
    let data_dir = get_repo_root()
        .context("Failed to get repository root directory")?
        .join("data");
    let out_dir = data_dir.join("datasets").join(dataset_name);
    fs::create_dir_all(&out_dir)?;
    println!("{indent}Dataset output directory: {:?}\n", out_dir);
    Ok((data_dir, out_dir))
}

fn create_writers(out_dir: &Path) -> Result<[DfWriter; 2]> {
    let raw_writers = [
        DfWriter::parquet(out_dir.join("sensor1.parquet").as_path())?,
        DfWriter::parquet(out_dir.join("sensor2.parquet").as_path())?,
    ];

    Ok(raw_writers)
}

fn process_sensor(
    sensor_data: sensor_data::SensorData,
    idx: usize,
    total: usize,
    heatpump_data: &df::DataFrame,
    raw_writers: &mut [DfWriter; 2],
    indent: &str,
) -> Result<()> {
    let sensor_start = Instant::now();
    let level1 = format!("{indent}  ");
    println!(
        "{level1}Processing sensor {} ({}/{})",
        sensor_data.sensor,
        idx + 1,
        total
    );

    // Merge with heat pump data
    let merge_start = Instant::now();
    let mut df_sensor = sensor_data.readings;
    df_sensor.set_all("defect", sensor_data.defect as i32 as f32);
    df_sensor.set_all("sensor", (sensor_data.sensor - 1) as i32 as f32);
    let merged = merge::merge_asof_optimized(heatpump_data, &df_sensor, TimeDelta::seconds(2));
    println!(
        "{level1}  Merged {} sensor rows with {} heatpump rows => {} rows in {:.2?}",
        df_sensor.len(),
        heatpump_data.len(),
        merged.len(),
        merge_start.elapsed(),
    );

    // Write raw merged data

    let start_write = Instant::now();
    merged.to_writer(&mut raw_writers[(sensor_data.sensor - 1) as usize])?;
    println!(
        "{level1}  Raw CSV written in {:.2?} ({} rows)",
        start_write.elapsed(),
        merged.timestamps.len()
    );

    println!(
        "{indent}Finished sensor {} in {:.2?}\n",
        sensor_data.sensor,
        sensor_start.elapsed()
    );

    Ok(())
}

pub fn run(in_dir: PathBuf, dataset_name: String) -> Result<()> {
    let indent = "  ";

    let hp_dir = in_dir.join("measurements");
    let (_data_dir, out_dir) = prepare_output_dir(&dataset_name, indent)?;
    let heatpump_data = process_heatpump_data(&hp_dir, &out_dir, indent)?;

    let mut raw_writers = create_writers(&out_dir)?;

    let (sensor_count, sensors) = sensor_data::import_all(in_dir.join("readings"));
    println!(
        "{}Importing and processing sensor data (0/{})...",
        indent, sensor_count
    );
    for (idx, sensor_data) in sensors.into_iter().enumerate() {
        process_sensor(
            sensor_data,
            idx,
            sensor_count,
            &heatpump_data,
            &mut raw_writers,
            indent,
        )?;
    }
    let [raw0, raw1] = raw_writers;
    raw0.close()?;
    raw1.close()?;

    println!(
        "{}All sensor data processed and written successfully.",
        indent
    );

    Ok(())
}
