use std::time::Instant;

use anyhow::Result;
use chrono::TimeDelta;
use parquet::basic::Compression;

use crate::{
    cli::Aggregate,
    df::{self, DataFrame},
    features::{aggregate_ranges, extract_all_ranges},
    hp_data,
};

pub fn build_aggregate(args: Aggregate) -> Result<()> {
    let start_read = Instant::now();
    let merged = DataFrame::read_parquet(&args.merged)?;
    println!(
        "Read {} rows in {:.2?}",
        merged.timestamps.len(),
        start_read.elapsed()
    );

    let window = TimeDelta::seconds(args.window.parse()?);
    let interval = TimeDelta::seconds(args.interval.parse()?);

    let agg_df = aggregate_data(&merged, window, interval)?;
    agg_df.to_parquet(&args.out_file, Some(Compression::SNAPPY))?;

    Ok(())
}

pub fn aggregate_data(
    merged: &DataFrame,
    window: TimeDelta,
    interval: TimeDelta,
) -> Result<DataFrame> {
    let level1 = "  ";
    println!(
        "{level1}  Aggregating with window: {} and interval: {}",
        window.to_string(),
        interval.to_string()
    );
    let agg_start = Instant::now();
    let mut agg_df = df::DataFrame::empty();
    let ranges = merged.sliding_window_ranges(window, interval, TimeDelta::milliseconds(100));
    for &(_, end) in &ranges {
        agg_df.timestamps.push(merged.timestamps[end]);
    }
    aggregate_ranges(&merged, &ranges, hp_data::COLUMN_NAMES, "mean", &mut agg_df);
    aggregate_ranges(&merged, &ranges, &["sensor", "defect"], "mean", &mut agg_df);
    extract_all_ranges(&merged, &ranges, &["x", "y", "z"], &mut agg_df);
    println!(
        "{level1}  Aggregated {} rows in {:.2?}",
        agg_df.timestamps.len(),
        agg_start.elapsed()
    );

    Ok(agg_df)
}
