use std::time::Instant;

use anyhow::Result;
use clap::Parser;

mod aggregate;
mod cleaner;
mod cli;
mod df;
mod features;
mod fft;
mod hp_data;
mod import;
mod merge;
mod sensor_data;

use cli::{Args, Cmd};

fn main() -> Result<()> {
    let args = Args::parse();
    let overall_start = Instant::now();

    match args.cmd {
        Cmd::Import(args) => {
            import::run(args.in_dir, args.dataset_name)?;
        }
        Cmd::Aggregate(args) => {
            aggregate::build_aggregate(args)?;
        }
    }

    // println!("=== Starting data import ===");
    println!(
        "=== Finished all tasks in {:.2?} ===",
        overall_start.elapsed()
    );
    Ok(())
}
