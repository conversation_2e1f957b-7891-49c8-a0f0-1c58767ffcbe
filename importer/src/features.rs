use std::borrow::Cow;

use rayon::prelude::*;

use crate::{
    df::{DataFrame, DataFrameSlice},
    fft,
};

pub struct FeatureInput<'a> {
    pub start: usize,
    pub data: &'a [f32],
    pub sample_rate: f32,
    spectrum: Option<(Cow<'a, [f32]>, Cow<'a, [f32]>)>,
}

impl<'a> FeatureInput<'a> {
    pub fn from_df(df: DataFrameSlice<'a>, column: &str) -> Option<Self> {
        let data = df.get_column(column)?;
        let time_delta = df.timespan();
        let sample_rate = data.len() as f32 / time_delta.as_seconds_f32();
        Some(Self {
            start: df.start,
            data,
            sample_rate,
            spectrum: None,
        })
    }

    pub fn from_df_with_fft(df: DataFrameSlice<'a>, column: &str) -> Option<Self> {
        let data = df.get_column(column)?;
        let time_delta = df.timespan();
        let sample_rate = data.len() as f32 / time_delta.as_seconds_f32();
        let s = fft::compute_spectrum(data, sample_rate);
        let (freqs, mags) = s;

        Some(Self {
            start: df.start,
            data,
            sample_rate,
            spectrum: Some((Cow::Owned(freqs), Cow::Owned(mags))),
        })
    }

    pub fn spectrum(&'a self) -> (Cow<'a, [f32]>, Cow<'a, [f32]>) {
        if let Some(spectrum) = self.spectrum.as_ref() {
            return (Cow::Borrowed(&spectrum.0), Cow::Borrowed(&spectrum.1));
        }
        let s = fft::compute_spectrum(self.data, self.sample_rate);
        let (freqs, mags) = s;

        (Cow::Owned(freqs), Cow::Owned(mags))
    }
}

pub struct Feature {
    pub name: &'static str,
    pub function: fn(&FeatureInput) -> f32,
}

pub const FEATURES: &[Feature] = &[
    Feature {
        name: "mean",
        function: |input| input.data.iter().sum::<f32>() / input.data.len() as f32,
    },
    Feature {
        name: "stddev",
        function: |input| {
            let mean = input.data.iter().sum::<f32>() / input.data.len() as f32;
            (input.data.iter().map(|x| (x - mean).powi(2)).sum::<f32>() / input.data.len() as f32)
                .sqrt()
        },
    },
    Feature {
        name: "min",
        function: |input| {
            *input
                .data
                .iter()
                .min_by(|a, b| a.partial_cmp(b).unwrap())
                .unwrap()
        },
    },
    Feature {
        name: "max",
        function: |input| {
            *input
                .data
                .iter()
                .max_by(|a, b| a.partial_cmp(b).unwrap())
                .unwrap()
        },
    },
    Feature {
        name: "variance",
        function: |input| {
            let mean = input.data.iter().sum::<f32>() / input.data.len() as f32;
            input.data.iter().map(|x| (x - mean).powi(2)).sum::<f32>() / input.data.len() as f32
        },
    },
    Feature {
        name: "rms",
        function: |input| {
            let sum_sq: f32 = input.data.iter().map(|x| x.powi(2)).sum();
            (sum_sq / input.data.len() as f32).sqrt()
        },
    },
    Feature {
        name: "median",
        function: |input| {
            let mut v = input.data.to_vec();
            v.sort_by(|a, b| a.partial_cmp(b).unwrap());
            let n = v.len();
            if n % 2 == 0 {
                (v[n / 2 - 1] + v[n / 2]) / 2.0
            } else {
                v[n / 2]
            }
        },
    },
    Feature {
        name: "iqr",
        function: |input| {
            let mut v = input.data.to_vec();
            v.sort_by(|a, b| a.partial_cmp(b).unwrap());
            let n = v.len();
            let q1 = if n % 4 == 0 {
                (v[n / 4 - 1] + v[n / 4]) / 2.0
            } else {
                v[n / 4]
            };
            let q3 = if (3 * n) % 4 == 0 {
                (v[3 * n / 4 - 1] + v[3 * n / 4]) / 2.0
            } else {
                v[3 * n / 4]
            };
            q3 - q1
        },
    },
    Feature {
        name: "skewness",
        function: |input| {
            let n = input.data.len() as f32;
            let mean = input.data.iter().sum::<f32>() / n;
            let m2 = input.data.iter().map(|x| (x - mean).powi(2)).sum::<f32>() / n;
            let std = m2.sqrt();
            if std == 0.0 {
                0.0
            } else {
                let m3 = input.data.iter().map(|x| (x - mean).powi(3)).sum::<f32>() / n;
                m3 / (std.powi(3))
            }
        },
    },
    Feature {
        name: "kurtosis",
        function: |input| {
            let n = input.data.len() as f32;
            let mean = input.data.iter().sum::<f32>() / n;
            let m2 = input.data.iter().map(|x| (x - mean).powi(2)).sum::<f32>() / n;
            let std = m2.sqrt();
            if std == 0.0 {
                0.0
            } else {
                let m4 = input.data.iter().map(|x| (x - mean).powi(4)).sum::<f32>() / n;
                m4 / (std.powi(4)) - 3.0
            }
        },
    },
    Feature {
        name: "energy",
        function: |input| input.data.iter().map(|x| x.powi(2)).sum::<f32>(),
    },
    Feature {
        name: "zcs",
        function: |input| {
            input.data.windows(2).filter(|w| w[0] * w[1] < 0.0).count() as f32
                / (input.data.len() as f32 - 1.0)
        },
    },
    Feature {
        name: "peak_count",
        function: |input| {
            let mut count = 0;
            for i in 1..input.data.len() - 1 {
                if input.data[i] > input.data[i - 1] && input.data[i] > input.data[i + 1] {
                    count += 1;
                }
            }
            count as f32
        },
    },
    Feature {
        name: "centroid_0_120",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_centroid(&freqs, &mags, 0.0, 120.0)
        },
    },
    Feature {
        name: "centroid_120_400",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_centroid(&freqs, &mags, 120.0, 400.0)
        },
    },
    Feature {
        name: "centroid_400_800",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_centroid(&freqs, &mags, 400.0, 800.0)
        },
    },
    Feature {
        name: "centroid_800_1200",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_centroid(&freqs, &mags, 800.0, 1200.0)
        },
    },
    Feature {
        name: "centroid_1200_1600",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_centroid(&freqs, &mags, 1200.0, 1600.0)
        },
    },
    // Peak Frequency per Band
    Feature {
        name: "peak_0_120",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_peak_frequency(&freqs, &mags, 0.0, 120.0)
        },
    },
    Feature {
        name: "peak_120_400",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_peak_frequency(&freqs, &mags, 120.0, 400.0)
        },
    },
    Feature {
        name: "peak_400_800",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_peak_frequency(&freqs, &mags, 400.0, 800.0)
        },
    },
    Feature {
        name: "peak_800_1200",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_peak_frequency(&freqs, &mags, 800.0, 1200.0)
        },
    },
    Feature {
        name: "peak_1200_1600",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_peak_frequency(&freqs, &mags, 1200.0, 1600.0)
        },
    },
    // Spectral Energy per Band
    Feature {
        name: "energy_0_120",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_energy(&freqs, &mags, 0.0, 120.0)
        },
    },
    Feature {
        name: "energy_120_400",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_energy(&freqs, &mags, 120.0, 400.0)
        },
    },
    Feature {
        name: "energy_400_800",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_energy(&freqs, &mags, 400.0, 800.0)
        },
    },
    Feature {
        name: "energy_800_1200",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_energy(&freqs, &mags, 800.0, 1200.0)
        },
    },
    Feature {
        name: "energy_1200_1600",
        function: |input| {
            let (freqs, mags) = input.spectrum();
            fft::band_energy(&freqs, &mags, 1200.0, 1600.0)
        },
    },
];

pub fn aggregate_ranges(
    df: &DataFrame,
    windows: &[(usize, usize)],
    columns: impl IntoIterator<Item = impl AsRef<str>>,
    func: &str,
    out: &mut DataFrame,
) {
    let feature = match FEATURES.iter().find(|f| f.name == func) {
        Some(f) => f.function,
        None => {
            eprintln!("Feature '{}' not found", func);
            return;
        }
    };

    let cols: Vec<String> = columns
        .into_iter()
        .map(|c| c.as_ref().to_string())
        .collect();

    for column in cols {
        if !df.features.contains_key(&column) {
            eprintln!("Column '{}' not found", column);
            continue;
        }
        let results: Vec<f32> = windows
            .iter()
            .map(|&(start, end)| {
                let df = df.slice(start..=end);
                let input = FeatureInput::from_df(df, &column).unwrap();

                (feature)(&input)
            })
            .collect();

        out.features
            .entry(column)
            .or_insert_with(Vec::new)
            .extend(results);
    }
}

pub fn extract_all_ranges(
    df: &DataFrame,
    windows: &[(usize, usize)],
    columns: &[&str],
    out: &mut DataFrame,
) {
    for &column in columns {
        let inputs = windows
            .iter()
            .map(|&(start, end)| {
                let df_slice = df.slice(start..=end);
                FeatureInput::from_df_with_fft(df_slice, column).unwrap()
            })
            .collect::<Vec<_>>();

        let results = FEATURES
            .par_iter()
            .map(|feature| {
                let mut results: Vec<(usize, f32)> = inputs
                    .par_iter()
                    .map(|input| (input.start, (feature.function)(input)))
                    .collect();
                results.sort_by_key(|&(start, _)| start);

                (format!("{}_{}", column, feature.name), results)
            })
            .collect::<Vec<_>>();

        for (key, values) in results {
            out.features
                .insert(key, values.into_iter().map(|(_, v)| v).collect());
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::{TimeZone, Utc};
    use indexmap::IndexMap;

    fn simple_df() -> DataFrame {
        let base = Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap();
        DataFrame {
            timestamps: vec![
                base,
                base + chrono::Duration::seconds(1),
                base + chrono::Duration::seconds(2),
            ],
            features: IndexMap::from([(String::from("x"), vec![1.0, 3.0, 5.0])]),
        }
    }

    #[test]
    fn aggregate_ranges_two_windows() {
        let df = simple_df();
        let windows = vec![(0, 1), (1, 2)];
        let mut out = DataFrame::empty();
        aggregate_ranges(&df, &windows, ["x"], "mean", &mut out);
        assert_eq!(out.features["x"], vec![2.0, 4.0]);
    }
}
