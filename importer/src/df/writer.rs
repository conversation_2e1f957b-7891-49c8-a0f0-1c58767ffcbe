use std::{
    collections::HashMap,
    fs::File,
    io::{BufWriter, Write as _},
    path::{Path, PathBuf},
};

use anyhow::Context;

use crate::df::DataFrame;

impl DataFrame {
    pub fn to_writer(&self, writer: &mut DfWriter) -> anyhow::Result<()> {
        match writer {
            DfWriter::Csv(csv_writer) => csv_writer.write(self).context("Failed to write CSV data"),
            DfWriter::<PERSON><PERSON><PERSON>(pq_writer) => pq_writer
                .write(self)
                .context("Failed to write Parquet data"),
            DfWriter::DuckDb(duckdb_writer) => duckdb_writer
                .write(self)
                .context("Failed to write DuckDB data"),
        }
    }
}

pub enum DfWriter {
    Csv(CsvWriter),
    Parquet(PqWriter),
    DuckDb(DuckDbWriter),
}

impl DfWriter {
    pub fn csv(path: &Path) -> anyhow::Result<Self> {
        let file = File::create(path).unwrap();
        let writer = BufWriter::new(file);
        Ok(DfWriter::Csv(CsvWriter {
            writer,
            header_written: false,
        }))
    }

    pub fn parquet(path: &Path) -> anyhow::Result<Self> {
        let writer = PqWriter {
            path: path.to_path_buf(),
            writer: None,
        };
        Ok(DfWriter::Parquet(writer))
    }

    pub fn duckdb(
        path: &Path,
        table_name: String,
        overrides: HashMap<&'static str, &'static str>,
    ) -> anyhow::Result<Self> {
        Ok(DfWriter::DuckDb(DuckDbWriter {
            con: duckdb::Connection::open(path).context("Failed to open DuckDB connection")?,
            table_name,
            overrides,
        }))
    }

    pub fn close(self) -> anyhow::Result<()> {
        match self {
            DfWriter::Csv(mut csv_writer) => {
                if csv_writer.header_written {
                    csv_writer.writer.flush()?;
                }
                Ok(())
            }
            DfWriter::Parquet(pq_writer) => {
                if let Some(writer) = pq_writer.writer {
                    writer.writer.close()?;
                }
                Ok(())
            }
            DfWriter::DuckDb(duckdb_writer) => duckdb_writer
                .con
                .close()
                .map_err(|x| x.1)
                .context("Failed to close DuckDB connection"),
        }
    }
}

pub struct CsvWriter {
    writer: BufWriter<std::fs::File>,
    header_written: bool,
}

impl CsvWriter {
    pub fn write(&mut self, df: &DataFrame) -> anyhow::Result<()> {
        if !self.header_written {
            df.write_csv_header(&mut self.writer)?;
            self.header_written = true;
        }
        df.write_csv(&mut self.writer)?;
        Ok(())
    }
}

pub struct PqWriter {
    path: PathBuf,
    writer: Option<super::ParquetWriter>,
}

impl PqWriter {
    pub fn write(&mut self, df: &DataFrame) -> anyhow::Result<()> {
        if self.writer.is_none() {
            self.writer = Some(df.parquet_writer(&self.path, None)?);
        }
        if let Some(writer) = &mut self.writer {
            df.to_parquet_writer(writer)
                .context("Failed to write Parquet data")?;
        }
        Ok(())
    }
}

pub struct DuckDbWriter {
    con: duckdb::Connection,
    table_name: String,
    overrides: HashMap<&'static str, &'static str>,
}

impl DuckDbWriter {
    pub fn write(&mut self, df: &DataFrame) -> anyhow::Result<()> {
        df.to_duckdb(&mut self.con, &self.table_name, Some(&self.overrides))
            .context("Failed to write DuckDB data")?;
        Ok(())
    }
}
