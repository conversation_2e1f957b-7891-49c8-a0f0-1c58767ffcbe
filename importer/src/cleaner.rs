use chrono::{DateTime, Utc};
use eframe::egui;
use egui::Color32;
use egui_plot::{Line, Plot, PlotPoints, PlotResponse, PlotUi, VLine};
use indexmap::IndexMap;
use std::ops::RangeInclusive;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct DataFrame {
    pub timestamps: Vec<DateTime<Utc>>,
    pub features: IndexMap<String, Vec<f32>>,
}

#[derive(Debug, <PERSON>lone)]
pub struct TimeRange {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone)]
struct DataSegment {
    start_idx: usize,
    end_idx: usize,
    start_time: DateTime<Utc>,
    end_time: DateTime<Utc>,
}

struct DataFrameEditor {
    dataframe: DataFrame,
    segments: Vec<DataSegment>,
    selected_ranges: Vec<TimeRange>,
    current_feature: String,
    plot_selection: Option<TimeRange>,
    editing_range: Option<usize>,
    confirmed: bool,
    cancelled: bool,
}

impl DataFrameEditor {
    fn new(dataframe: DataFrame) -> Self {
        let current_feature = dataframe
            .features
            .keys()
            .next()
            .cloned()
            .unwrap_or_default();
        let segments = Self::create_segments(&dataframe);

        Self {
            dataframe,
            segments,
            selected_ranges: Vec::new(),
            current_feature,
            plot_selection: None,
            editing_range: None,
            confirmed: false,
            cancelled: false,
        }
    }

    fn create_segments(dataframe: &DataFrame) -> Vec<DataSegment> {
        let mut segments = Vec::new();

        if dataframe.timestamps.is_empty() {
            return segments;
        }

        let max_gap = chrono::Duration::seconds(5);
        let mut segment_start = 0;

        for i in 1..dataframe.timestamps.len() {
            let gap = dataframe.timestamps[i] - dataframe.timestamps[i - 1];
            if gap > max_gap {
                // End current segment
                segments.push(DataSegment {
                    start_idx: segment_start,
                    end_idx: i - 1,
                    start_time: dataframe.timestamps[segment_start],
                    end_time: dataframe.timestamps[i - 1],
                });
                segment_start = i;
            }
        }

        // Add final segment
        if segment_start < dataframe.timestamps.len() {
            segments.push(DataSegment {
                start_idx: segment_start,
                end_idx: dataframe.timestamps.len() - 1,
                start_time: dataframe.timestamps[segment_start],
                end_time: dataframe.timestamps[dataframe.timestamps.len() - 1],
            });
        }

        segments
    }

    fn timestamp_to_plot_value(timestamp: DateTime<Utc>) -> f64 {
        timestamp.timestamp() as f64
    }

    fn plot_value_to_timestamp(value: f64) -> DateTime<Utc> {
        DateTime::from_timestamp(value as i64, 0).unwrap_or_default()
    }

    fn get_plot_points(&self, feature_name: &str) -> Vec<PlotPoints> {
        let mut segment_points = Vec::new();

        if let Some(feature_data) = self.dataframe.features.get(feature_name) {
            for segment in &self.segments {
                let points: PlotPoints = (segment.start_idx..=segment.end_idx)
                    .filter_map(|i| {
                        if i < self.dataframe.timestamps.len() && i < feature_data.len() {
                            Some([
                                Self::timestamp_to_plot_value(self.dataframe.timestamps[i]),
                                feature_data[i] as f64,
                            ])
                        } else {
                            None
                        }
                    })
                    .collect();

                if !points.is_empty() {
                    segment_points.push(points);
                }
            }
        }

        segment_points
    }
}

impl eframe::App for DataFrameEditor {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        if self.confirmed || self.cancelled {
            ctx.send_viewport_cmd(egui::ViewportCommand::Close);
            return;
        }

        egui::CentralPanel::default().show(ctx, |ui| {
            ui.horizontal(|ui| {
                ui.label("Feature:");
                egui::ComboBox::from_id_source("feature_selector")
                    .selected_text(&self.current_feature)
                    .show_ui(ui, |ui| {
                        for feature_name in self.dataframe.features.keys() {
                            ui.selectable_value(
                                &mut self.current_feature,
                                feature_name.clone(),
                                feature_name,
                            );
                        }
                    });

                ui.separator();

                if ui.button("Confirm Selection").clicked() {
                    self.confirmed = true;
                }

                if ui.button("Cancel").clicked() {
                    self.cancelled = true;
                }
            });

            ui.separator();

            ui.horizontal(|ui| {
                // Left panel - Plot
                ui.vertical(|ui| {
                    ui.set_width(ui.available_width() * 0.7);

                    let plot_points = self.get_plot_points(&self.current_feature);

                    let plot_response = Plot::new("dataframe_plot")
                        .height(400.0)
                        .show_x(true)
                        .show_y(true)
                        .allow_zoom(true)
                        .allow_drag(true)
                        .show(ui, |plot_ui| {
                            // Draw data segments
                            for (i, points) in plot_points.iter().enumerate() {
                                let line = Line::new(points.clone())
                                    .color(Color32::from_rgb(100, 150, 250))
                                    .width(1.5);
                                plot_ui.line(line);
                            }

                            // Draw segment boundaries
                            for segment in &self.segments {
                                if segment.start_idx > 0 {
                                    let vline = VLine::new(Self::timestamp_to_plot_value(
                                        segment.start_time,
                                    ))
                                    .color(Color32::from_gray(128))
                                    .width(1.0);
                                    plot_ui.vline(vline);
                                }
                            }

                            // Draw selected ranges
                            for (i, range) in self.selected_ranges.iter().enumerate() {
                                let start_x = Self::timestamp_to_plot_value(range.start);
                                let end_x = Self::timestamp_to_plot_value(range.end);

                                let color = if Some(i) == self.editing_range {
                                    Color32::from_rgba_unmultiplied(255, 255, 0, 60)
                                } else {
                                    Color32::from_rgba_unmultiplied(255, 100, 100, 40)
                                };

                                // Draw range highlight (simplified - in real implementation would need custom shapes)
                                let start_vline = VLine::new(start_x)
                                    .color(Color32::from_rgb(255, 100, 100))
                                    .width(2.0);
                                let end_vline = VLine::new(end_x)
                                    .color(Color32::from_rgb(255, 100, 100))
                                    .width(2.0);

                                plot_ui.vline(start_vline);
                                plot_ui.vline(end_vline);
                            }
                        });

                    // Handle plot interactions
                    if let Some(hover_pos) = plot_response.response.hover_pos() {
                        if plot_response.response.drag_started() {
                            if let Some(plot_pos) =
                                plot_response.transform.value_from_position(hover_pos)
                            {
                                self.plot_selection = Some(TimeRange {
                                    start: Self::plot_value_to_timestamp(plot_pos.x),
                                    end: Self::plot_value_to_timestamp(plot_pos.x),
                                });
                            }
                        }

                        if plot_response.response.dragged() {
                            if let (Some(ref mut selection), Some(plot_pos)) = (
                                &mut self.plot_selection,
                                plot_response.transform.value_from_position(hover_pos),
                            ) {
                                selection.end = Self::plot_value_to_timestamp(plot_pos.x);
                            }
                        }

                        if plot_response.response.drag_stopped() {
                            if let Some(selection) = self.plot_selection.take() {
                                if selection.start != selection.end {
                                    let mut range = selection;
                                    if range.start > range.end {
                                        std::mem::swap(&mut range.start, &mut range.end);
                                    }
                                    self.selected_ranges.push(range);
                                    self.editing_range = Some(self.selected_ranges.len() - 1);
                                }
                            }
                        }
                    }
                });

                ui.separator();

                // Right panel - Range list
                ui.vertical(|ui| {
                    ui.set_width(ui.available_width());
                    ui.heading("Selected Time Ranges");

                    egui::ScrollArea::vertical().show(ui, |ui| {
                        let mut to_remove = None;

                        for (i, range) in self.selected_ranges.iter_mut().enumerate() {
                            ui.group(|ui| {
                                ui.horizontal(|ui| {
                                    let is_editing = Some(i) == self.editing_range;
                                    let mut selected = is_editing;

                                    if ui.checkbox(&mut selected, "").changed() {
                                        self.editing_range = if selected { Some(i) } else { None };
                                    }

                                    ui.vertical(|ui| {
                                        ui.label(format!("Range {}", i + 1));
                                        ui.label(format!(
                                            "Start: {}",
                                            range.start.format("%Y-%m-%d %H:%M:%S")
                                        ));
                                        ui.label(format!(
                                            "End: {}",
                                            range.end.format("%Y-%m-%d %H:%M:%S")
                                        ));
                                        ui.label(format!(
                                            "Duration: {:.2}s",
                                            (range.end - range.start).num_milliseconds() as f64
                                                / 1000.0
                                        ));
                                    });

                                    if ui.button("Delete").clicked() {
                                        to_remove = Some(i);
                                    }
                                });

                                if Some(i) == self.editing_range {
                                    ui.separator();
                                    ui.label("Drag handles in plot to adjust range");
                                }
                            });
                        }

                        if let Some(idx) = to_remove {
                            self.selected_ranges.remove(idx);
                            if Some(idx) == self.editing_range {
                                self.editing_range = None;
                            } else if let Some(editing_idx) = self.editing_range {
                                if editing_idx > idx {
                                    self.editing_range = Some(editing_idx - 1);
                                }
                            }
                        }
                    });

                    ui.separator();
                    ui.label(format!("Total ranges: {}", self.selected_ranges.len()));
                });
            });
        });
    }
}

pub fn edit_dataframe_ranges(dataframe: DataFrame) -> Option<Vec<TimeRange>> {
    if dataframe.timestamps.is_empty() || dataframe.features.is_empty() {
        return Some(Vec::new());
    }

    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([1000.0, 600.0])
            .with_title("DataFrame Range Editor"),
        ..Default::default()
    };

    let mut app = DataFrameEditor::new(dataframe);
    let mut result = None;

    eframe::run_native(
        "DataFrame Range Editor",
        options,
        Box::new(|_cc| Ok(Box::new(app))),
    )
    .unwrap();

    // Note: In a real implementation, you'd need to handle the app lifecycle differently
    // to actually return the selected ranges. This is a simplified version.
    // You might use channels or other mechanisms to communicate the result.

    if app.confirmed {
        Some(app.selected_ranges)
    } else {
        None
    }
}

// Example usage and test data
#[cfg(test)]
mod tests {
    use super::*;
    use chrono::{TimeZone, Utc};

    fn create_test_dataframe() -> DataFrame {
        let mut features = IndexMap::new();

        // Create some test data with gaps
        let base_time = Utc.with_ymd_and_hms(2024, 1, 1, 12, 0, 0).unwrap();
        let mut timestamps = Vec::new();
        let mut temp_data = Vec::new();
        let mut pressure_data = Vec::new();

        // First segment
        for i in 0..100 {
            timestamps.push(base_time + chrono::Duration::seconds(i));
            temp_data.push(20.0 + (i as f32 * 0.1).sin() * 5.0);
            pressure_data.push(1013.25 + (i as f32 * 0.05).cos() * 10.0);
        }

        // Gap of 10 seconds
        // Second segment
        for i in 0..80 {
            timestamps.push(base_time + chrono::Duration::seconds(110 + i));
            temp_data.push(22.0 + (i as f32 * 0.08).sin() * 3.0);
            pressure_data.push(1015.0 + (i as f32 * 0.06).cos() * 8.0);
        }

        features.insert("Temperature".to_string(), temp_data);
        features.insert("Pressure".to_string(), pressure_data);

        DataFrame {
            timestamps,
            features,
        }
    }

    #[test]
    fn test_segment_creation() {
        let df = create_test_dataframe();
        let segments = DataFrameEditor::create_segments(&df);

        assert_eq!(segments.len(), 2);
        assert_eq!(segments[0].start_idx, 0);
        assert_eq!(segments[0].end_idx, 99);
        assert_eq!(segments[1].start_idx, 100);
        assert_eq!(segments[1].end_idx, 179);
    }
}
