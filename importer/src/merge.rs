use chrono::Duration;
use indexmap::IndexMap;

use crate::df::DataFrame;

pub fn merge_asof_optimized(
    heat_pump: &DataFrame,
    sensor: &DataFrame,
    max_diff: Duration,
) -> DataFrame {
    let mut merged_timestamps = Vec::new();
    let mut merged_features: IndexMap<String, Vec<f32>> = IndexMap::new();

    // Prepare all keys
    for key in heat_pump.features.keys().chain(sensor.features.keys()) {
        merged_features
            .entry(key.to_string())
            .or_insert_with(Vec::new);
    }

    let mut hp_idx = 0;

    // TODO: something is not working with timestamps here, they have huge gaps

    for (sensor_idx, &sensor_ts) in sensor.timestamps.iter().enumerate() {
        // Move forward in heat pump timestamps while they are <= sensor_ts
        while hp_idx + 1 < heat_pump.timestamps.len()
            && heat_pump.timestamps[hp_idx + 1] <= sensor_ts
        {
            hp_idx += 1;
        }

        // Check if current heat pump timestamp is within max_diff
        let diff = sensor_ts - heat_pump.timestamps[hp_idx];
        if diff.abs() <= max_diff {
            // Add timestamp
            merged_timestamps.push(sensor_ts);

            // Add heat pump features
            for (key, values) in &heat_pump.features {
                merged_features.get_mut(key).unwrap().push(values[hp_idx]);
            }

            // Add sensor features
            for (key, values) in &sensor.features {
                merged_features
                    .get_mut(key)
                    .unwrap()
                    .push(values[sensor_idx]);
            }
        }
    }

    DataFrame {
        timestamps: merged_timestamps,
        features: merged_features,
    }
}

#[cfg(test)]
mod tests {
    use chrono::{TimeZone, Utc};

    use super::*;
    use crate::df::DataFrame;

    #[test]
    fn test_merge_asof_optimized() {
        let heat_pump = DataFrame {
            timestamps: vec![
                chrono::Utc.with_ymd_and_hms(2023, 10, 1, 12, 0, 0).unwrap(),
                chrono::Utc.with_ymd_and_hms(2023, 10, 1, 12, 0, 5).unwrap(),
            ],
            features: IndexMap::from([("temp".to_string(), vec![20.0, 21.0])]),
        };

        let sensor = DataFrame {
            timestamps: vec![
                Utc.with_ymd_and_hms(2023, 10, 1, 10, 0, 0).unwrap(),
                Utc.with_ymd_and_hms(2023, 10, 1, 12, 0, 0).unwrap(),
                Utc.with_ymd_and_hms(2023, 10, 1, 12, 0, 1).unwrap(),
                Utc.with_ymd_and_hms(2023, 10, 1, 12, 0, 2).unwrap(),
                Utc.with_ymd_and_hms(2023, 10, 1, 12, 0, 3).unwrap(),
                Utc.with_ymd_and_hms(2023, 10, 1, 12, 0, 4).unwrap(),
                Utc.with_ymd_and_hms(2023, 10, 1, 12, 0, 5).unwrap(),
                Utc.with_ymd_and_hms(2023, 10, 1, 12, 0, 6).unwrap(),
                Utc.with_ymd_and_hms(2023, 10, 1, 22, 0, 6).unwrap(),
            ],
            features: IndexMap::from([(
                "humidity".to_string(),
                vec![0.0, 30.0, 35.0, 40.0, 45.0, 50.0, 55.0, 60.0, 0.0],
            )]),
        };

        let merged = merge_asof_optimized(&heat_pump, &sensor, Duration::seconds(60));
        // merged.print_head(10);

        assert_eq!(merged.timestamps.len(), 7);
        assert_eq!(
            merged.features["temp"],
            vec![20.0, 20.0, 20.0, 20.0, 20.0, 21.0, 21.0]
        );
        assert_eq!(
            merged.features["humidity"],
            vec![30.0, 35.0, 40.0, 45.0, 50.0, 55.0, 60.0]
        );
    }
}
