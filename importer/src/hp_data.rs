use std::{fs, path::Path, time::Instant};

use anyhow::{Context, Result};
use chrono::{TimeZone, Utc};
use encoding_rs::WINDOWS_1252;
use indexmap::IndexMap;

use crate::df::{self, DataFrame, DfWriter};

pub const COLUMN_NAMES: &[&str] = &[
    "flow_brine",
    "flow_heat",
    "HP",
    "LP",
    "T_discharge",
    "T_suction",
    "T_liquid",
    "EWT",
    "LWT",
    "brine_out",
    "brine_in",
    "sig_comp",
    "sig_EEV1",
    "QS0675.12c",
    "HK",
    "HK_C",
    "HK_Rho",
    "bHP",
    "bLP",
    "power_A(6)",
    "voltage_A(6)",
    "current1_A(6)",
    "current2_A(6)",
    "current3_A(6)",
    "sh",
    "DSH",
    "VD_rps",
    "EEV1",
    "spread_heat",
    "DFL_5K",
    "Atmosph_press",
    "valve_brine",
    "valve_bypass",
    "pump_brine",
    "pump_hc",
    "Analog2",
];

pub fn process_heatpump_data(hp_dir: &Path, out_dir: &Path, indent: &str) -> Result<df::DataFrame> {
    let mut data = df::DataFrame::empty();
    let hp_files: Vec<_> = fs::read_dir(hp_dir)?
        .filter_map(|e| e.ok())
        .filter(|e| e.path().extension().and_then(|s| s.to_str()) == Some("csv"))
        .collect();

    println!("{indent}Importing heat pump CSVs (0/{})...", hp_files.len());
    for (i, entry) in hp_files.iter().enumerate() {
        let file_start = Instant::now();
        let path = entry.path();
        println!(
            "{indent}  [{}] Loading {:?}",
            i + 1,
            path.file_name().unwrap()
        );
        let mut hp = import_heatpump(&path)?;
        let was_ramp_up: Vec<usize> = hp
            .features
            .get("Analog2")
            .map(|v| {
                v.iter()
                    .filter(|&&x| x > 0.0)
                    .enumerate()
                    .map(|(i, _)| i)
                    .collect()
            })
            .unwrap_or_default();
        hp.remove_rows(&was_ramp_up);
        println!("{indent}    Removed {} ramp-up rows", was_ramp_up.len());
        hp.features.shift_remove("Analog2");

        // let was_bad_sh: Vec<usize> = hp
        //     .features
        //     .get("sh")
        //     .map(|v| {
        //         v.iter()
        //             .enumerate()
        //             .filter(|(_, &x)| x < 4.5 || x > 9.5)
        //             .map(|(i, _)| i)
        //             .collect()
        //     })
        //     .unwrap_or_default();
        // hp.remove_rows(&was_bad_sh);
        // println!("{indent}    Removed {} bad 'sh' rows", was_bad_sh.len());

        data.append(&hp);
        println!(
            "{indent}    Imported {} rows in {:.2?}",
            hp.timestamps.len(),
            file_start.elapsed()
        );
    }

    println!("{indent}Total heat pump rows: {}\n", data.timestamps.len());
    let mut hp_writer = DfWriter::parquet(out_dir.join("heatpump.parquet").as_path())?;
    let start_time = Instant::now();
    data.to_writer(&mut hp_writer)?;
    println!(
        "{}Heat pump data written in {:.2?} ({} rows)",
        indent,
        start_time.elapsed(),
        data.timestamps.len()
    );
    hp_writer.close()?;

    Ok(data)
}

pub fn import_heatpump(hp_csv: &Path) -> Result<DataFrame> {
    // Column names expected in each file (the first is the timestamp).
    // let time_col = "Time";
    // Time;flow_brine;flow_heat;HP;LP;T_discharge;T_suction;T_liquid;EWT;LWT;
    // brine_out;brine_in;sig_comp;sig_EEV1;QS0675.12c;HK;HK_C;HK_Rho;bHP;bLP;
    // power_A(6);voltage_A(6);current1_A(6);current2_A(6);current3_A(6);sh;DSH;
    // VD_rps;EEV1;spread_heat;DFL_5K;Atmosph_press;valve_brine;valve_bypass;
    // pump_brine;pump_hc;Analog1;Analog2;Analog3;Analog4;Analog5;Analog6;
    let columns = COLUMN_NAMES;

    let split_char = ';';

    let bytes = std::fs::read(hp_csv).context("Failed to read heat pump CSV file")?;
    let text = WINDOWS_1252.decode(&bytes).0.into_owned();
    let lines = text.lines();
    let mut lines = lines.skip(1);
    let header: Vec<&str> = lines
        .next()
        .context("CSV file is empty")?
        .split(split_char)
        .collect();

    // println!("Header: {:?}", header);

    let lines = lines.skip(2);
    let column_ids = header
        .iter()
        .enumerate()
        .map(|(i, &col)| (col.to_string(), i as u32))
        .collect::<IndexMap<_, _>>();

    // println!("Column IDs: {:?}", column_ids);

    let mut timestamps = Vec::new();
    let mut features = IndexMap::new();

    for line in lines {
        let values: Vec<&str> = line.split(split_char).collect();

        // println!("Values: {:?}", values);

        // Parse the timestamp
        let timestamp_str = values[0];
        let timestamp =
            parse_european_datetime(timestamp_str).context("Failed to parse timestamp")?;

        timestamps.push(timestamp);

        // Store the data in a HashMap
        for col in columns.iter() {
            let Some(&col_id) = column_ids.get(*col) else {
                features
                    .entry(col.to_string())
                    .or_insert_with(Vec::new)
                    .push(0.0);
                continue;
            };
            let value = values
                .get(col_id as usize)
                .and_then(|v| v.trim().replace(',', ".").parse::<f32>().ok())
                .unwrap_or(f32::NAN);
            features
                .entry(col.to_string())
                .or_insert_with(Vec::new)
                .push(value);
        }
    }

    Ok(DataFrame {
        timestamps,
        features,
    })
}

fn parse_european_datetime(s: &str) -> Result<chrono::DateTime<Utc>> {
    // Parse European date format (DD.MM.YYYY HH:MM:SS)
    let dt = chrono::NaiveDateTime::parse_from_str(s, "%d.%m.%Y %H:%M:%S")
        .or_else(|_| chrono::NaiveDateTime::parse_from_str(s, "%d/%m/%Y %H:%M:%S"))
        .context("Failed to parse datetime")?;

    // Convert from Europe/Berlin to UTC
    let berlin_tz = chrono_tz::Europe::Berlin;
    let berlin_dt = berlin_tz
        .from_local_datetime(&dt)
        .single()
        .context("Ambiguous local time")?;

    Ok(berlin_dt.with_timezone(&Utc))
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::{TimeZone, Utc};
    use tempfile::NamedTempFile;

    #[test]
    fn import_small_heatpump_file() {
        const CSV: &str = "header1\nTime;flow_brine;flow_heat;HP;LP;T_discharge;T_suction;T_liquid;EWT;LWT;brine_out;brine_in\n;unit\n;channel\n05.06.2025 14:13:01;1,0;2,0;3,0;4,0;5,0;6,0;7,0;8,0;9,0;10,0;11,0\n05.06.2025 14:13:02;11,0;12,0;13,0;14,0;15,0;16,0;17,0;18,0;19,0;20,0;21,0\n";
        let mut file = NamedTempFile::new().unwrap();
        std::io::Write::write_all(&mut file, CSV.as_bytes()).unwrap();

        let df = import_heatpump(file.path()).unwrap();
        assert_eq!(df.len(), 2);
        assert!(df.features.contains_key("flow_brine"));

        let expected_ts = chrono_tz::Europe::Berlin
            .with_ymd_and_hms(2025, 6, 5, 14, 13, 01)
            .unwrap()
            .with_timezone(&Utc);
        assert_eq!(df.timestamps[0], expected_ts);
    }
}
