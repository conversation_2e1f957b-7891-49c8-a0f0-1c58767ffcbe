#![allow(dead_code)]

use std::{
    collections::HashMap,
    fs::File,
    io::{BufWriter, Write},
    path::Path,
    sync::Arc,
};

use anyhow::Result;
use arrow::{
    array::{ArrayRef, TimestampNanosecondArray},
    datatypes::{DataType, Field, Schema, TimeUnit},
};
use chrono::{DateTime, Duration, TimeDelta, Utc};
use duckdb::{arrow::array::RecordBatch, Connection};
use indexmap::IndexMap;
use parquet::{
    arrow::ArrowWriter,
    errors::ParquetError,
    file::{properties::WriterProperties, reader::SerializedFileReader},
};

pub use self::writer::DfWriter;

mod writer;

#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct DataFrame {
    pub timestamps: Vec<DateTime<Utc>>,
    pub features: IndexMap<String, Vec<f32>>,
}

impl DataFrame {
    pub fn empty() -> Self {
        DataFrame {
            timestamps: Vec::new(),
            features: IndexMap::new(),
        }
    }

    pub fn read_parquet<P: AsRef<Path>>(path: P) -> Result<Self, ParquetError> {
        use parquet::file::reader::FileReader;
        use parquet::record::*;

        // 1) Open the file and reader
        let file = File::open(path)?;
        let reader = SerializedFileReader::new(file)?;
        let mut rows = reader.get_row_iter(None)?;

        // 2) Prepare an empty DataFrame
        let mut df = DataFrame::default();

        // 3) Consume all rows
        while let Some(row) = rows.next() {
            let row = row?;
            // Each `group` is a Vec<(column_name, Field)>
            for (name, field) in row.into_columns() {
                match field {
                    // Timestamp in millis → chrono::DateTime<Utc>
                    Field::TimestampMillis(ms) if name == "timestamp" => {
                        let secs = ms / 1_000;
                        let nsec = ((ms % 1_000) * 1_000_000) as u32;
                        df.timestamps
                            .push(DateTime::<Utc>::from_timestamp(secs, nsec).unwrap());
                    }
                    Field::Long(nsec_since_epoch) if name == "timestamp" => {
                        df.timestamps.push(
                            DateTime::<Utc>::from_timestamp(
                                nsec_since_epoch / 1_000_000_000,
                                (nsec_since_epoch % 1_000_000_000) as u32,
                            )
                            .unwrap(),
                        );
                    }

                    // Float32 → f32
                    Field::Float(val) => {
                        df.features.entry(name.clone()).or_default().push(val);
                    }

                    // Double → downcast to f32
                    Field::Double(val) => {
                        df.features
                            .entry(name.clone())
                            .or_default()
                            .push(val as f32);
                    }

                    // Skip everything else
                    _ => {}
                }
            }
        }

        Ok(df)
    }

    pub fn len(&self) -> usize {
        self.timestamps.len()
    }

    pub fn time_span(&self) -> TimeDelta {
        if self.timestamps.is_empty() {
            TimeDelta::zero()
        } else if self.timestamps.len() == 1 {
            TimeDelta::zero() // Single timestamp, no span
        } else {
            *self.timestamps.last().unwrap() - *self.timestamps.first().unwrap()
        }
    }

    pub fn columns(&self) -> Vec<String> {
        self.features.keys().cloned().collect()
    }

    /// Creates a DuckDB CREATE TABLE statement for the DataFrame structure
    ///
    /// # Arguments
    /// * `table_name` - The name of the table to create
    /// * `column_overrides` - Optional HashMap to override column types for specific features
    ///
    /// # Returns
    /// A String containing the CREATE TABLE statement
    pub fn create_table_statement(
        &self,
        table_name: &str,
        column_overrides: Option<&HashMap<&str, &str>>,
    ) -> String {
        let mut columns = Vec::new();

        // Add timestamp column first
        columns.push("timestamp TIMESTAMPTZ NOT NULL".to_string());

        // Add feature columns in the order they appear in the IndexMap
        for feature_name in self.features.keys() {
            // Sanitize column name by replacing spaces and special chars with underscores
            let sanitized_name = feature_name
                .chars()
                .map(|c| {
                    if c.is_alphanumeric() || c == '_' {
                        c
                    } else {
                        '_'
                    }
                })
                .collect::<String>()
                .to_lowercase();

            // Check for column type override, default to REAL
            let column_type = column_overrides
                .and_then(|overrides| overrides.get(feature_name.as_str()))
                .unwrap_or(&"REAL");

            columns.push(format!("{} {}", sanitized_name, column_type));
        }

        // Build the complete CREATE TABLE statement
        format!(
            "CREATE TABLE IF NOT EXISTS {} (\n    {}\n);",
            table_name,
            columns.join(",\n    ")
        )
    }

    /// Stores the DataFrame data into a DuckDB table using an appender for efficient insertion
    ///
    /// # Returns
    /// Result with number of rows inserted, or error
    ///
    /// # Errors
    /// Returns error if table creation fails, appender creation fails, or data insertion fails
    pub fn to_duckdb(
        &self,
        conn: &Connection,
        table_name: &str,
        column_overrides: Option<&HashMap<&str, &str>>,
    ) -> anyhow::Result<usize> {
        // Validate that we have data to insert
        if self.timestamps.is_empty() {
            println!("No data to insert into DuckDB table '{}'", table_name);
            return Ok(0);
        }

        // Validate that all feature vectors have the same length as timestamps
        let expected_len = self.timestamps.len();
        for (feature_name, values) in &self.features {
            if values.len() != expected_len {
                anyhow::bail!(
                    "Feature '{}' has {} values but expected {} (same as timestamps)",
                    feature_name,
                    values.len(),
                    expected_len
                )
            }
        }

        // Create the table
        let create_sql = self.create_table_statement(table_name, column_overrides);
        conn.execute(&create_sql, [])?;

        // Create appender for efficient batch insertion
        let mut appender = conn.appender(table_name)?;
        let mut row = vec![];

        // Insert each row
        for i in 0..self.timestamps.len() {
            row.extend_from_slice(duckdb::params![self.timestamps[i]]);
            row.extend(self.features.values().map(|v| &v[i] as &dyn duckdb::ToSql));

            appender.append_row(row.as_slice())?;
            row.clear(); // Clear the row for the next iteration
        }

        // Flush the appender to ensure all data is written
        appender.flush()?;
        conn.flush_prepared_statement_cache();

        Ok(self.timestamps.len())
    }

    pub fn parquet_writer(
        &self,
        file_path: &Path,
        compression: Option<parquet::basic::Compression>,
    ) -> anyhow::Result<ParquetWriter> {
        // Validate that all feature vectors have the same length as timestamps
        let expected_len = self.timestamps.len();
        for (feature_name, values) in &self.features {
            if values.len() != expected_len {
                anyhow::bail!(
                    "Feature '{}' has {} values but expected {} (same as timestamps)",
                    feature_name,
                    values.len(),
                    expected_len
                )
            }
        }

        // Build the schema
        let mut fields = Vec::new();

        // Add timestamp column
        let timestamp_name = "timestamp";
        fields.push(Field::new(
            timestamp_name,
            DataType::Timestamp(TimeUnit::Nanosecond, None),
            false,
        ));

        // Add feature columns (assuming they're f64 - adjust type as needed)
        for feature_name in self.features.keys() {
            fields.push(Field::new(feature_name.as_str(), DataType::Float32, true));
        }

        let schema = Arc::new(Schema::new(fields));

        // Create new file (this will overwrite the existing file)
        let file = File::create(file_path)?;

        // Set up writer properties with compression
        let props = WriterProperties::builder()
            .set_compression(compression.unwrap_or(parquet::basic::Compression::SNAPPY))
            .build();

        let writer = ArrowWriter::try_new(file, schema.clone(), Some(props))?;

        Ok(ParquetWriter { writer, schema })
    }

    pub fn to_parquet(
        &self,
        path: &Path,
        compression: Option<parquet::basic::Compression>,
    ) -> anyhow::Result<()> {
        let mut writer = self.parquet_writer(path, compression)?;
        self.to_parquet_writer(&mut writer)?;
        writer.writer.close()?;
        Ok(())
    }

    pub fn to_parquet_writer(&self, writer: &mut ParquetWriter) -> Result<()> {
        writer
            .writer
            .write(&self.to_arrow_record_batch(writer.schema.clone())?)?;
        Ok(())
    }

    fn to_arrow_record_batch(&self, schema: Arc<Schema>) -> Result<RecordBatch> {
        // Convert data to Arrow arrays
        let mut arrays: Vec<ArrayRef> = Vec::new();

        let timestamp_nanos: Vec<i64> = self
            .timestamps
            .iter()
            .map(|dt| dt.timestamp_nanos_opt().unwrap_or(0))
            .collect();
        let timestamp_array = TimestampNanosecondArray::from(timestamp_nanos);
        arrays.push(Arc::new(timestamp_array));

        // Convert feature data (assuming f32 values)
        for feature_name in self.features.keys() {
            let values = &self.features[feature_name];
            let float_array = arrow::array::Float32Array::from(values.clone());
            arrays.push(Arc::new(float_array));
        }

        // Create record batch for new data
        let new_batch = RecordBatch::try_new(schema, arrays)?;

        Ok(new_batch)
    }

    pub fn write_csv_header(&self, writer: &mut BufWriter<std::fs::File>) -> Result<()> {
        // Write header
        writer.write_all(b"timestamp")?;
        for key in self.features.keys() {
            writer.write_all(format!(",{}", key).as_bytes())?;
        }
        writer.write_all(b"\n")?;
        Ok(())
    }

    pub fn write_csv(&self, writer: &mut BufWriter<std::fs::File>) -> Result<()> {
        // Write data rows
        for (i, timestamp) in self.timestamps.iter().enumerate() {
            writer.write_all(format!("{}", timestamp.to_rfc3339()).as_bytes())?;
            for key in self.features.keys() {
                if let Some(values) = self.features.get(key) {
                    if i < values.len() {
                        writer.write_all(format!(",{}", values[i]).as_bytes())?;
                    } else {
                        writer.write_all(b",N/A")?;
                    }
                } else {
                    writer.write_all(b",N/A")?;
                }
            }
            writer.write_all(b"\n")?;
        }
        Ok(())
    }

    pub fn remove_rows(&mut self, indices: &[usize]) {
        if indices.is_empty() || self.timestamps.is_empty() {
            return; // Nothing to remove
        }

        // Remove rows at specified indices
        let mut to_remove = indices.to_vec();
        to_remove.sort_unstable();
        to_remove.reverse(); // Start from the end to avoid shifting issues

        for &index in &to_remove {
            if index < self.timestamps.len() {
                self.timestamps.remove(index);
                for values in self.features.values_mut() {
                    if index < values.len() {
                        values.remove(index);
                    }
                }
            }
        }
    }

    #[must_use]
    pub fn limit(&self, n: usize) -> DataFrame {
        let mut limited_features = IndexMap::new();
        for (key, values) in &self.features {
            limited_features.insert(key.clone(), values.iter().take(n).cloned().collect());
        }
        DataFrame {
            timestamps: self.timestamps.iter().take(n).cloned().collect(),
            features: limited_features,
        }
    }

    pub fn append(&mut self, other: &DataFrame) {
        if other.timestamps.is_empty() {
            return;
        }

        if self.timestamps.is_empty() {
            self.timestamps = other.timestamps.clone();
            self.features = other.features.clone();
        }

        let needs_sorting = self.timestamps.last().unwrap() < other.timestamps.first().unwrap();

        self.timestamps.extend_from_slice(&other.timestamps);
        for (key, values) in &other.features {
            self.features
                .entry(key.clone())
                .or_insert_with(Vec::new)
                .extend_from_slice(values);
        }

        if needs_sorting {
            // Sort timestamps and features if the new data is later than the existing data
            self.sort_by_timestamp();
        }
    }

    pub fn sort_by_timestamp(&mut self) {
        let mut indices: Vec<usize> = (0..self.timestamps.len()).collect();
        indices.sort_by_key(|&i| self.timestamps[i]);
        self.timestamps.sort();

        for values in self.features.values_mut() {
            let sorted_values: Vec<f32> = indices.iter().map(|&i| values[i]).collect();
            *values = sorted_values;
        }
    }

    pub fn set_all(&mut self, key: &str, value: f32) {
        if let Some(values) = self.features.get_mut(key) {
            for v in values.iter_mut() {
                *v = value;
            }
        } else {
            let values = vec![value; self.timestamps.len()];
            self.features.insert(key.to_string(), values);
        }
    }

    pub fn print_head(&self, n: usize) {
        for i in 0..n {
            if i < self.timestamps.len() {
                let timestamp = self.timestamps[i];
                print!("{}: ", timestamp);
            } else {
                break; // Stop if we run out of timestamps
            }

            for (key, values) in &self.features {
                if i < values.len() {
                    print!("{}: {:.2}, ", key, values[i]);
                } else {
                    print!("{}: N/A, ", key);
                }
            }
            println!();
        }
    }

    pub fn slice<'a>(&'a self, range: std::ops::RangeInclusive<usize>) -> DataFrameSlice<'a> {
        let start = *range.start();
        let end = *range.end();
        assert!(
            start <= end && end < self.timestamps.len(),
            "Invalid range for slicing"
        );

        DataFrameSlice {
            df: self,
            start,
            end,
        }
    }

    /// Creates a sliding‐window iterator over the DataFrame:
    ///
    /// * Each window covers [start_time, start_time + window_len].  
    /// * After yielding (or skipping) one window, we advance start_time by exactly `interval`.  
    /// * If any two consecutive timestamps inside the candidate window differ by more than `max_sparse`, we skip that window.  
    pub fn sliding_window(
        &self,
        window_len: Duration,
        interval: Duration,
        max_sparse: Duration,
    ) -> impl Iterator<Item = DataFrameSlice<'_>> + '_ {
        let timestamps = &self.timestamps;
        std::iter::from_fn({
            let mut start_idx = 0;
            let mut end_idx = 1;
            let mut next_target = timestamps.first().cloned();

            move || {
                if self.len() < 3 {
                    println!("DataFrame has fewer than 3 timestamps, returning empty iterator");
                    return None;
                }

                let n = timestamps.len();
                'outer: while let Some(target_time) = next_target {
                    // 1) Move start_idx up to ≥ target_time
                    while start_idx < n && timestamps[start_idx] < target_time {
                        start_idx += 1;
                    }
                    if start_idx >= n {
                        next_target = None;
                        return None;
                    }

                    let current_start = timestamps[start_idx];
                    let window_end = current_start + window_len;

                    // 2) Grow end_idx (never moving backward)
                    if end_idx < start_idx {
                        end_idx = start_idx;
                    }
                    while end_idx < n && timestamps[end_idx] <= window_end {
                        let time_diff = timestamps[end_idx] - timestamps[end_idx - 1];
                        if time_diff > max_sparse {
                            // If we hit a sparse gap, we skip this window
                            start_idx = end_idx + 1; // Jump to the next point after the gap
                            continue 'outer; // Try again from the next start_idx
                        }
                        end_idx += 1;
                    }
                    let window_end_idx = end_idx.saturating_sub(1);

                    // 4) If we reach here, no large gap → valid window
                    next_target = Some(current_start + interval);
                    return Some(self.slice(start_idx..=window_end_idx));
                }
                None
            }
        })
    }

    pub fn sliding_window_ranges(
        &self,
        window_len: Duration,
        interval: Duration,
        max_sparse: Duration,
    ) -> Vec<(usize, usize)> {
        self.sliding_window(window_len, interval, max_sparse)
            .map(|s| s.indices())
            .collect()
    }
}

pub struct DataFrameSlice<'a> {
    df: &'a DataFrame,
    pub start: usize,
    pub end: usize,
}

impl<'a> DataFrameSlice<'a> {
    pub fn timestamps(&self) -> &[DateTime<Utc>] {
        &self.df.timestamps[self.start..=self.end]
    }

    pub fn features(&self) -> impl Iterator<Item = (&String, &[f32])> {
        self.df.features.iter().map(move |(key, values)| {
            let slice = &values[self.start..=self.end];
            (key, slice)
        })
    }

    pub fn get_column(&self, column: &str) -> Option<&'a [f32]> {
        self.df
            .features
            .get(column)
            .map(|values| &values[self.start..=self.end])
    }

    pub fn timespan(&self) -> TimeDelta {
        if self.start == self.end {
            TimeDelta::zero() // Single timestamp, no span
        } else {
            self.df.timestamps[self.end] - self.df.timestamps[self.start]
        }
    }

    pub fn indices(&self) -> (usize, usize) {
        (self.start, self.end)
    }
}

pub struct ParquetWriter {
    schema: Arc<Schema>,
    writer: ArrowWriter<std::fs::File>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::{TimeZone, Utc};
    use indexmap::IndexMap;

    #[test]
    fn write_read_parquet() {
        let ts0 = Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap();
        let ts1 = Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 1).unwrap();

        let df = DataFrame {
            timestamps: vec![ts0, ts1],
            features: IndexMap::from([
                (String::from("temp"), vec![20.0, 21.0]),
                (String::from("pressure"), vec![1.0, 1.1]),
            ]),
        };

        // Create temp file and write DataFrame to it
        let temp_dir = tempfile::tempdir().unwrap();
        let temp_path = temp_dir.path().join("test.parquet");

        df.to_parquet(&temp_path, None).unwrap();

        // Read back and compare
        let df2 = DataFrame::read_parquet(&temp_path).unwrap();

        assert_eq!(df.timestamps, df2.timestamps);
        assert_eq!(df.features.len(), df2.features.len());
        for (key, values) in df.features.iter() {
            assert_eq!(values, &df2.features[key]);
        }
    }

    #[test]
    fn append_and_set_all() {
        let ts0 = Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap();
        let ts1 = Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 1).unwrap();
        let ts2 = Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 2).unwrap();

        let mut df1 = DataFrame {
            timestamps: vec![ts0],
            features: IndexMap::from([(String::from("a"), vec![1.0])]),
        };

        let df2 = DataFrame {
            timestamps: vec![ts1, ts2],
            features: IndexMap::from([(String::from("a"), vec![2.0, 3.0])]),
        };

        df1.append(&df2);
        assert_eq!(df1.len(), 3);
        assert_eq!(df1.features["a"], vec![1.0, 2.0, 3.0]);

        df1.set_all("b", 7.0);
        assert_eq!(df1.features["b"], vec![7.0, 7.0, 7.0]);
    }

    #[test]
    fn sliding_window_ranges_basic() {
        let base = Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap();
        let mut df = DataFrame::empty();
        for i in 0..5 {
            df.timestamps.push(base + chrono::Duration::seconds(i));
        }
        df.features.insert("x".into(), vec![0.0; 5]);

        let ranges = df.sliding_window_ranges(
            chrono::Duration::seconds(2),
            chrono::Duration::seconds(1),
            chrono::Duration::seconds(1),
        );
        assert_eq!(ranges, vec![(0, 2), (1, 3), (2, 4), (3, 4), (4, 4)]);
    }

    #[test]
    fn remove_range() {
        let base = Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap();
        let mut df = DataFrame::empty();
        for i in 0..5 {
            df.timestamps.push(base + chrono::Duration::seconds(i));
        }
        df.features
            .insert("x".into(), vec![0.0, 1.0, 2.0, 3.0, 4.0]);

        df.remove_rows(&[1, 3]);
        assert_eq!(df.len(), 3);
        assert_eq!(df.features["x"], vec![0.0, 2.0, 4.0]);
    }
}
