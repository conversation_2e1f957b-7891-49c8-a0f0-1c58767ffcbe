use rustfft::{num_complex::Complex, FftPlanner};

/// Helper: takes a slice of samples and a sample rate,
/// applies a Hanning window, runs FFT, and returns (frequencies, magnitudes)
pub fn compute_spectrum(data: &[f32], sample_rate: f32) -> (Vec<f32>, Vec<f32>) {
    let n = data.len();
    if n == 0 {
        return (Vec::new(), Vec::new());
    }

    // Build complex buffer and apply Hanning window
    let mut buffer: Vec<Complex<f32>> = data.iter().map(|&x| Complex::new(x, 0.0)).collect();
    for (i, sample) in buffer.iter_mut().enumerate() {
        let window = 0.5 * (1.0 - (2.0 * std::f32::consts::PI * i as f32 / (n - 1) as f32).cos());
        sample.re *= window;
    }

    // FFT
    let mut planner = FftPlanner::new();
    let fft = planner.plan_fft_forward(n);
    fft.process(&mut buffer);

    // Build frequency and magnitude vectors (up to Nyquist)
    let mut freqs = Vec::with_capacity(n / 2);
    let mut mags = Vec::with_capacity(n / 2);

    for i in 0..(n / 2) {
        let freq = i as f32 * sample_rate / n as f32;
        let mag = buffer[i].norm() / n as f32;
        freqs.push(freq);
        mags.push(mag);
    }

    (freqs, mags)
}

// Compute spectral centroid within a given band [f_low, f_high)
pub fn band_centroid(freqs: &[f32], mags: &[f32], f_low: f32, f_high: f32) -> f32 {
    let mut weighted_sum = 0.0;
    let mut mag_sum = 0.0;
    for (&f, &m) in freqs.iter().zip(mags.iter()) {
        if f >= f_low && f < f_high {
            weighted_sum += f * m;
            mag_sum += m;
        }
    }
    if mag_sum == 0.0 {
        0.0
    } else {
        weighted_sum / mag_sum
    }
}

/// Compute spectral energy (sum of squared magnitudes) in [f_low, f_high)
pub fn band_energy(freqs: &[f32], mags: &[f32], f_low: f32, f_high: f32) -> f32 {
    mags.iter()
        .zip(freqs.iter())
        .filter_map(|(&m, &f)| {
            if f >= f_low && f < f_high {
                Some(m * m)
            } else {
                None
            }
        })
        .sum()
}

/// Compute peak frequency (freq with max magnitude) in [f_low, f_high)
pub fn band_peak_frequency(freqs: &[f32], mags: &[f32], f_low: f32, f_high: f32) -> f32 {
    let mut max_mag = 0.0;
    let mut peak_f = 0.0;
    for (&f, &m) in freqs.iter().zip(mags.iter()) {
        if f >= f_low && f < f_high && m > max_mag {
            max_mag = m;
            peak_f = f;
        }
    }
    peak_f
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn simple_sine_spectrum() {
        // Generate 50 Hz sine sampled at 1000 Hz
        let sr = 1000.0;
        let n = 256;
        let data: Vec<f32> = (0..n)
            .map(|i| (2.0 * std::f32::consts::PI * 50.0 * i as f32 / sr).sin())
            .collect();

        let (freqs, mags) = compute_spectrum(&data, sr);
        let peak = band_peak_frequency(&freqs, &mags, 0.0, 200.0);
        assert!((peak - 50.0).abs() < 1.0);

        let centroid = band_centroid(&freqs, &mags, 0.0, 200.0);
        assert!((centroid - 50.0).abs() < 1.0);

        let energy = band_energy(&freqs, &mags, 0.0, 200.0);
        assert!(energy > 0.0);
    }
}
