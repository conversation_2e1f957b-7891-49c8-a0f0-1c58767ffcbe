import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import ipywidgets as widgets
from IPython.display import display, clear_output
import eda

class TimeSeriesCleaner:
    def __init__(self, df, time_col, features, gap_threshold='1H'):
        self.df = df.copy()
        self.time_col = time_col
        self.features = features
        self.removed_ranges = []
        
        self.df[time_col] = pd.to_datetime(self.df[time_col])
        self.df = self.df.sort_values(time_col).reset_index(drop=True)
        
        self.segments = self._find_segments(gap_threshold)
        self.selection_mode = False
        self.start_x = None
        
        # Enable interactive mode
        plt.ion()
        
    def _find_segments(self, gap_threshold):
        time_diffs = self.df[self.time_col].diff()
        gaps = time_diffs > pd.Timedelta(gap_threshold)
        
        segments = []
        start = 0
        
        for i, is_gap in enumerate(gaps):
            if is_gap:
                if start < i:
                    segments.append(self.df.iloc[start:i])
                start = i
        
        if start < len(self.df):
            segments.append(self.df.iloc[start:])
            
        return segments
    
    def _create_plot(self):
        n_segments = len(self.segments)
        self.fig, axes = plt.subplots(n_segments, 1, figsize=(15, 4*n_segments))
        self.axes = [axes] if n_segments == 1 else axes
        
        colors = sns.color_palette("husl", len(self.features))
        self.patches = [[] for _ in range(n_segments)]
        
        for i, segment in enumerate(self.segments):
            ax = self.axes[i]
            
            for j, feature in enumerate(self.features):
                ax.plot(segment[self.time_col], segment[feature], 
                       label=feature, color=colors[j], linewidth=1.5)
            
            start_time = segment[self.time_col].iloc[0].strftime("%m/%d %H:%M")
            end_time = segment[self.time_col].iloc[-1].strftime("%m/%d %H:%M")
            ax.set_title(f'Segment {i+1}: {start_time} - {end_time}')
            ax.legend()
            ax.grid(True, alpha=0.3)
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            # Connect mouse events
            ax.figure.canvas.mpl_connect('button_press_event', self._on_press)
            ax.figure.canvas.mpl_connect('button_release_event', self._on_release)
            ax.figure.canvas.mpl_connect('motion_notify_event', self._on_motion)
        
        plt.tight_layout()
        return self.fig
    
    def _get_segment_for_axes(self, ax):
        for i, segment_ax in enumerate(self.axes):
            if ax == segment_ax:
                return i
        return None
    
    def _on_press(self, event):
        if event.inaxes in self.axes and event.button == 1:
            self.selection_mode = True
            self.start_x = event.xdata
            self.current_ax = event.inaxes
    
    def _on_motion(self, event):
        if self.selection_mode and event.inaxes == self.current_ax and event.xdata:
            # Clear previous selection rectangle
            for patch in getattr(self, 'temp_patches', []):
                patch.remove()
            
            # Draw new selection rectangle
            ylim = self.current_ax.get_ylim()
            width = event.xdata - self.start_x
            patch = Rectangle((self.start_x, ylim[0]), width, ylim[1]-ylim[0], 
                            facecolor='red', alpha=0.2, edgecolor='red')
            self.current_ax.add_patch(patch)
            self.temp_patches = [patch]
            self.fig.canvas.draw()
    
    def _on_release(self, event):
        if self.selection_mode and event.inaxes == self.current_ax and event.xdata:
            segment_idx = self._get_segment_for_axes(self.current_ax)
            if segment_idx is not None:
                xmin = min(self.start_x, event.xdata)
                xmax = max(self.start_x, event.xdata)
                self._add_removal(xmin, xmax, segment_idx)
            
            # Clear temp patches
            for patch in getattr(self, 'temp_patches', []):
                patch.remove()
            self.temp_patches = []
            
        self.selection_mode = False
        self.start_x = None
    
    def _add_removal(self, xmin, xmax, segment_idx):
        start_time = pd.to_datetime(xmin, origin='unix', unit='D')
        end_time = pd.to_datetime(xmax, origin='unix', unit='D')
        
        self.removed_ranges.append({
            'segment_idx': segment_idx,
            'start': start_time,
            'end': end_time,
            'xmin': xmin,
            'xmax': xmax
        })
        
        ax = self.axes[segment_idx]
        ylim = ax.get_ylim()
        patch = Rectangle((xmin, ylim[0]), xmax-xmin, ylim[1]-ylim[0], 
                         facecolor='red', alpha=0.4, edgecolor='darkred', linewidth=2)
        ax.add_patch(patch)
        self.patches[segment_idx].append(patch)
        
        # Add removal count text
        mid_x = (xmin + xmax) / 2
        mid_y = (ylim[0] + ylim[1]) / 2
        text = ax.text(mid_x, mid_y, f'#{len(self.removed_ranges)}', 
                      ha='center', va='center', color='white', fontweight='bold')
        self.patches[segment_idx].append(text)
        
        self.fig.canvas.draw()
        self._update_status()
    
    def _update_status(self):
        if hasattr(self, 'status_widget'):
            with self.status_output:
                clear_output(wait=True)
                if self.removed_ranges:
                    removed_count = sum(self._count_removed_rows(r) for r in self.removed_ranges)
                    print(f"Marked {len(self.removed_ranges)} ranges for removal ({removed_count:,} rows)")
                else:
                    print("No ranges selected")
    
    def _count_removed_rows(self, removal):
        segment = self.segments[removal['segment_idx']]
        mask = (segment[self.time_col] >= removal['start']) & \
               (segment[self.time_col] <= removal['end'])
        return mask.sum()
    
    def clear_selections(self):
        self.removed_ranges = []
        for patches in self.patches:
            for patch in patches:
                patch.remove()
            patches.clear()
        self.fig.canvas.draw()
        self._update_status()
    
    def undo_last(self):
        if not self.removed_ranges:
            return
            
        self.removed_ranges.pop()
        
        # Remove last two patches (rectangle and text) from any segment that has them
        for patches in self.patches:
            if patches:
                patches.pop().remove()
                if patches:
                    patches.pop().remove()
                break
                
        self.fig.canvas.draw()
        self._update_status()
    
    def export_csv(self, filename=None):
        cleaned_df = self.get_cleaned_data()
        if filename is None:
            filename = f"cleaned_data_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv"
        cleaned_df.to_csv(filename, index=False)
        print(f"Exported {len(cleaned_df):,} rows to {filename}")
        return cleaned_df
    
    def get_cleaned_data(self):
        if not self.removed_ranges:
            return self.df.copy()
            
        mask = pd.Series(True, index=self.df.index)
        
        for removal in self.removed_ranges:
            segment = self.segments[removal['segment_idx']]
            segment_mask = (segment[self.time_col] >= removal['start']) & \
                          (segment[self.time_col] <= removal['end'])
            mask.loc[segment.index[segment_mask]] = False
            
        return self.df[mask].copy()
    
    def show(self):
        # Create the plot
        self.fig = self._create_plot()
        
        # Create control widgets
        clear_btn = widgets.Button(description='Clear All', button_style='warning')
        undo_btn = widgets.Button(description='Undo Last', button_style='info')
        export_btn = widgets.Button(description='Export CSV', button_style='success')
        
        clear_btn.on_click(lambda b: self.clear_selections())
        undo_btn.on_click(lambda b: self.undo_last())
        export_btn.on_click(lambda b: self.export_csv())
        
        controls = widgets.HBox([clear_btn, undo_btn, export_btn])
        
        # Status output
        self.status_output = widgets.Output()
        
        # Display everything
        display(widgets.VBox([
            widgets.HTML("<b>Instructions:</b> Click and drag on the plots to select time ranges to remove"),
            controls,
            self.status_output
        ]))
        
        plt.show()
        self._update_status()
        
        return self

# Usage function
def clean_timeseries(df, time_col, features, gap_threshold='1H'):
    return TimeSeriesCleaner(df, time_col, features, gap_threshold).show()

df = eda.load_hp_dataset("tb_01_good")[['vd_rps', 'ewt', 'hp', 'lp', 'brine_in', 'sh']]

cleaner = TimeSeriesCleaner(df.reset_index(), 'timestamp', ['vd_rps', 'sh', 'ewt', 'brine_in'])
cleaner.show()

# Get cleaned data
cleaned_df = cleaner.get_cleaned_data()
