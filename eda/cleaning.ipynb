{"cells": [{"cell_type": "code", "execution_count": 17, "id": "02de2751", "metadata": {}, "outputs": [], "source": ["import eda\n"]}, {"cell_type": "code", "execution_count": 18, "id": "e1ea4e6c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>vd_rps</th>\n", "      <th>ewt</th>\n", "      <th>hp</th>\n", "      <th>lp</th>\n", "      <th>brine_in</th>\n", "      <th>sh</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>22008.000000</td>\n", "      <td>22008.000000</td>\n", "      <td>22008.000000</td>\n", "      <td>22008.000000</td>\n", "      <td>22008.000000</td>\n", "      <td>22008.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>68.444794</td>\n", "      <td>36.225655</td>\n", "      <td>14.131746</td>\n", "      <td>4.827493</td>\n", "      <td>7.046069</td>\n", "      <td>10.189116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>35.700142</td>\n", "      <td>6.909907</td>\n", "      <td>2.679290</td>\n", "      <td>2.142818</td>\n", "      <td>7.396112</td>\n", "      <td>9.456807</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>28.940001</td>\n", "      <td>7.780000</td>\n", "      <td>2.400000</td>\n", "      <td>-1.100000</td>\n", "      <td>-16.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>30.610001</td>\n", "      <td>30.040001</td>\n", "      <td>12.380000</td>\n", "      <td>3.480000</td>\n", "      <td>0.020000</td>\n", "      <td>7.530000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>92.199997</td>\n", "      <td>34.009998</td>\n", "      <td>12.710000</td>\n", "      <td>4.260000</td>\n", "      <td>5.510000</td>\n", "      <td>8.470000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>92.199997</td>\n", "      <td>40.080002</td>\n", "      <td>15.220000</td>\n", "      <td>5.350000</td>\n", "      <td>12.420000</td>\n", "      <td>15.330000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>92.330002</td>\n", "      <td>50.150002</td>\n", "      <td>19.969999</td>\n", "      <td>12.340000</td>\n", "      <td>21.030001</td>\n", "      <td>39.220001</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             vd_rps           ewt            hp            lp      brine_in  \\\n", "count  22008.000000  22008.000000  22008.000000  22008.000000  22008.000000   \n", "mean      68.444794     36.225655     14.131746      4.827493      7.046069   \n", "std       35.700142      6.909907      2.679290      2.142818      7.396112   \n", "min        0.000000     28.940001      7.780000      2.400000     -1.100000   \n", "25%       30.610001     30.040001     12.380000      3.480000      0.020000   \n", "50%       92.199997     34.009998     12.710000      4.260000      5.510000   \n", "75%       92.199997     40.080002     15.220000      5.350000     12.420000   \n", "max       92.330002     50.150002     19.969999     12.340000     21.030001   \n", "\n", "                 sh  \n", "count  22008.000000  \n", "mean      10.189116  \n", "std        9.456807  \n", "min      -16.500000  \n", "25%        7.530000  \n", "50%        8.470000  \n", "75%       15.330000  \n", "max       39.220001  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "df = eda.load_hp_dataset(\"tb_01_good\")[['vd_rps', 'ewt', 'hp', 'lp', 'brine_in', 'sh']]\n", "df.describe()\n"]}, {"cell_type": "code", "execution_count": 19, "id": "7e42f426", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='timestamp'>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df.plot(y='vd_rps')"]}, {"cell_type": "code", "execution_count": 20, "id": "73f2496b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/tn/8gmb5b0d3n939xr1fwtj8s2m0000gn/T/ipykernel_55341/729479731.py:24: FutureWarning: 'H' is deprecated and will be removed in a future version. Please use 'h' instead of 'H'.\n", "  gap_threshold_td = pd.Timedel<PERSON>(gap_threshold)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAASlCAYAAABHkZBpAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3QecHHX5P/Bn+/Wau1x6DymQhJqEUAIBQpWq2FERyw/8CehfRUHEBqI/QVSwIaCCUiSiICX0lkAIBBIggfSeu5TrZdv8X59ndvZm93bv9i63t3u3nzdsttzu1O/3OzPPfOcZh2EYhhARERERERERERERURfOrh8REREREREREREREREwiE5ERERERERERERElASD6ERERERERERERERESTCITkRERERERERERESUBIPoRERERERERERERERJMIhORERERERERERERJQEg+hEREREREREREREREkwiE5ERERERERERERElASD6ERERERERERERERESTCITkRERERERERERESUBIPoRERElHarV6+Wiy66SMaNGyd5eXkyatQoOfXUU+XXv/615JqdO3fKD37wA1m1alVK329ubpbrr79eTj/9dKmoqBCHwyF33333QU1DOBzWYXzkIx+RMWPGSGFhoRx66KHy4x//WNrb2xP+5s4775Tp06fr+psyZUrCdffwww/LxRdfLBMnTpSCggI55JBD5Bvf+IbU19d3+e748eN1XuIfX/nKV1Kejx07dsjHPvYxKSsrk5KSEjn33HNl48aNCb+7Z88e+fKXv6xlD/OA8V966aUpjef111+X//mf/5EjjzxSPB6PTmd3UllW/bmuA4GAzJgxQ7//i1/8IqVx7dq1S77zne/ISSedJMXFxfrb559/vsv3Nm/enHA9WY/LLrusX8t7qtauXSvf+ta3ZM6cOTr9I0aMkLPOOkveeOONPpeVbdu2yQ033CDHHHOMlJeXy7Bhw2ThwoXy9NNPdxke1kuyZbJ79+6U56M3ZeX++++X+fPna33FfBx77LHy7LPPpjSen/zkJ1rfhw8frtOIddIf9SreU089pfUK7YnL5dJ6lkh35eof//iHpOrf//63HHHEEbr8xo4dq/UnGAzGfAfrMNm4UJ+JiIiIUuFO6VtEREREffTqq69qoA4BDgTcampqNFi1fPly+dWvfiVf+9rXJJcgqIhAHYJLCAD2ZO/evfLDH/5Ql9/s2bMTBjp7q7W1VT7/+c/LvHnzNGhdXV0ty5Yt0wDUM888o4E5e6D497//vX7vwgsvlKuvvlpeeukl+d///V8dzre//e3o9770pS/JyJEj5dOf/rROL06e/OY3v5H//ve/8uabb0p+fn7MdGD+EWS3mzp1asoBZ5SrhoYG+e53v6vBsFtuuUVOPPFEDdhWVlZGv4vytmDBAn2N+UAgHesBwfFUYPr/9Kc/yaxZs/QEwQcffJD0u6kuq/5c1wi8bt26VXpj3bp18rOf/UwDt4cddpiu/0Sqqqrkr3/9a5fPn3jiCbn33nvltNNO69fyniqsDwSgsZxxggPlAMseZRrTdsopp/S6rDzyyCO6TM477zy55JJLNBj7l7/8RU/4/fnPf9Y6Ew/ra8KECTGfIficit6UFQS9MS6cjPzc5z6nJ07WrFmjAe9UXHvttdr2Hn744fLkk0/2S71K5L777tNgPwLbaAt68olPfELOPPPMmM9woiAVjz/+uK4rBMlRB9De4ERgbW2t3HHHHdHvfe9735MvfvGLMb9taWnRZd9T+SUiIiKKMoiIiIjS6MwzzzSqqqqMAwcOdPnbnj17jFyzYsUKA7tgd911V0rfb29vN3bt2tWn3ybT0dFhvPLKK10+v+GGG3T4S5cujX7W2tpqVFZWGmeddVbMdz/1qU8ZhYWFxv79+6OfPffcc12Gec899+gw//jHP8Z8Pm7cuC7D7I2f/exnOtzXX389+tn7779vuFwu45prron57hlnnGFMmDDB2Lt3b5/GtXv3bl0OcPnll+t4E+nNsuqvdY06VFpaavzwhz/U7//85z9PaZ4aGxuNffv26esHH3xQf5to/SWzaNEio6SkxGhra+v2e/1VZuO98cYbRlNTU8xnWL9oaxYsWNCnsrJmzRqjrq6uyzqZNm2aMXr06JjPMT8YJuavL3pTVpYtW2Y4HA7jl7/8pdFXmzZt0mfMH6b7+uuvP+h6lciOHTsMv9+vrzFvqOfJpqc35TWRGTNmGLNnzzYCgUD0s+9973u6rDDN3fnrX/+q47/33nv7PH4iIiLKLUznQkRERGm1YcMGmTlzZsLemegBHe9vf/ubps1Ar2WktPj4xz+uPYnj/fa3v9Vewfge0i+gFyd6JOJhQU9e9Kh+4IEHtDcseiAj9QN6c6KnZUdHh1x55ZU6HUVFRdrTFJ/1ZZowXqQweO+997QnJ9KZYHw333xzzPQcffTR+hrjslIKdJeyw+fzaQ/SVGCekOYCz93xer2aCiLe+eefr8/vv/9+9LPnnntO9u3bp7197S6//HLtzfnYY4/FLINUhmnn9/t1OL310EMP6bK0lidMmzZNFi1apOvbguWBHqv/7//9P+1Fi3Q16MXbG0iBEd+LPpHeLKuDXdcWpGRB2hz0/u+uDuJhh3qAstwXSAWDeb3gggs0jUYyqZT3Bx98MFq3kD4F85FK72r8BnXWDuv3+OOP71LWUi0raKcwDfHrBD2lt2/fLk1NTQmnBZ+HQiHpjd6UlVtvvVXLxde//nWcvdHe4r2VLK1KvFSXVTLofd7bFCmYX7QDvWnX0M7igatf3O7Oi6uxPLGMMB899ZhHWhykqiEiIiJKBYPoRERElFbIg75y5UpNPZBK3t7PfvazmmLil7/8pQa4kV7khBNOiMmrjUv1r7jiChk9erQGqRE4w2X9CHQlcuONN2oKAwQcv/CFL2jublzKj9dIzYFUCQgIIriHdA59mSY4cOCA5rNGKo7/+7//0+AT0jIgiAvIfYyUDIDgD9Jk4IFh9YclS5boOPDcF1YuZ3sg8a233tLno446qksQ0+l0Rv/em2FakDYGJxsQDEWQD+l9Us3p/s4773SZJsAJFQSMrYCnlc8agXAEAhGsxeOMM87QvMz96WCXVW8hHc0999yjQdbu8rRjvvHoL8hZjXXwqU99qtvv9VTeUd+Qexu5s1FHke4JdfO4445LmEc/FShv9rLWm7LS3TBRTvGIhxNmyBuOvyHn+IcfftjvZQXtDYLat912m6bXsXLAI1VSf+qPZdVbOLmJ+o+TMZhH5FRPpV1LtvwQxMd2obu6VldXJ0uXLtVtBgLpRERERKlgTnQiIiJKq29+85sasEQ+ZARiEPBGQA/BJ3uPxS1btmhObuS0RS5eC4LbyON7++236+fosXjddddpwAVBWKsXIvJVI1cwAijxkNv4hRdeiI4PQRQEAhHwRr5rqwfj+vXrNffx97///V5Nkz3/M3Iof+Yzn9H3uMEeTiIgdzOWAQK5eMbwkfe3u97DmYATEggIYhrtvY4R5Iy/agC92dHzF/PcHZyUwO/R+98O6wvBUvSiRo9cBFRxggLDiz+REW///v16xQACifGszzAcDNsKaiKIizKDfM3IH47gHfJmI2iYKDjaFwe7rHoDvW1xPwHcyBVlqb9PCHQHudCxnE8++eRuv9ddecfVADjBhKs3XnzxxWiPdpSJs88+W/NwYx31Bq5GQW535P/uS1lJBG0CAvsf/ehHdd1aUGbQ3lhBdJwoxEk2XOGB/P+4YW9/lBWcmEOu/FdeeUXbO7RHyJl/11136fpHm4Yb5vaHg11WvYETBchHjitVcMUOblyK5YfygpuF4iaxPS0/+3TFT2t3dQ1tALYJPZ0EIiIiIrJjT3QiIiJKK9yUD4Et9NJ8++23NVC7ePFiDZwgWGJBoAo9IdEzFUEj64E0BugFjvQH8MYbb2jQFb1W7ZfxIyBSXl6ecBrQk9wesJ87d64GIdET3Q6fI00LAiy9mSYLelTaA4UIiOHEAQJEAwFBPcwXnnvrpz/9qfbavummm2JS77S1tel8JILAJ/7eXcoEnEDAzUOxvOyw7r/1rW9pOgWsB5zkQLlAIC3ZFQX2abJSbSSaJvt3rNQXWGdIkYF1iRM7f/zjH7VnLaaxvxzMsuotnHTAjRR7OuEACLD3V5AdV24gYIyURgiE9hXqMW4AiZNX9pQwCJ7iCo6eUt/Ew7A++clP6k0+Ua76Ulbi4QafCJ7jygXUCzuUIwSy0bagR/OPfvQjvdoFbROuXumvsmKVXwwXN1NF2cW4sXxmzJihJ/j6y8Esq97CiQAsL1wRdM4552iqGvQeR0/7+JsNJ2rXeprWntoljAfbJiIiIqJUMYhOREREaYcewAhIo1clUlBcc801mhYAvZOR1xbQYxiBEgRbEeCwP5DjGEEyq3c4TJ48OWYcCKgny/uLgI1daWmpPsf3FsXnCJpbuXdTnSYLesHHp9VAYB/znc3QMxO9d9Fz/qtf/WrM3xBATJavGPnFk+UKR69gDA+B8VSCilhuV111lZ7AQC5tQCAMqTTsD2uaIFH+ekyT/TvWMwKP9qAvgqMoM6+++mo0WGkfD65W6K2+Lqveamxs1DqEPO899XhORy90ONhevFY9TtSrGUF06++p5tRG73W0KY888khMrvTelBU75DjHiQK0T8ivjTQhPUEvepyIs1IIAcqRvVxZQfFUy4r1jJOA9qs5UJZxFQJOOOHKCoivK70NePd1WfUX5OhH7vx169b1eCKtp2lNNp04oYmTulh29pOwRERERD3hngMRERENGPS8tG5aN3XqVA2Y4MaCSFGA4DUCqcgfbk+bYIm/iWBvJBped58jcA69naaehpeNkBsYvWnRA/h3v/tdwtQICCjihIE99QQCgOgdmyi4iCsOcOUBUnUgAJlqsMoKCCOthBXcRxmJX5YItqEHqpXSwc76zJou6xmpReLXFdJmWCc4fvGLX8SkD0Eant723u7LsuoLTCuGiUCgNY1W0BHzg88wrmQ9nQ8GevEi8I3c3dkAywHplZCWBz2bUebselNW7HCly6OPPqonDXpKWxNfhhEEtqCts58QQFuHezCkWlYw/ehZjatD4tsX63dY5zhRGJ/aBD3le3NVSl+XVX+ytwGJUnNZrHnFdMWfSMJnuAIoEevKE6ZyISIiot5iEJ2IiIgywrohnBWcmTRpkgZIkY4BAfZkENy0chUjH7EFPZgRPESu7f6S6jT1Rnc3gBxor732muYkxrp44IEHEga7kcveSr9x5plnRj/He5xksP5uQYoU5JpHgA/55ntz8sNKe4Oe/oBe7Ajyx0Mv3MMOO0ynIdE8TZw4UW++CFawd8eOHTHfQ7ASqXmsceFEAnoSW/rS47a3y6qv0PMYgdOZM2cmTMuDB1Jj9Nf47MsW9c66WejBlHerHiPgHB+kxmfW37uDZYr1hhtvovyeeOKJB1VWLOjhjwA0btj6iU98QnoDZdgqU4AgvL1HOMbXm7KC6cfrFStWaJm1nxix8n5b44uvK4nKR3f6sqz6W3wbkIx9+dkD5lgmOKGEeyAkC6KjXZ83b16/TjcRERENfUznQkRERGmFvOGJemJbN/S00jmgNyl6WqI3cPz38R69MwEBX/QgRk5rK3e5Fazq77QpqU5TbxQWFupzfX299DekoVm7dm00HU13kI4Gvc+RAgc9bpMFjRHgRA/VO+64I+ZzvMfNFe03AEQKCdwsEME49ApOFghDL1P0wrXDjSaRdxpBQuvkCHqb4uaf9ocFqS0QWLQH/BB8xc0XkarFsnDhQg3oo3xYKSmsnOKYBisvMgKE9vEsWLCgx2V4MMvqYPzv//6vLFmyJObx+9//Xv+Gnsd4jxM/9hMbeBwsqxcvco8fbHlHPcZ6wdUP9pQcuOrDKps9wY01cbUCbvCLuppMqmUFfv7zn2tPf9wwGHm6k0mU7gdtGvLF4ySSBeXIXq6sIHpvygquOEBZveeee6KfoSyjTCMvutU7PL6uJLrpZk96s6wORqLlhxNduLEzToTapz1Ru4YTBEj784c//CGmLcHyw4mb+BsZA04soWz1pvwSERERWdgTnYiIiNIKgS7coA89nhH0QG9K5KFG8AsBXCtdB3oH4iZ5yPWMHuW4WR96PW7atEmDguhZiJvqIciKdAgYLgJRyHWN7yMoimH0Z0/vVKept8NEagYEDzEsBBmRR9ke9Iz3m9/8RoOQVs/T//znP9H0HVgOVo53TBOWZ09pHJA7Gr28cdIBvW7jb+KIaZw/f76+RnAdN028/PLLNYiG3yHf+d/+9jfNdY5AoAXBQ/QkxY0dX375ZX1YkE7FCljjpqJYrgh0Yb4RVEeAds2aNdqLGjcB7QluSIkTKQg2Yh0gZzRuSorx2G9MiPQUCIxecsklcsIJJ8hnPvMZ7cn9q1/9So4//vhug68WpOP461//qq+t4KJ1Q0f0mMYwe7usDmZdH3HEEfqws9K6ILiIcmq3aNGimO9YrHl499139RnzaK0z5Mi3Q6ASdRY9eFE++qO846aoKK/oQY4e33v27NH1gnYB+fG7g17iCJ6jnCLojGVsh/bGCuCnWlZQf1B2cQ+E6dOndxkmyq+VFujYY4+Vww8/XE8GYJ28+eabGgBGahEE4HvSm7Ly5S9/WW8qiu/ixq5I3YJ1hXKJ8pEK6/toi+HFF1+Mrn+UX6vnf6rLKhmk1bFuGI2rFhD4tsYze/ZsvYkoYDnjxA7KJk4CoGziRBDy26MM2CVr11CvkTYKJ+6Qvx7tB+rPF7/4RV1/6crnT0RERDnKICIiIkqjxx9/3PjCF75gTJs2zSgqKjK8Xq8xefJk42tf+5qxZ8+eLt//5z//aRx33HFGYWGhPvC7yy+/3Fi3bl3M92677TZj3Lhxhs/nM4455hjjlVdeMY488kjj9NNPj37nueeeQ/dx48EHH4z57V133aWfr1ixIubz66+/Xj+vq6vr9TSdeOKJxsyZM7vMzyWXXKLTaffII48YM2bMMNxut44P09Md/B7fS/TYtGlTl/nqaXj4TbLh4YFpjveHP/zBOOSQQ3T9TZo0ybjllluMcDgc853uhonlY3njjTeMc845xxg1apQOD+UCy/eBBx4wemPbtm3GRRddZJSUlOgwzj77bOPDDz9M+N2///3vxuzZs7W8DB8+3LjiiiuMxsbGlMZjlaOe5qs3y+pg13Wydfrzn/884TDjy2BP6yveE088oZ+j3vVWd+X9/vvvNw4//HBdLxUVFcanPvUpY/v27T0OE2W0u+mPX1aplBWr/id7oBxYvve97xlz5swxSktLDY/HY4wdO9b46le/auzevbtXyybVsoK2EvOMZYRlNXfuXF0nqUI5TWW+Ul1WyVhtUE/tyn333WeccMIJRlVVlZaLYcOGGeeff76xcuXKpMNM1K4tWbJE1wOWyejRo41rr73W8Pv9Xb4XCoW0vTniiCNSXGJEREREsRz4J9OBfCIiIqKDhTzCSB+CnsXoSUlERERERETUH5gTnYiIiAYd5AOO7wfwl7/8RdOCIAc2ERERERERUX9hT3QiIiIadJ5//nnNmYxcwrjJKPIR33nnnZoHFzf2Q950IiIiIiIiov7AG4sSERHRoIMbD+IGfrfddpv2PsdN+D772c/KTTfdxAA6ERERERER9Sv2RCciIiIiIiIiIiIiSoI50YmIiIiIiIiIiIiIhmo6l3A4LDt37pTi4mJxOByZnhwiIiIiIiIiIiIiOkhIoNLU1CQjR44UpzOzfcEHfRAdAXTkRCUiIiIiIiIiIiKioWXbtm0yevTojE7DoA+iowe6tTBLSkoyPTmUgSsR6urqpKqqKuNnpIho4LDuE+Um1n2i3MS6T5S7WP+JcpNV930+n4wbNy4a/82kQR9Et1K4IIDOIHpuVqr29nZd99ygEuUO1n2i3MS6T5SbWPeJchfrP1Fu1/28vDx9nw0pvNkCERERERERERERERElwSA6EREREREREREREVESDKITERERERERERERESXBIDoRERERERERERERURIMohMRERERERERERERJcEgOhERERERERERERFREgyiExERERERERERERElwSA6EREREREREREREVGmg+g33XSTOBwOufLKK6Oftbe3y+WXXy6VlZVSVFQkF154oezZs2egJomIiIiIiIiIiIiIqFtuGQArVqyQ3//+9zJr1qyYz6+66ip57LHH5MEHH5TS0lK54oor5IILLpBXXnllICYrZ4QNQ5oDfilwe8ThEGkJBKJ/w2dup/Ogh7+vvU0a/B2yv6O9y9+d4pDJpWVS5stLaXjNgYBOY0swIB2hoLgcDnE6HOJyOPU1prcyL198LlfK02gYhjQF/F0+d4hDijxYLo6UhtMWDMr+jjb9HaZJ/3OY86jPmFZxiMfl1GXb0zQ1BvwSDIclGDYkZISlNRiQkGGI2+HU+fS6nFLs8ek09haGh+XY23m0aw8GxR8Odfnc6XD2aZoOVkcoZC6jsCFBIyyBsLnMMI9YXm6nQwrdHvE4zeXfHgqJ0yHidbp03aAe9ATD6Wnd9QTrFuUXJSx+ObVHynRHKCxhI5zyMDGP+W63zq8dpjXRZ5hfQwytN72FMol6bYhoPfOHQvoeZbPQ49GyhXWR53JLfUeHjgd10qq/vZkvwHC8vajP2QbLBm1Dgdut828uufTMO5a91f6kCuUD6zSez+XusR212k7UNbO+BbVOoY5hOgCTYraGeB15jrSJYntt/d3ndOkbzHtvtmGom6j7ZtnsXMqGIdF3eK3PkU/M97bv4j/bdwBlGQ+rTcf8db7ubOPNddV9fSryeBOumwMd7V3qKRR7vFqvUX6SSbW91W2KP7KujJC0BTFfZnuDdsDcjjrEFWknre0q6nR/7F+gTcbwPVi/vYTyieFY28JgZPuFNqez/ETKlGHIgdZmaW3yidPp1HVj/o915Epp3wDbBKs9ixcIh5KuD2v9Ylr92oab04thtUW2l1bZwbJAebfKE95byx3r1Ot0RtvYVPYNsF7N35r7GdHXDofOT57bpWU02fxjeWLeMK2oA/osZn3CtKNOo2xY9Tq+bps12Posto53qeeR76JtQ/nuDsaP/UdsG5v82H4Ykfkyy765HEXyXW7dzqTa9pnlp+dtUbI6Gw/rJdX2HfOcaj3Aek7UNvR1eFbZxTPWN/aDMHzUdewjWc+Y7xKPN+V9Q6yXRn+HlhurXGI/B21yovWFdj7P7baVJpNV9tHmprJvau1PYfzhsCHNwYDk+f3ixAh7AXUE2xCsv+7a20SsbWX8sQTqGyYfwzuY/Rj7dt0qZxgf6mSiMof2FvteHeFgzHZY28K4emgtXrNdMj9zOfu+D9+betCX/dveHLOgDOFbaBtTqeu9gW1zX46b7Kz9rp7qt539uNxe9pNBu2BvY61xov2z9oOs31v79Yb1HNkfwnNLIKjP0f2EyD4D9mtRDlG2E+2zmftPqdUnrKe97W16jIvjZGwPsb6t44uwhHV6hhcUdHvsgv2p9jDWfef+GUoRmoT4/QVnpLz7XM5e1/veSHaMhv0wxA2s71h106qn5it7Xe3cAsdsf22vsS6wnu3rCvumaNOxLLEttR/Hor5i3whlAvtW2pZGlnl7ZP0l2v+19mHs+xf4DL/Fvg7eY18obEjMOsR6rmtr0202lgmmx2rfrGFa2wkMA+VRj+Edzph9d/t+vbmvou9iyq71CbZJ1rKPLwNYZuY23lx+VlsokfKB8qb7zaFwlzrT3bjxbO37YV7NfQn81vy9Ds1aLoY5fbH7tJ3loLMMWMdSXde9K7IuGgMdMrao5KDjd5TlQfTm5mb51Kc+JX/84x/lxz/+cfTzhoYGufPOO+W+++6Tk08+WT+76667ZPr06bJ8+XKZN29ewuF1YKehoyP6vrGxUZ/DCPokCBIMZe/sq5NfvrPSbLgiB0LRRgwb3sgGFME6NHbYaUU1tAdF0WBhA2kPFEQbN3Foo2zuOEpMY2C9D+kGsV0b5VR2SEYVFklVXr6Uen06jfgdhodGeE9bqwYAEgXi46FRqc4v0OGVO1xStXubFHt92rgFIztTGM7OlhapbWuVen9HJKjZVYUvT3fYsCww39jpx7RZOwvY+DVFDj6xIcL0pqIoctIiGTTY2HilAgc6WA/YGOE5HHcQbG+grYYfOzb+SJ3AOsb8oMHFhtTaaJnrNPZg2nxvHhxhvrubJgwXQQhzx7+zjGADYm1krM8QfMN4rA2ntYPmjOwQdF/OzHnTA7YUlz/KMn4Licp+d0q9Xt0xxUFggceNmTE3qpEXeE60U4oNqVWmMb/Wssc8A36CedCdxj4cgNjnqbvPrPnFtOW7e3dQh2nEQaq5UypS6HFH152+d3u0LqBsYZ2hnFnziflKVs+6g7o3LC9fy0Xs6u180xkE7ZxQtD8IosV+M35+Eg3D9u3EL2O+Y5+mrkMTXRZYDvblkSqUDSy7VGGH3DqwMQOi2PF0RHeY7dsA1GO0qVifycaNts8MqjjFE9keaJAkcpIKJ0itdqQ/oXyhbKXCWr6DAcqAWf86oT3srl6g/Pe0DTWDr92PuzfblPj2zjoAsOqg/SAhvj2X6DbCDBpj3WCeMX6UJd2eRsoltqf4u7m9iTuYj2yDMHwcxKWjnPUk0XK1t3eJ1i/qjdW+Hwx7G9sf67G7etVTGUyn6PbU5Yq2LyHrZEk4LA1+v7SlGIRBXcH8YXja7mH/I7Kts7a9OOmL9ZPq/GKa0H5a06fl3NoHiexfYXpx4Jrqesd0mm2r2UajfiAQhfY71CWI0XPZt++rxzxw4sjt0g4PQcPQbTOWZ6pQ760ybZ1Us7Yt1vLU/a8QOrck7lTRF9Z+i33/KBn7/lR/jrsv2+n4umlNu7Vv25tteaLtOgJ91vxaAZNEdQN/S3Vf+GC2KYkgWJVqfe3rekmlXADKJPRlf7onxR6PFLq9tsBZZ/BZ4gLR1jFC53ZTv9Gn6UNdt/bbUy371vHmwR5jdAdroyq/IHKM19l+aceZfh4X2stkJ4Ox39/ah+1jX+p9No6jO9b2FftciG1gnaHNxrF8f0yb9mGwzWem57e/9Ed7OtCWnPYRqSkolFwRDuMEm5FVsV6HYY8wpMEll1wiFRUVcsstt8jChQtlzpw5cuutt8qzzz4rixYtkgMHDkhZWVn0++PGjdOUL+ilnsgPfvADueGGG7p8/sEHH0hxcbHkkjcP7JMb3l8l2cDspeeUYd68Lj16OsIh2dVunoVNlR58I7DqNAPGVtASB4IBAycFMleJfLagXWcAtbPnY19gftGIY37N3pboiWfossMzxdKeBZFllh/Z0cJywkEfDnC4xIj6lyNycGcFiENJeoPH9/4eyLpo9uTo2mPW/KTzs87vmm88TjNYEX9SLGw7KNbgXDikn/VVXlxPUj3JnaYTFOhpim0V5ss64LUC1mgn0zHe/oDSZfVWQoDPCqREewXZAvBGZF3ag/3YNxjIgzqzR7h5EIZlbfUujwYabGWoL8vdvm+AIKx9/q31aZWj3hwEWtNt9cjCdJsn32y9vyLftfcIsz636nj0vRw88yQeguPOmPJqXXmCbXt2ltrshfKDdYu6ZAW98DiY/WiUF2v/Ky+yz2pfX1aHDAyf+6/pgzpstjfZtR2m7GTuuZntvvkqtgcsjrmtYKK9rUCP7/5UEunwgTaoLRIMt9oUbI/0StgUhoO2J77Xcmwv++xR4fVF90WtabR0PRFjfWrf1sbWa8QGrH0Naz31fb/F3E/s3O/t2oEilf0La9/NOrlb6vHo71tDuErHNnxbhwr7PnCy4Vv79VYPbfOzrr34dXsUiQfYl3HXMmH1KjfnrbvtlP2YIlm90d70TpeuE/vJHWt52K+IQLzCipF1mcZoJz1ryrque912h8NS7PbITw49QsYWFEmuCKPDRUODdpybNm2avi4pKRm6PdH/8Y9/yJtvvqnpXOLt3r1bvF5vTAAdhg8frn9L5pprrpGrr746pif6mDFjpKqqKuMLc6DNKy2VX5aVRhstV/TS887Lhq1Lc9H7e29Hm1ZI9OC2DkzRSxsNSOcBmXWpvNnA4aAFPVuil/hEeqp3NpSil9eiF2l3l5XgssO97a2yo6VF9mnPbn9nepZIz5fqggIp9/r0TDd6qieDaT3g75AdLc2yvalR1u+tFcPr1bP1mDYcNGBaSrxeGVlQpGfq0Gt6REFhl2nEGfttzU2R3utmEBY9+PE99A4CLEfMH3o+YZh43d20Ydnh0uRE6WPiYV10l9LAvHQ0EOkNGjIvx45c9hlzSVSksTYvA4xeICTD8wt0nmrb2sxLw/Wy8GDMOrUuH9K+y7Z1i/lGT4xE6wLDwCV56I3SEvDrPNvLiMQND1Nk9YS3X9pk9fJKpZxh3lBOUD66u/Ta6oGLecY8QHPQ36XsJ4P1hvJq9bA0y1XspYHWxtyaDvs8WukeKn35Oi0H/LFXVuA7mGf0HOnpMnc77RXc0abr1NqVwPrEeqjK65wvjLO2vbVLyorecEbqJZ5RDzQ4FNk5wVUjGD+uQEB5xPRg/dVH5rPMm9fr3lhbmhtjenfZ11DM68g8OiIb1AMH6qWiotz2eWwagkTDsAdYOz9xJBlf19/ZX9nTlaDXIcoO1gWCXamqa2/Tsp8qjEvrTaQnp7VTZfY+snZizbYVr1HOUO4T9epBu2L2ujV3ItGDE3XaapdRX9GbxWz/nAd1abPVNlopATA+q41NZZ6xDcO2SoPDLldM+piBYm0fk8G6wDY1UZ1DL28sy3h1ba26/rCOksE6supXT4bnF6aUzgTr3Oq1tq+9XdqCgegVC/YDlvj2PHoprG4jEOR2RvcvEOi1empie4NtFa4gKnBZV7vZL6W12ndzjFivNfldt9GJoO7X1dXpfp91JUqq68iCMihJUnwhmIvtTDy0t7hEGesX61IvRe9l+bOWO9Yplo3VxvZ23yCetV3FFXjJ6hW25b1J39EXMT01BWlazO2plVYEV2thv9Seng9tFPbPekqFgTJltVlYF2gPzPSEZtoy6+SLlc6tzOeL7gN0N8zd2A+OtH2YPntqEvtJHWzz0L6nUr/QDmD/werNjddYBvnYL48EGczlYC4D+3Y92fD0JJ6eVDCHaUR7ZZqX7lu93TUNhYiUoydiN+savddxBag9JRHaZZRLrEdrHaEnPQ7+kdKkpgDbN1evymQ86yoBlEXs06QC+1N6FSauft1bJ8OGda37PcEVqZpOD9uTbtrbRFDuECSB6ryCaDuF/S/MJ4bX2225nbXurdqD+UVKJwwP21/rJK99H2+YL1+v9EmVvec0ekinuk3pbn30F5TtAx0dUp2fr2myUi0XaCPNKy7dPdb13kB9xX6p0UMQLzYdQ2caBmuNWsfgmK/u6nd8XbdfmdbTskY5sW/HrCAmtiHRFD4H0eZb29RdrS2RY/fOYzPz6gtPwn2bZOzbeAw3fvrQFu9ua0k8LWFD9u/fL+OGD5fKFOqwVeaxncD+DfZV0gVlBm1M/Lq2rrzuT5gv+zKz0v5g+49xYzuA19gmYHlj24+/YzuDffvelgf7/gXqKoaB7QxiHUiFYh0X93Ve9DnSNg30vj32AVC2cQyHGM/BjD9Ree5v8es+V4TDYTMdqC95fHDI9ETftm2bHHXUUbJ06dJoLnR7T3Skcfn85z8fk5oFjjnmGDnppJPkZz/7WUrjQRAd+dSz4YwEZaZS1dbWSnV1da93qIlo8GLdJ8pNrPtEuYl1nyh3sf4T5Xbdz8vLk/Ly8qyI+6atBVq5cqXO7BFHHCFut1sfL7zwgtx22236Gj3O/eihUl8f87s9e/ZITU1NuiaLiIiIiIiIiIiIiCjz6VyQ73z16tUxn6HnOfLYfPvb39YULB6PR5555hm58MIL9e/r1q2TrVu3yvz589M1WUREREREREREREREmQ+i4yafhx56aMxnhYWFUllZGf380ksv1fzmuPEouuR/7Wtf0wD6vHnz0jVZRERERERERERERETZcWPRntxyyy2a0wo90ZEbffHixXL77bdncpKIiIiIiIiIiIiIiDITRH/++edj3iM5/G9/+1t9EBERERERERERERFlG97amIiIiIiIiIiIiIgoCQbRiYiIiIiIiIiIiIiSYBCdiIiIiIiIiIiIiCgJBtGJiIiIiIiIiIiIiJJgEJ2IiIiIiIiIiIiIKAkG0YmIiIiIiIiIiIiIkmAQnYiIiIiIiIiIiIgoCQbRiYiIiIiIiIiIiIiSYBCdiIiIiIiIiIiIiCgJBtGJiIiIiIiIiIiIiJJgEJ2IiIiIiIiIiIiIKAkG0YmIiIiIiIiIiIiIkmAQnYiIiIiIiIiIiIgoCQbRiYiIiIiIiIiIiIiSYBCdiIiIiIiIiIiIiCgJBtGJiIiIiIiIiIiIiJJgEJ2IiIiIiIiIiIiIKAkG0YmIiIiIiIiIiIiIkmAQnYiIiIiIiIiIiIgoCQbRiYiIiIiIiIiIiIiSYBCdiIiIiIiIiIiIiCgJBtGJiIiIiIiIiIiIiDIRRL/jjjtk1qxZUlJSoo/58+fL448/Hv37woULxeFwxDy+8pWvpHOSiIiIiIiIiIiIiIhS5pY0Gj16tNx0000yZcoUMQxD7rnnHjn33HPlrbfekpkzZ+p3LrvsMvnhD38Y/U1BQUE6J4mIiIiIiIiIiIiIKDuC6Oecc07M+5/85CfaO3358uXRIDqC5jU1NemcDCIiIiIiIiIiIiKi7Aui24VCIXnwwQelpaVF07pY7r33Xvnb3/6mgXQE3a+77rpue6N3dHTow9LY2KjP4XBYH5RbsM5xlQPXPVFuYd0nyk2s+0S5iXWfKHex/hPlpnAW1v20B9FXr16tQfP29nYpKiqSJUuWyIwZM/Rvn/zkJ2XcuHEycuRIeeedd+Tb3/62rFu3Th5++OGkw7vxxhvlhhtu6PJ5XV2djoNyCypTQ0ODViynk/fJJcoVrPtEuYl1nyg3se4T5S7Wf6LcrvvOLKr3DgMtURr5/X7ZunWrzvhDDz0kf/rTn+SFF16IBtLtnn32WVm0aJGsX79eJk2alHJP9DFjxsiBAwf05qWUe5UKJ1CqqqqyqmIRUXqx7hPlJtZ9otzEuk+Uu1j/iXK77vt8PqmsrNS4cqbjvmnvie71emXy5Mn6+sgjj5QVK1bIr371K/n973/f5btz587V5+6C6Fh4eMRDY8oGNTc5HA6uf6IcxLpPlJtY94lyE+s+Ue5i/afBwGhrl/Ca9eKYOFqclWWZnpwhVfezhTMTZxLsPcntVq1apc8jRowY4KkiIiIiIiIiIiIi6r3gv5+XwN//K4HfP5DpSaHB2BP9mmuukTPOOEPGjh0rTU1Nct9998nzzz8vTz75pGzYsEHfn3nmmdotHznRr7rqKjnhhBNk1qxZ6ZwsIiIiIiIiIiIion4R3rRdn4299ZmeFBqMQfTa2lr57Gc/K7t27ZLS0lINjiOAfuqpp8q2bdvk6aeflltvvVVaWlo0r/mFF14o1157bToniYiIiIiIiIiIiIgoO4Lod955Z9K/IWiOG4wSEREREREREREREWWr7MnOTkRERERERERERESUZRhEJyIiIiIiIiIiIiJKgkF0IiIiIiIiIiIiIqIkGEQnIiIiIiIiIiIiIkqCQXQiIiIiIiIiIiKivjKMzpfhztc0dDCITkRERERERERERNQfwuFMTwGlAYPoRERERERERERERH3lcHS+ZhB9SGIQnYiIiIiIiIiIiKgf0rkwiD40MYhORERERERERERE1B9CDKIPRQyiExEREREREREREfUH9kQfkhhEJyIiIiIiIiIiIuqP3ufsiT4kMYhORERERERERERE1EeGvfc5e6IPSQyiExEREREREREREfWVrfe5wZ7oQxKD6ERERERERERERER9xZ7oQx6D6ERERERERERERER9Ze99ziD6kMQgOhEREREREREREVFf2QPnTOcyJDGITkRERERERERERNRX7Ik+5DGITkRERERERERERNRXzIk+5DGITkRERERERERERNQHRtgQMYzODxhEH5IYRCciIiIiIiIiIiLqi7igucGc6EOSO9MTQH1n+AMiza3mG49bxO0WaWs335cW41SYSGOLSFmxOJyd50uMphaRfJ9IU2vsmbJ4eT6zIcB4wOcV6fB3/R7GjUe7X6QgTyQQFEdhvhjBoPn3nsYTmT4D4wqGxOH1mNPZ0maOH8NubddnR3GhOVzMl57tC4ujoVkMt08M2zzGT5/5u5BIY7P5GaYT84XPCvPN+cL38vPEwGuMF42ewyHidIoDywvjw9+8HnE4HOb04b3Pa84vXuMzt0skP09/p8PB8i4pFMEiwPopKjC/F4gsH7zH/OG7RQU6/4a1Xl1OkbYOHae+xrItKRID3w+ar2PKhH0e8X1MQ0G+OFxOMfA5/m5b7jpeTEdpkTlN+D7mA9OE6cHfI+se89g5ns51oPOLsoHfQ3GhSGubuQzwWSnWr8Ncvw3NZlnAd/Dc3mGWWy2PLea6xnLP84rD7TaXKb6Hz7CurfWI5YjpsYaHsup1d04TYJk7nCINTeZ6LC4w/45lhuFG1p2yXmOZ4bs4i4zptco7/obxYJ4wPKwPTDPGD1hupUVaLqLLCL+31mOknDl8XjEw7VinWMaAcojfWdOA9/gbxmeVEWs6MH2Y1/hypvXL6Kxr1jrAvOI9xo9h2Os91guGh19a02SVM+ssOoaNdY3P49cByhOmG9OKdYfXaHeaW8zvYl1iXiLLRss/lh3qnoi5LDAOLMM8rzmvVh02DHFYy8dq69xusxxh2C2tYuA96n5eoRhOhzlczINVLvCM+SouMqepsMB8j2WM9RKZjmi5tcqC1eZY7Rn+bs0jlgP+juWL+cfyxef4nlX2MA9YhlhGGA7aC9Rpq73AssCwMSx8B9MeaX+sHHox825rc6LLIlG9subX5TKXe2T9ao8ILHcs6/h6bGvDtDwBpgHTj+nDOkH9DETGCVoWQpGyFVmn8e076l6idh/lA9NolXe3K9qG6Xzp+Fzm3+3v7ds2DBvLGusAyxrjty93q87YRdrV6Hgi69+R5zPLPtp4lMdI2Yppz6y2A2XL2hHGOsQyj7TZuszxGsPCd9C2olzgc0w/2jmv16xLtnY/Zpoi282E5d1qc/BbzKPVJuryKBKH29V1u26VZ5Qdj9vcpvgi02DV9Uj91m0K1qnVniViX6dYjyi3GKc1v6gPXm/nttIqWxBZ1ok+1+WL7YxVx3SbldfZdlrrAMs8cmBiNDSbdd+bJwaWK+YlruxHp8/axkfWQcy2HMO1lwVd7q7o/pJhLSsMI9JWRrehKH/4LYaNdWy1sZj29kh9RjtmrQdrePib1cbi+1b7iHWL6cF02vZ5YrZzVlkUR2Q7FSlzGIa1nwbaFnR0tmXW8ADTg4cuD0/i+m2VM7zG9BZF2ozo+o+0YdYytNr6ZPuI2E5hGWF9gH2arN9gfAX55nrDOFDH7esRyxXNCbZreMY04HOUG6u9sNaBvWzZ58u+3CHS/iQs+yjvmD6sZ6uuR8ticew2Bdsvv61uWsvaVvYNLG/UlUTLx74PhdcYBubR2v5gXjFee5tj22eM7itgGaB82/eh8LlVzjAN8fA5ygs2L/b5xXuM0yrvJYU6vGhZxnet4dnn0V7OrG2KNV/2dtVWznQd2NsFqy232jNMH+o21lNkmAb2AQKhzvm1yr+1n2WNx9q+Wc94NCZY1/btd5L2xzxOiWxPMU16jBDZh8OyCVvHDI7YabIvD2u+8T2U3/hpsvZBUWcA5cu+347Pre1Ifl5se4tjGOxrRPYZtI5a2834dY4yZQ0X6w1l2dqW2/eX7ax9Ckyrtb9in0eLNZ/WPhS+g2WDbYD1OrLvqsPDOOtt+xH2YxNrOjBN+BzrxX7shGnAPMbvu1rHTt1NE4YTac8cLlfn/miiOgb4jb2uQ1ybY+5TRNpePFAu/H5z3q3tHL6Psmzti3V3jByp31qmsdyxnuzbNjvbtiamLkWOy7uUfWseMbzI9HdpY619PNQra38F47bvZyU6/rKmp8Ovgwj+6xkJb98j7tMWiGvqOPPvycqZ9bke1hjmco+0bdqehULa/uj22ppuzGeXfShzWx49vrGGjXKEYTdF9uGxTPA+vs2xHzvFs44JrX1OLBssS6uc2bcBdlb9Rrm19nni2x/7ts2KFVjrML7sW2XLav+temDt41n76pjHRIFc+7G4/bgtPv5j24eKbsvtZcsqc9Yxkr2tj49Jxa9nq35Hjpei9S5+vzN+muK3KfHtNNavvY5FynD0+NW+3O3Lo9m2D21f9/Z14IyUFfs85vnEUWDGkAy0Z3bYX8XvrGFbsRtMR1yMjgYPh6FbjcGrsbFRSktLpaGhQUpKSiSXhNZuksAfHkz8R+xwYtVGdgA8F5yiDUxo1VoJv70u7dPmnDVVwu+uT+2OxMWFOn3Bx18SY+8BcZ0yXxum0CtvdR3ujEkS/mCLuRPZm+mZNlHCG7cl3uGzf2/2IRJ+Z525QbVxnTpfN0qhl98Ux4RR4hxRJaFXV8X97oPYHSErSNaby3icTnHOnCzh1R8k/46tYXfOmCiuow8zPw+GJPDgk13nMd8nzjEjJPzBZjkYruOPFOfE0Tp8HU/8DlwyZcXiOfdkCTzyrLmj3MM8KbdLnNMnSnj1hzFfc06bIOFN20U6ul+P5pdxEsF2EJhmjrEjxH3SMdH3gX890+XAXMtJf9c/3WkxEtYJx5gaPcAw9tZ3/V2eTzwfPU3CO2ol9Oxr/V6XYqZjZLUGaoy6A9HPXMfO0Z1qY+uuxOM5fJq4Zh0i4W27JfTcazoM98lzJfDQ0sQHhtnK6RDnoVPM9iFFrqMPNdu6zTsk9MIb4hg3UtwLj9ZAaHDJ07q+HRWl4j5noQSWPNM1KACRdj/4xMti1O7vHPa8WeI8ZIKE129N2MZGWScltG52dGkTHaOq9YDU2JegbPUCypPzsCkS/OfSXreVKddvp0Pc55+iyw0Hc1Y7ba+P7rNPlOBzr8fuFKc0HZGd/1TaJDuHQ9zYLhuGBLEODUPbWCzv0DPLxTG8UtynHSuBh5+ODejE83rEOWmMhN/f2M02ZVKXtjTmK4eMl/CWXYmDbIkU5Ilz1HAJf7ily590+91ubivt3KcfpwcWoeVvS59YAblU1ndhvm6jUca7TJ9tW27nnIOy8IE4qit0WlPezuHADW1wogNtjO/Eo8Q5flT3w/N5eld+EJhBuetFG5wUgkJ6slqyg3Xgj31HrMeRVRL+sOt6jMIBaEWpGBu392l0zqnjJLx1d+plvy/jONjtPgID5SVibN6Z+m/cLnGfvVCC/3kutf3wFIbn+ehiXT/YBwu9uLL/5jHZyZfowB1mmQ+GxHX6AmnbulO8azYk/q7DYR5/9Pd+lrUf++/nRA409n04CLzYg7sHyXXasZ3HS9jXOCwy7whSd7efhHrW2/YjP0+cY4abx2D9wdpHeepVMXbvlYzJ82rZxvbf2L7noAaVln18QAARbVQ3297+FrOPh/2V8xfpcWR4zXrJVq7jjtBlFFq6TLfl2PfG/pSdY0SVGYi37RdTmtljUulUUiSe8xdJ8NEXuh6bOJ3i/shCCT72YuLONv0gWQypC+skXCgsjomjxXfFJ9MyPUNJOByW2tpaycvLk/Ly8qyI+6Y1iH7HHXfoY/NmM3g3c+ZM+f73vy9nnHGGvm9vb5dvfOMb8o9//EM6Ojpk8eLFcvvtt8vw4cNTHkdOB9HXbZbAnx8+uMbA6q0cr7thImCQyvfsrN6FdtZZ3r6KTAeKsL0HcIw0NZSDmh6MDOHlgnLB9U40cKztiHY5CnZt9+M/T7e+bKMSsXoY59r2wb45Heh1F78ecYIQQdU4hsOBHVjJGVqPjMGx7euv+pepsp9smhHMRXlMRXfrIpPLZ7Cum/6mV4r00LZ10/70ajz9sZwPZjj2dY75NXrRzqS6nCwHM6+oe8ZBtH/Z3Cb2xLr6wi6V6baWfW/mMZ1lMtH0JDrmxLzaT+5Zv0NdS7WN7c/y1ZO+lHH7b3rzu6HU/ida95Gr9Pp13/pg9tV72u73VF+S/d36vKdOEi6X5P38G32Y8NwSzsIgelrTuYwePVpuuukmmTJligY677nnHjn33HPlrbfe0oD6VVddJY899pg8+OCDGgi/4oor5IILLpBXXnklnZM1ZLgOGS+un12tl6KiFx96KeJsenjjdrOXFS6VG14p4V11sTsf6A1cXiKuI2eIe97shMPGpYGBh57SRsBz0Wl6+TTO7LlPPkac40Z2fi/Ss8/YXy+OkmJzXJGUJdozCD0Nzj1JXEfOTDie0JvvSXDZ2+b0NbVoT1WclQOkjbAuhfecf4oEn14u4dp9evkferu5poyLVqrq6mpxJjgZgFQH6OWH6cLlMq6T5+rZaSwvx7ByPWuNvzuKC8TAGXdcym9dFoyNuV46GTJ7a2F4G7ebvWpxqSx6ppy9UAL/flaMxhZxeDziPvckCb3xrtmzt6RQnDXDxFFdKSH0TGhq0R5TWPZO9Ha74FQxdtXqWVH0JHROHS/B/76kl3xrigPtaRY0z6o++YpeIujI80p4X72Z7gGX98WdadV5POkYcU2fKMGlr+q0WCk9nGNqxP2Rk/SEQ+it9yX46ipxDisTx4TRElqxRnuzOmqGieuYw8zy43Lpusd4Ag8+ofMYHY/LJe5T54tj0hjzjO+uvdpLwahvlNDTy3W6sHwdVeVmmbDK7DGH6eV2wX89q99xHn2o2dOhpVWclWV6ObCxsy5yqZMhxqYdZq8BlAOUK+sSKJThsmJxz52lPfe1h19rW2e52LhNgk++qme9tWdCMCSh11drfTD27NVLIj0fOUkC/3nenK5ZUyX4/Apd9wbKCsaZ7xPPmSfoetQyvrdeyyR68uIEFnojoZeh69ApEnx6mV4ZErsyHLpM0RMCvwkueVYMv18chQVa93SdYlxV5TpdrvlzJLT8HXHNOUQCf/2POYjqCnGMHh7tARZ8/GUxtu3uUs609yzWyTkLxVFVoT0vtGzgskVcRob1UVGqvdmwDjBv6OFsnUPFOtE69vhL2jvcTGfSoeUB49VeQqhfkRQxVl0yDjRIeNU6cS2aJ0bdfi1XzrEjzJ6Bu/eK+7yTJfTyWxJaY/aC1d6hO+vE2LQ9Ws+d1ZViNCLNS37nZc64HK++s7eX1rvhlZHLzh1mT7DqCmk4fo6UfbBNjPc2mJeE4/OyYh0P2hJc2aLLINLm6WWpxUXau9GsO+gNvEjXa+DBp8w6H0kzYA6vQcsxeqyg53zo3fVibN5hlkNcZYDLLuubxFFZKq4FR0TLkbG/Qetb8MU3zOGhLS4tFtehkyW07G0x9PLYyGW9Lqc5vVZqI01X0RQ775HyqOsKl2wPs9Ur9LzD72v36/Q6J48V54TREnztnWi775ozTXu3o03XZWQV0dIi8ZxzkvauC69aa65vwxBjzz4td+iVheWOcuM+b5G2eai74Q1bzcseMWxsR+bP6WzT39ugZQvjcx3V2e6jDUS7gkuBMf1aNl55S680UDhwwiX62AZgOlDGTjhSjC07te3RqyY279TfaD2u3aepTtwLDtf5w7LGb93nnqzpTSzh7bu1jdVLKSPLz7XgcHM9YPnbL8GNlC202SgXzmHlZj17fbVeSu066lC9JBX134Ue9FPGmXWmdp+WA7QHjvJSCaEnCsoH1hl6go8YpttUR1GhuC88ReuGtpvWjje2MVj/kcvGdZ1XV0RTVaCdQ+9PbHuw/jAP7sULdDpCzy43U76gHGLbguV+2BRtE40dtZ2Xf+NqheIi3XZgmxJ4/KVoigNtHxwOcS08WlwzJ3fZjqL+hl56U1zHHCri8UjopZXadmBfAPUktPoDXVZGa5uZ5gHLuaRQPB89Xceh5S7S09dRVqLrEuUg+LDZA98xrEzbD/S8R3lAr3r0Btce3Bj/ijVannWfIj9PXOecKPvGD5fKTXskjG0Vts9NLeKcPE6vlgrhu5i+8aO0DAYfeU4cZUViNLeZl3ZbyxhXeCyaq73jdfuPz6rKo2XbSsGGtlkvpccVDacfpz2j9WqB6gptY7BtsNpYbWf0u06zHkXauejwNO2NR3vLYf8BdQl1RseN32MZ1QzTqxTQvofRjs6bbc7HK29JENsslBeUDWyzK8vEMX6keFA/C/O1fcV3HONG6PLE/g3KTJdtubVdwDYGV7Wg3lSU6rZOyxnmq3a/1nnUFfSE1P2HkkIJYVup63extvm4mgh1Ae2jToc9HVVTi9m2+v3iPuN4cYwZEdlnbDDb1NdWi/v0BWb78NSr0d7njuHDdPtopXqwLsF2nTJPUyZgmVn7AZoyYEetOA+fLp6PLda6Zl7ZYui2EuUr+NBSs63Bdv3shbp8gmj3tu02y35kW67l7f2NOo/WNtQ1fZKWE62qL7+pV3RGtyl4xvYA28ZIuiysO92fqW+SwD+fMtM7aMouj7jPOE73UaLLB1eioH3aWWu2iSveNVOc+LwSXrfZ3IfC9hdXqkwZp1co6T4j2vkp4yTw8FLdJ3AeNVP3V3AFnNZ7TJMGjcx9a8eo4bpskLLQDvsheFhpUHR+Z04220a0r2cer2VTewvbgsnYZmJ4CA5Y84jUIp4LUc48EnjgSe2Ri/KPsod21JpHtAHBR57Vnq4oY9jnsNoLDCP4zGsSWrFax4G6gHbdamOxvxoI+MVbUyWeM47X+cU8uXGcUrdfgv9+XgykJ8I2HPtrC4/WYbnmTDfXW0ur7q+4po7v3C69ukpc82eb25EXViRtfyzR/QtNaeAwjwlQBrGeIqkVdH/7olPNlDqRq1DCK9/Tbbm134dx6L5Rc6vuT+EYAHUAx0Va3rGPedIxepWUppCM1CVHUb64TzWvUrLaM02vgP0jtDtoS0YPN49RLjqtS9pHzGNo9Ydme1peYnZCwno/cqa458/WeQ2/+b55LDFtQuc2ILLPaO6r+3VdWfMYc0z4CPbvD+g+j+6nWCmFsC1CQA3LCldHIqUZtnORfXD3CUfZjk3K9apfLDPsB2CfAcdHKCd6rLF9j77XY9RIb3at35FjRrQf2M6gLYpO094DukzCu/bqvr77xKN0mWldj/SQdc2YZO4/PfmyXg2L9Rh4+Bktu2hro6kvsT527xXnnGnm/nl7hwRwHI7tnO7Xmccsuv9UkK/bD7SvOm5N+xYy2xdsy89ZKO5jD++y3Q1v2yWBfz6t7atuR6w2B+sAV75ifz/SgSy8Z585j9g/GDGssy7hOPC9jea0lBaJ6+R5GjvAVY64ShFXAqIMYf/PQKAPbeyieZqWAu15tLxjexDZX8F7rANNm9Ha3uX4S8tBc6vWTd33PNSMAWFfCek03LhaDeUwQTnTz7GdwxUfkfHhmAX7O7jqMoxj5nyfeCvKxHPx6Z3tT3NbZ7pUa/se2ZZj+WtZKC6U4Atv6P6Kimw/cPyt2xTU8WLzuF2vkt2yU5xHzDB/a0uVE957wDyGRXkaPkyPy42mVl0muo+774Ae3zsj2/OYuod2tMOv+2q4OtA6JsU+hrY/z68w24TIunZWVYj7lHkSWPK01idsW4wdeyT4mFn2Ub61joXDWn9w9R3aYo0bLH3VvBoP8zRpjLaXdtiGapmpO6BXQTpqKs3jy9MWmFdS+7xm/Gd/g9leayrZNrPunHG8eTUrygj2bXGces5J5v4w9lUry7V8Yltu7ftrT/DFx4pz4hhz/Cvf031r1/FHaJ3RY3C3W1wzJmr7hmHjiifrylwtZ5GYFJ47Yw8n63IKrXxXgss7j3t0v2F/g+5ruQ6fbrbZiK9g+4r9Cyx37FsdPt1cr+u36rE8jtlRBlAOPahjOJ7Ftvyhp8xjISxbrMctu6LzqDEz7HM3t5pl/LxFGueJLusPNkvomdf0ikc9Rnj2Nf2ubjPQFnTXGZSy1oCnc6moqJCf//znctFFF0lVVZXcd999+hrWrl0r06dPl2XLlsm8efNSGl4u90Qn6TGITkRDE+s+UW5i3SfKTaz7RLmL9Z+GCpy86vjebfrad/M3Yjr/UFc51xPdLhQKaY/zlpYWmT9/vqxcuVICgYCccsop0e9MmzZNxo4d220QHWlf8LAH0a2FiwflFqxznAfiuifKLaz7RLmJdZ8oN7HuE+Uu1n8aKgzrhr4o1/6AOGzvaXDU/bQH0VevXq1Bc+Q/LyoqkiVLlsiMGTNk1apV4vV6paysLOb7yIe+e3fk8u4EbrzxRrnhhhu6fF5XV6fjoNyCyoSzUXqJIc9KE+UM1n2i3MS6T5SbWPeJchfrPw0Z4bBYEdC9u3eLURCbao0S1/1sqvdpD6IfcsghGjDHjD/00ENyySWXyAsvvNDn4V1zzTVy9dVXx/REHzNmjKaGyXS3fspMpUIeKaz/bKpYRJRerPtEuYl1nyg3se4T5S7WfxpK/JGblQ8rr9B7FlDPdd/nM+8PlhNBdPQ2nzzZvFHVkUceKStWrJBf/epXcvHFF4vf75f6+vqY3uh79uyRmpqapMPDwku0ANGYskHNTahUXP9EuYd1nyg3se4T5SbWfaLcxfpPQwZuyoxULuEwy3Mv6n62cGbiTAJymiOg7vF45Jlnnon+bd26dbJ161ZN/0JEREREREREREQ0JLgiNxMNhjI9JZRtPdGReuWMM87Qm4U2NTXJfffdJ88//7w8+eSTUlpaKpdeeqmmZqmoqNBULF/72tc0gJ7spqJEREREREREREREg447EkQPMYg+GKU1iF5bWyuf/exnZdeuXRo0nzVrlgbQTz31VP37Lbfcot3yL7zwQu2dvnjxYrn99tvTOUlEREREREREREREmQmisyf6oJTWIPqdd97Z7d/z8vLkt7/9rT6IiIiIiIiIiIiIhiKH2yUGXjCIPihlT3Z2IiIiIiIiIiIioiGcE91gOpdBiUF0IiIiIiIiIiIionRiOpdBjUF0IiIiIiIiIiIionRiEH1QYxCdiIiIiIiIiIiIaADSuUgwmOkpoT5gEJ2IiIiIiIiIiIgozTcWhdDrazI9KdQHDKITERERERERERERpVMorE9GQ1Omp4T6gEF0IiIiIiIiIiIiojRyLZpnvjCMTE8K9QGD6ERERERERERERERp5PB6zBe8seigxCA6ERERERERERERUTq5zDCsETbTutDgwiA6ERERERERERERUTq5zBuLsif64MQgOhEREREREREREVE6uSNBdPZEH5QYRCciIiIiIiIiIiJKJ2ckDMue6IMSg+hEREREREREREREaeSweqKH2BN9MGIQnYiIiIiIiIiIiGgAbiwqIfZEH4zckiNCoZAEAoFMTwbZeDwecVk3VSAiIiIiIiIiIhqqrBiYYYgRDovDSu9Cg8KQD6IbhiG7d++W+vr6TE8KJVBWViY1NTXicDgyPSlERERERERERETp7YlupXRhEH1QGfJBdCuAXl1dLQUFBQzWZtHJjdbWVqmtrdX3I0aMyPQkERERERERERERpYc9GwNSuniGfFh2SHEP9RQuVgC9srIy05NDcfLz8/UZgXSsI6Z2ISIiIiIiIiKinOiJToPKkL5uwMqBjh7olJ2sdcN89URERERERERENFRpDnQrQwZvLjroDOkguoUpXLIX1w0REREREREREeUEKwtDkEH0wSYnguhERERERERERERE2ZDSxWA6l0GHQXQiIiIiIiIiIiKidHNHeqIzncugwyD6EPGDH/xA5syZk+nJICIiIiIiIiIiou5uLsqe6INOWoPoN954oxx99NFSXFws1dXVct5558m6detivrNw4ULNi21/fOUrX0nnZBERERERERERERFlJic6e6IPOmkNor/wwgty+eWXy/Lly2Xp0qUSCATktNNOk5aWlpjvXXbZZbJr167o4+abb07nZFEcv9+f6UkgIiIiIiIiIiIa0hzsiT5oudM58CeeeCLm/d1336090leuXCknnHBC9POCggKpqalJaZgdHR36sDQ2NupzOBzWhx3eG4YR8xB/QDLC69Fe9qn4wx/+IDfccINs27ZNnM7O8xzoyV9RUSF//vOf5aabbpJbb71VWltb5aMf/ahUVVXpd3Qee/D5z39e6uvr5aijjpLbb79dfD6fbNy4USZMmCBf+MIX5P3335d///vfUlZWJtdcc42eCLGGjem66667ZM+ePVJZWSkXXnih3HbbbX1eLNZ6SbT+UmGt4778logGL9Z9otzEuk+Um1j3iXIX6z8NNUYkzhcOBFDAMz05WSuchXU/rUH0eA0NDfqMQLDdvffeK3/72980kH7OOefIddddp4H1ZCliEMiNV1dXJ+3t7TGfoec7FnYwGNSH4Q9I+LrfSCY4f3SFOLyelL57/vnny//+7//K008/LSeffLJ+tn//fj0pgeD23//+d10GCF4fe+yxuvx++9vfahAc89kTLJNnnnlGioqK5L///a9+Zv3uF7/4hXz729+Wa6+9Vq8euPLKK2XSpElyyimnyD//+U8N3GNdzZgxQwPp77zzTkrjTAa/xfTs27dPPJ7Ulk/8vKBcoWLZTzgQ0dDGuk+Um1j3iXIT6z5R7mL9p6GmWAxBQpf6ffslWJKX6cnJ+rrvzKJ67x7ImUdAdsGCBXLooYdGP//kJz8p48aNk5EjR2pAFgFc5E1/+OGHEw4HPaOvvvrqmJ7oY8aM0Z7YJSUlMd9FUL2pqUncbrc+jFBYMpW4xO1yicOd2uLGvJxxxhly//33a/ob+Ne//iXDhg3TYPbxxx+vPcaRBgd++tOfynPPPafzi/nsCQpgYWGh3HnnneL1emP+hvXz3e9+V18jUI5UPL/+9a/l9NNPlx07duiJjsWLF2vAe+LEiTJ//nw5GJheTA96tefl5fWpXKGHP5ZZNlUsIkov1n2i3MS6T5SbWPeJchfrPw01AZ9PkEOirLhYnNXVmZ6crK/7Pp9Pci6IjpQga9askZdffjnm8y996UvR14cddpiMGDFCFi1aJBs2bNAe0PGw8BItQDSm8Q0q3ttvWCo+r/huvFKyPZ0LfOpTn9Ig+R133KHze99998nHP/5xcblcmm4FN1+1Dw/BbATSUx0HlnWi5YjhxA8Xvc/x2cc+9jH51a9+pesFQfUzzzxTrxxIJXCfjLVuEq2/3gzjYH5PRIMT6z5RbmLdJ8pNrPtEuYv1n4bijUUdYV5dkWrdzxYDMiVXXHGFPProoxrkHT16dLffnTt3rj6vX7++36dDA7Y+b2YevQigA4LTuFzpscce09zoL730kgbW+wt6ovcWevzjKgHkUc/Pz5f/+Z//0dz2SJtDREREREREREREKdxYNItyfVMWBNERBEYAfcmSJfLss89qzu6erFq1Sp/RIz2XIbXJBRdcoPnOkQP9kEMOkSOOOEL/Nn36dHnttddivo+0K/0hfjh4j/FZEDxHgB/52J9//nlZtmyZrF69ul/GTURERERERERENNR7okswlOkpoWxK54IULkhD8sgjj0hxcbHs3r1bPy8tLdVgLFK24O9IC4Kc2MiJftVVV2nv5lmzZkmuQ8/zs88+W95991359Kc/Hf3861//unzuc5+To446SnOYI9CO7yBH+cF65ZVX5Oabb5bzzjtPbyz64IMPam94uPvuuyUUCunVArjxK24wivWInPZERERERERERETUDbfZn9kIMYg+2KQ1iI583rBw4cKYz++66y4NAuOmlk8//bTm3G5padF0IRdeeKFce+216ZysQePkk0+WiooKTaGCG7BaLr74Yj0B8a1vfUtvJopl9tWvflWefPLJgx7nN77xDXnjjTfkhhtu0Bu1/vKXv9QbiUJZWZncdNNNemNXBNORV/0///mPngAhIiIiIiIiIiKibjgjPdFDTOcy2LjTnc6lOwiav/DCC+mchEENyfN37tyZ8G/f/e539WH3s5/9LKXhokd5MgicP/DAAwn/ht7peBAREREREREREVHfeqILe6IPOtlzi1MiIiIiIiIiIiKioZ4TnT3RBx0G0YegoqKipI+XXnop05NHRERERERERESUcxzRIDp7og82aU3nQpmxatWqpH8bNWpU0r9t3rw5TVNERERERERERESU41xWOhf2RB9sGEQfgiZPnpzpSSAiIiIiIiIiIiK7SE90gz3RBx2mcyEiIiIiIiIiIiIaqJ7oQQbRBxsG0YmIiIiIiIiIiIjSzcqJHmY6l8GGQXQiIiIiIiIiIiKidGNP9EGLQXQiIiIiIiIiIiKiNHO4Iz3ReWPRQYdBdCIiIiIiIiIiIqJ0c1pBdPZEH2wYRB9kPve5z8l5552X6ckgIiIiIiIiIiKi3nCbodjQijWZnhLqJQbRiYiIiIiIiIiIiNLMUV4afW00tWR0Wqh3GEQnIiIiIiIiIiIiSjPnoVOirw1/IKPTQr2TU0F0wzAkEGzLyAPj7o2HHnpIDjvsMMnPz5fKyko55ZRTpKWl8wzVL37xCxkxYoT+7fLLL5dAgBWPiIiIiIiIiIgoWzmcDpGCPPNNIJjpyaFecEsOCYba5c77F2Rk3Jde/Ip43PkpfXfXrl3yiU98Qm6++WY5//zzpampSV566aVoIP65557TADqe169fLxdffLHMmTNHLrvssjTPBREREREREREREfWZJxKOZRB9UMmpIPpggSB6MBiUCy64QMaNG6efoVe6pby8XH7zm9+Iy+WSadOmyVlnnSXPPPMMg+hERERERERERERZzOFxi3aTDTKIPpjkVBDd7crTHuGZGneqZs+eLYsWLdLA+eLFi+W0006Tiy66SIPnMHPmTA2gW9ArffXq1WmZbiIiIiIiIiIiIuonHo8+GeyJPqjkVBDd4XCknFIlkxAgX7p0qbz66qvy1FNPya9//Wv53ve+J6+99pr+3ROpbPb5CofDGZpaIiIiIiIiIiIiSgnTuQxKOXVj0cEEgfEFCxbIDTfcIG+99ZZ4vV5ZsmRJpieLiIiIiIiIiIiI+srtSimIHvAbsvXDoGxYExB/h3mfRMqcnOqJPligxzlynCONS3V1tb6vq6uT6dOnyzvvvJPpySMiIiIiIiIiIqI+cHg8Zk70JEF0wzBkzWsBefe1gIQiX6ka5RKvzzGg00mxGETPQiUlJfLiiy/KrbfeKo2NjXpz0f/7v/+TM844Q+6///5MTx4REREREREREREdRDoXIxCI+TgcMuTDd4KydmVAmhvMnucOp0jNWJe4GcHNOK6CLIQe50888UTCv919991dPkOwnYiIiIiIiIiIiAZLTvSQPnW0GfLOq37ZvDYo/naJBs8Pm+eRGUd7xOVmD/RswCA6ERERERERERER0QBweNyazmXnmkbZ2twu2z4ISThs/s3jE5kyyyNTZrulqJS3sswmaV0bN954oxx99NFSXFysub3PO+88WbduXcx32tvb5fLLL5fKykopKiqSCy+8UPbs2ZPOySIiIiIiIiIiIiIaUMh3vjU8SpZWf0Jeap8rW9aaAfQCR5scWbVRzrssXw4/wcsAehZK6xp54YUXNEC+fPlyWbp0qQQCAb1ZZktLS/Q7V111lfznP/+RBx98UL+/c+dOueCCC9I5WUREREREREREREQDxt9hyIuPdMjyXZOkyVMhTiMoI9s2ytx9T8jp2/8s41c9Lu79+zI9mZSJdC7xeb2Rzxs90leuXCknnHCCNDQ0yJ133in33XefnHzyyfqdu+66S3OCI/A+b968dE4eERERERERERERUVrtrw3JU39vl1DQfD96vMjs4XukOA/9m6dJYMl2kbYOkZCZJ51yPCc6guZQUVGhzwimo3f6KaecEv3OtGnTZOzYsbJs2bKEQfSOjg59WBobG/U5HA7rww7vcZmE9aDsY62bROsvFdY67stviWjwYt0nyk2s+0S5iXWfKHex/tNQcKA2LEvv74gG0Oee5pGJMxGSndz5pcde0CC6lnWWd8nGuj9gQXTM9JVXXikLFiyQQw89VD/bvXu3eL1eKSsri/nu8OHD9W/J8qzfcMMNXT6vq6vT/Op2CNBjvMFgUB+UfbBesI727dsnHo+n17/Hb3FyBhXL6WS+KKJcwbpPlJtY94lyE+s+Ue5i/afBrr7WJSuXFuhrp8uQI09tlaLKsNTWxn6vJBzWnNsH9u2XkJdlPRyp+9lU7wcsiI7c6GvWrJGXX375oIZzzTXXyNVXXx3TE33MmDFSVVUlJSUlMd9FUL2pqUncbrc+KPtgvaBC4MayeXl5fapUDodD1382VSwiSi/WfaLcxLpPlJtY94lyF+s/DWZbPwjJyqX+6PtFH82TYSPMgHo8v8ulz+Xl5eKsrpZcF47UfZ/PJ9liQCLLV1xxhTz66KPy4osvyujRo6Of19TUiN/vl/r6+pje6Hv27NG/JYKFl2gBojGNb1DxHgvcelD2sdZNovXXm2EczO+JaHBi3SfKTaz7RLmJdZ8od7H+02C0c1NQXnmsM4B+1iX5UjasmzIciVs6HSzr8XU/W6R1SnC5DQLoS5YskWeffVYmTJgQ8/cjjzxSU3g888wz0c/WrVsnW7dulfnz56dz0oiIiIiIiIiIiIj61frVAXnuYfN+jkWlDvn41wu6D6DH4D0ds5U73Slc7rvvPnnkkUekuLg4mue8tLRU8vPz9fnSSy/V9Cy42SjSsXzta1/TAHqim4oSERERERERERERZZtw2JDXl/plwxrzvozePJGTL8oTl9uRUq9rhs+zW1p7ot9xxx2aBH7hwoUyYsSI6OP++++PfueWW26Rs88+Wy688EI54YQTNI3Lww8/nM7Jyjnjx4+XW2+9NdOTQURERERERERENOQE/IY8eldbNIA+aqJLLvhKgRSX9TL0ykh6bvZERzqXnuBmkr/97W/1QURERERERERERDSYrHjGL031Zhx00qFumXuat4/3Z2QUPVtlT3b2AYCgflswmJFHKicU4u9Ce+ONN2oeeaS+mT17tjz00EP6t6OOOkp+8YtfRL973nnnaW755uZmfb99+3atqOvXr9erALZs2SJXXXUVb7BKRERERERERETUj3ZvCcmm98we6IfO88i8xb7ex9+s7zOGnrXS2hM927SHQrLw352pZAbS8x+5WPLdqS9uBND/9re/ye9+9zuZMmWKvPjii/LpT39aqqqq5MQTT5Tnn39evvnNb2pw/qWXXpKysjJ5+eWX5fTTT5cXXnhBRo0aJZMnT9bUOAjAf+lLX5LLLrssrfNIRERERERERESUK4IBQ555qF1fV41yyuwF3oMcIqPo2SqnguiDRUdHh/z0pz+Vp59+Wm+yChMnTtQg+e9//3v5xCc+IXfeeaeEQiFZs2aNeL1eufjiizWwjiA6nhFoB9yw1eVy6Y1dkW+eiIiIiIiIiIiIDt6Lj3REXx918kEE0K2O64yhZ62cCqLnuVzaIzxT404V0rC0trbKqaeeGvO53++Xww8/XI4//nhpamqSt956S1599VUNmCNty0033aTfQ0/0//f//l+/zwMRERERERERERGJ+DsM2bM9pK9nLfBIRXXqsb8uoulfGEXPVjkVREc+ot6kVMkUK7f5Y489pmlZ7Hw+n6ZuQYoW9DhftmyZBttPOOEE7Y3+wQcfyIcffhjtiU5ERERERERERET9641n/RIOiZRUOOSweQebxiWCMfSslf0R5Rw0Y8YMDZZv3bo1aTAcnz/33HPy+uuvy09+8hNN2zJ9+nR9PWLECJk6dWr0u0j3gtQvREREREREREREdHDaWsLRm4mOmdIP4dXojUUZRc9WzkxPAHWF/OW4aehVV10l99xzj2zYsEHefPNN+fWvf63vAelbnnzySXG73TJt2rToZ/fee2+XwPv48eP1xqQ7duyQvXv3ZmSeiIiIiIiIiIiIhoKl95s3E4WZR3v6bbgMoWcvBtGz1I9+9CO57rrr5MYbb9Qe5rhhKNK7TJgwQf+OvOjhcDgmYI4gOnqc49nuhz/8oWzevFkmTZokVVVVAz4vREREREREREREQ0FLY1iaDpjh7mNO9YrHZ+UzPwjsiZ71mM4li/O3f/3rX9dHIkjfgiC63XnnnSdGgso2b948efvtt9M2rURERERERERERLngnVcD+lw1yilTZvVfL3TKbuyJTkRERERERERERNSDhn1h2fiumQt99KR+7Jts9USnrMUgOhEREREREREREVE3wmFDHrunLfp+8mFpSPDBbC5Zi0F0IiIiIiIiIiIiom68+t+OaMryky/ME29eOnqPM4qerRhEJyIiIiIiIiIiIkqirTksWz8I6etpR7plxHhX/44gemPR/h0s9R8G0YmIiIiIiIiIiIiSpHF5+sF27YVeWOKQI070pnFsjKJnKwbRiYiIiIiIiIiIiOIYhiEv/KtDGvebwe1Zx3rEkY6bgLInetZjEJ2IiIiIiIiIiIgoTt2OsOzcZKZxGTXRJRNmpOFmomDF5a2k65R1GEQnIiIiIiIiIiIismltCsvS+9v1dfVop5x4ni89vdBpUGAQnYiIiIiIiIiIiCgiGDDkkT+1Rd/PPS3NAfRoOhf2RM9WDKJnoYULF8qVV17Zq99s3rxZK/OqVatkoGB8//rXvwZsfEREREREREREROm2Y2NIwmHz9cLzfVJSzhBqrktTIh8aaGPGjJFdu3bJsGHDBmycGF95efmAjY+IiIiIiIiIiCidQkFDlj3Roa+nzHbLqIkDED5lmpisxyD6EOD3+8Xr9UpNTc2Ajnegx0dERERERERERJROG98LSihovp4ya4BDp0znkrVy6loEwzA0p1EmHhh3bwSDQbniiiuktLRUe5dfd9110WGMHz9efvSjH8lnP/tZKSkpkS996Utd0rk8//zz+v6ZZ56Ro446SgoKCuTYY4+VdevWxYznkUcekSOOOELy8vJk4sSJcsMNN+i4e5vOxRr/ww8/LCeddJKOb/bs2bJs2bJezTcREREREREREVEmIPb2+lK/vi6tdEh5tWtgRsye6FkvradTXnzxRfn5z38uK1eu1NQfS5YskfPOOy/698997nNyzz33xPxm8eLF8sQTT6RlenAW6f7bWiUTLv7fAnF7Uv8+lsull14qr7/+urzxxhsaKB87dqxcdtll+vdf/OIX8v3vf1+uv/76bofzve99T/7v//5Pqqqq5Ctf+Yp84QtfkFdeeUX/9tJLL2kg/rbbbpPjjz9eNmzYoOOBnobb3fgwbVOmTNHXn/jEJ2T9+vXidvOiByIiIiIiIiIiyl57tkYSoYvIUSf7Bn4C2BM9a6U1stnS0qK9kRG4veCCCxJ+5/TTT5e77ror+t7ny0ABzdIc57fccov27j7kkENk9erV+t4Kop988snyjW98I/p99ARP5Cc/+YmceOKJ+vo73/mOnHXWWdLe3q49z9HrHJ9dcskl+nf0REcP929961t9DqJ/85vf1HEAhj9z5kwNok+bNq1PwyMiIiIiIiIiIhoIH7wd0GenS2T4mJxK4EGZDKKfccYZ+ugOguYDlVvb5TZ7hGcCxt0b8+bN0wC6Zf78+dqjPBQK6XukaEnFrFmzoq9HjBihz7W1tdqr/e2339Ze6Qi0WzB8BNlbW1s1JUtvJRsfg+hERERERERERJSt2lsN2fahGXebvcATE5dLO2tc7IietTKeYwO5u6urq6W8vFx7V//4xz+WysrKpN/v6OjQh6WxsVGfw+GwPuzwHrmMrEdfgtn9qTd50e3TbP+t9YwAd7K/23+LNCrx40WgHJ81NzfLD37wg4RXCeDkRirT25vxdff7ROsvFdY67stviWjwYt0nyk2s+0S5iXWfKHex/tNA2rvLDKDDhBmujJQ7o4/xsaEmnIV1P6NBdKRyQQB3woQJmo/7u9/9rvZcx80oXa7EiftvvPFGTRMSr66uTntQ2wUCAV3YuFFmqjfLzAYoJK+99lrMNL/66qsyefLkaDDami+L9dqaV6vHun3e479z+OGHy9q1a/VGpfFSDWpjPPHjiB+f9Z1E8DnGs2/fPvF4epE03jadDQ0NulycTl5mQ5QrWPeJchPrPlFuYt0nyl2s/zSQtm30olup1IwPSENTk0jTwI27KBjQIG1DQ70Eamsl14UjdT+b6n1Gg+gf//jHo68PO+wwTQUyadIk7Z2+aNGihL+55ppr5Oqrr47piY784bhxZklJScx3EVRvamrS3tGD6caWuFxk27Ztmpv8y1/+srz55pty++236w07rflAIbLPk/XamlfrJIR93uO/gxuTnnPOOTJu3Di56KKLdJhI8bJmzRq9IiAVGE/8OOLHZ30nEXyO8eLqA+Rp70ulwvLC+s+mikVE6cW6T5SbWPeJchPrPlHuYv2ngfR+EzJfhGXE2Hypro6NMaZbwOPVTC6lJaXirK6WXBeO1P1sundmVkWWcWPLYcOG6Y0okwXRsfASLUA0pvENKt5jgVuPweSzn/2sngSYO3euBqG//vWva0Ddmo/4eYr/PNH34j/DlQCPPvqo/PCHP5Sbb75Ze4Ijd/kXv/jFlJdXb8bX3e8Trb9UHezviWhwYt0nyk2s+0S5iXWfKHex/tNA2L8nJDs3mRkZKmvMDp8DyhZDY1mXrFwWWRVE3759u6b1sG5ImavQE99yxx13dPn75s2bu3yGlCz2vOMLFy7skod8zpw5XT5bvHixPvrCPqz48UNZWVmv8sATERERERERERENtDXLA9HXlTUZCNxafU/t9z9sbpXAP/4rRmOLeD9/vjjKB7Z3PA1gEB03rkSvcsumTZtk1apVUlFRoQ/kNr/wwgulpqZGc6IjfQnyfvc1qEtERERERERERESUqgN1Ydm23ry34LzTvOL2ZEc2i/DaTRJ+b6O+NoLBaJydMiOtp1beeOMNvXklHoBc5niNXNxIUfLOO+/IRz7yEZk6dapceumlcuSRR8pLL72UVfluctW9994rRUVFCR8zZ87M9OQREREREREREREdtI1rOnuhj5maoaQdCdIgG4HIdLlc4igpGvhpohhpLRmJUorYPfnkk+kcPR0EnNxAPvZEkDudiIiIiIiIiIhoMNuzPSRr3wzq6xPP84nXl5n+3hirRlDtcdSg2Tveedhkcfi8GZkuytKc6JQ9iouL9UFERERERERERDTUBAOGPH1/e/T9yPGuzE1Mgp7oVhBd3AzfZoOcWAu8uWX24rohIiIiIiIiIqKB0NFmyLb1QQkFzSC6ZfEn88TpymDW8QQ3FpWQGUR3uDJwo1PKrSC6lXaktbVV8vPzMz05lADWDTBFDBERERERERERpdPyJztk+4ZID++IsVNdMmxEBnuh29li6AZ7omeVIb0WcPPSsrIyqa2t1fcFBQXiSHR5BGWkBzoC6Fg3WEdYV0REREREREREROmAnufxAfSiMofMP90nmRftit75UTSIzphZNhjSQXSoqanRZyuQTtkFAXRrHREREREREREREaXDqpf9+ux0iVz0PwWyf09YSiud4vY4sjKGLkHzhqfCjqdZYcgH0dHzfMSIEVJdXS2BQCDTk0M2SOHCHuhERERERERERJRuTfVmhHrYCKd4vA4ZPiabYlIJAvmhsPnMnuhZYcgH0S0I1jJgS0RERERERERElHvamswg+oxjsvC+fIluLBrpie5gED0r8PauRERERERERERENKTzoR+oM3t2FxRlYzi0a0/0zhuLMoieDbKx1BARERERERERERH1i43vRvKLi0hhSRbkQI+XaJJCkSA6M2tkhZxJ50JEREREQ58/0CLbdr4qXk+hVJYfIgX5lZmeJCIiIiLKsJZGM01KfpFDvL4sDKJHouhGTDoX9kTPJgyiExEREdGggQOLxuYdUlxUI/vr10tJ0WgJBFslEGiRPF+5PPDYx6StfV/0++NHnySzp39GiotGaI7JhqatepBSVTldHOIUj6cgo/NDREREROnX0WYGp6fOztJQaKK4fiSI7nBn6TTnGK4FIiIiIsp6hhGW7buWy/Ov/Vha23an/LvN25/TRyIOh0uOP/rbMmbkAsn3lYvbndePU0xERERE2RZE9+ZnYy/0JDcWjaZzYTbubMAgOhERERFlnQ5/kzz10rekIK9Cg9sbtz4rHf6GlH47bdK5Mrpmnqz54H7ZXbcq6fcMIyQvvv5TfZ3nK5NFx/5YRtUcI00tu6SkaJQ4HDxgISIiIhpKQfS8bA2iW1F0Www9/OHWSPSW4dtswLVARERERFnnqRe/KTv2rEjpu+UlE+SMhb+S2v3vSUFepYwcfqR+Pm70CbJ3/zopLR4j769/WKorZ8roEfPkrXfvkn0HPpANW5dGh9HeUS+PPXdF9H115aFywjHf1bzqDke2HmwRERERUSo62s3otC9bg+jRyTKnM7x7b+ff8n0ZmSSKxSA6EREREWWN5W/9Sla9d0/MZ6XFY2Xe4VdKdeVhsn9/vYRll+w98L7Mmfk5aW+vl/y8cnE63VJSPDrmdx53voyonqOvjzzssujnRxx6qT5P2nqarNv4H9lV+6b+dt+BD7V3OtTuWyMPPf5JDaafctxNUlw4gsF0IiIiosGeziUvy/fnrJ7oLW3Rj5wTx2RscqgTg+hERERElHEIYH+w6VF5+/2/Rj9DYPyCxX+VyvKpGsAOh8PicQekuvpYGTf6OP1OYUFVn8c5cewifViaW/fIzj1v6M1Hd9e9Izt2v6bB9PseOVsqy6bIR079k/i8xQc5p0REREQ00Dem97ebr/PyRbI8Kbr5r3VT0RFV4mBO9KzAIDoRERERZfSgZsXbt8ub794Z8/ns6Z+RQw/5uPYAHyhFBcNl6oSzou8/3PS4rFj9O2ls2ib76j+Uvz68WMaMnC+Tx58hk8aeMmDTRURERER9hwC6db/O7L+xaOQ5GDSf3a5MTRHFYRCdiIiIiDIGN/6MD6AfPvMLMndOZ37yTJky4Qx9rN3wL3n97Tukta1ONm17Th8fjj5R8nzl2pN97MgFmZ5UIiIiIuohlYvbK+JyDXwQvbllt+zY84a4nB6pqT5cigqqe46ih8LmM4PoWYNBdCIiIiLKiECwTf7z9Jej73ETz4K8Cpk++YI+D7OtOSx7d5mP9lZDH61NYQmHRZDS3OF06CGKpjfX9ziYEikqdUpxuUOKy5xSXOaQgmKn5BWYBzPTJp0nU8afJTv2vC6vrfqN7DuwTjZvf0H/hgD7xLGnaiB96oQzNQUNEREREWWPxgNmQDovA73Qce+dR5Z+Mfq+qKBGPnnuf8TpjAuOx09apCe6g0H0rMG9fCIiIiIacIYRliVPXiJhwzxAOHHudTJ98vm9HIYhTQcM2bk5JPt2h6RuR1haGo2efpXw09rtkd4+NkWlDg2klw5zSkW1UypHzJOLzjhWA+i4IeneA2uluWWXbNy6VB8r3rlDRg0/SkaPmK8BdSIiIiLKvDXLA/rs8Q1cEH3P3tXy1rt3yebtz8d83ty6W1au+aMcPesrcb9wxOyqWjnRxcXQbbbgmiAiIiKiAbfi7Ttkf/36aPqWQyZ+JKXfIZ/l1g9CsnZlhxyoC0s4cnwRH/yuHu2SknKH+PIdUlDi0Et38VvNh2kY0dd4BP0iTQ1hDcg31eM5LB1tIs0Nhj7Qq31DzPCPkcqaeTJhnENaQitkY+3vZe+Bd6WldY98sOkxfWzY8pTUVM2RCWMWSmHBcPG4s/YuVkRERERDWjBoRqZrxg5Mr2509Hjmle9JY/N2fY99wanjz5S33rtL32/a+mzXIHqXnuiRnVw3byqaLRhEJyIiIqIBteKd30XzoB8+4/Mp5z/fvTUky58qlLYmf/QzpGWpHOGU6lEuqRrllMoal+QXOvoldyaC9EgHs39P2OzpvjMsRtgKrodkyzqdA3F7ficjitok7NomB9pekZD3PdmybYVs2fGivLbqNvF6imTRgp/KiOrDxespPOhpIyIiIqLUtTWbQfSJM9MfBn397dvlzTXYzzXHefbJd8iommPE4XDIuNEnyL+e+ry0tu/r+kPNNRjpMQIhK4jOdC7ZgkF0IiIiIhowuKnSytV/0Nd5vjKZM/Nz3X4/FDLkg1VB+fDtgPYUFzF740w+zC1TD/dISYXZy7y/oQe71Vtp/LTItAQNDa437DekbntI9u42g+v+dpHmA+hpPlV8MlWkBd8Oi3h3iN/9qoTcG+SJp28Wce+U0SPnSyDQInm+Ujlx7vclP6+836ediIiIiDr337CvBvlFjrTfQPTNNX+Kvp849hQZPWJu9H1J0Sh9bu+ol3A41DUvuj3zYLQnOkO32SKta+LFF1+Un//857Jy5UrZtWuXLFmyRM4777yYyxuuv/56+eMf/yj19fWyYMECueOOO2TKlCnpnCwiIiIiyoAt21+Ux1+4Ul+Xl06U8xffrb20k9mxMSgvP9ah6VYs1WMDMveUIikpH/gDCpcbNxzFQ2TEOPOgJxw287I37A9L4/6wNOwNy64tIeloc4r4x4jXf3H092HnXtl74AMJed6XsHuzPN3yOzlk6gkyeeJc3pCUiIiIKA1WLzPzoSNe7fWlbzwNTVvl7//ujHl+9oKlkp9XEfMddCAxGdLhb4ztTOGIi6JHgugOF3uiZ4u07q23tLTI7Nmz5Qtf+IJccMEFXf5+8803y2233Sb33HOPTJgwQa677jpZvHixvPfee5KXl5fOSSMiIiKiAfbqm7dEX8+d87WkAfSGfWF59/WAbHrPvOkozDrWIxNnuqSptUmKSkskWzidDimtxCM2X2VzA3qph2XPNvOmp/v3GOIMDxNnxzBxdxyr32msF1nxvsgbnp1SUZ0v7ca7MqyqQsaNnyJllT4pLHXo8ImIiIiob+p2msHo8iqnplRJl5ff+Hn09aJjfyIF+ZVdvoNOEz5vqXT4G6StfX/cFYlxNxZlOpfcCqKfccYZ+kgEvdBvvfVWufbaa+Xcc8/Vz/7yl7/I8OHD5V//+pd8/OMfT/i7jo4OfVgaGxv1ORwO64NyC9Y5yhLXPVFuYd0nGlxQX1eu+YM0NG3R96OGHyOjhs/rUof97Ya89Khfard1fj58rFMWnOnV9Cr4fmPL4Kj76K1eUOyUMVMQXPdEc6wjv3r9XkPqdu+VtiavhEN5IoFK2bcDvzpaWnaKbHkb89cmDochRaVOKSxxaEC9rNIpxeUO8eY5pKDIIb4CM4g/mMtFMCB6c1gcJ4ZDhgQ6Om/4aj4MzUMPWO0Bv3lZNn6D3+JYGA98F3/HMFqbUEZEPF6H3kjMCHWmF9Un6wazVtpRvI9OlPlZwG/efLa3nE4RdBgL2KbNoq/xmcQ+4x/8zeU25wHj7fxb3GtrePHDiY6k6/fdXocEOoyE04rL2kNB0dz//cnfITpOa3qjyyLyjCCGw2leoR4Mmt+1qnV8fMNav35/nni9HTHDjP1izFPMgnEkeRGzfpzm+0Q3K7brWi4SLLu4j1Jauj39JoWB9KXMpvKb+O+gTqLcuL1dv9tTfKq38auUvh9fZnr4e49/TmEA3U0XertGb2TdB0bYkNZI/mZHpH3orDvma9Rft8dcF1bbiM9QjpNNX7JpTlaXYl4mW8bxDVB8vYpMb3N9ONI+mY1UwrYBV3x5zKu+YheIIe3teSLhdi131qh0WLZxxIzb3jbahm3VcbRRWM4Op7l8sdywOXVFlqnbY/7aXIfmRiJmdVrv7duS2EmOPmNbhW0KM3JkBtbn/lpzhcw9zZOWfcgdu1+Xd9b+TbbtelXfnzz/xzJp3OKk40LgHEH01rZ9UlYyoes0G2Z80wiYBd5wOQfFvm8uHPNnrBpv2rRJdu/eLaecckr0s9LSUpk7d64sW7YsaRD9xhtvlBtuuKHL53V1ddLeHklyRDkDlamhoUErlhN7DUSUE1j3iQYPwwjJ66tvlF115oFFccEYOfrQH8i+fQdivudvd8hL/+zsmV5SGZJRkwMyYmJAGppEpGnw131nnsiwceZjsu6Gh2X7zpflrVVLxBkcL85QjThCI8QZGi3O4GgRwydN9YY+TF2jey63Id48Q1weQ197vDj4N99rUEAfeG/7UVxAOf61Pbhs/1soiBMZVqDZEROA1iB20GHeA8sWBJfI9xD4wDoOhx16QGv/O1FqUIiz50CaaGD07wmuwTs/ngS/7Y9lM9SWLyVTPjwo7cEm6ajt3+HurH1F3nj35xIOm7kHRw0/UUoKjpDa2uQjcjnNm8zvqd0sbsfY6OcF7e2Cc5PNTZjOWslrbBLk6Gjt6JD2boY3VIUj+/3ZtM+fsSA6AuiAnud2eG/9LZFrrrlGrr766pie6GPGjJGqqiopKcmeS3tp4CoVerJg/WdTxSKi9GLdJxoccMOk/zxzmezZ+46+Ly0eJxeefq+43bFp+5BX/N93dnaGWHiBN5pzfKjX/erq02XixOlS37hJaqoOl8ambbJ11yvy5uo/iQPpX4KjxRkaJY7QKPGGp4lbxotTiiTQZi5DBLbbmgd/IBo9AdF7EycBnK5Iz0tb72Drgd7l6LGN72ovzEggX7/ndGhPQvTQx/famg3x+Bw6zMQ9wON7h5v9Gs3e273vMQvoDRoMGJJfGDnJYO+hGN8DvktvRZwcMq8u6OwZbyTu7dhNr0h8p6PdEJ/PPKmB1wWF8V3Wzd7fHe3mssOVDf15hTvWD5ZBdJrtJ1asky4hQ3uhY33qerVV+fhenXrvgaYmKS4ujr0U336iJ+ZF8l7AXT6PvA+Fzase3PG9YKGfe1f3Ww/rpB/2cjy9/I155YRDy2x3etsTu8fv99zpv8eYaL9PU4Jx4goYs1d43ytVPq40QlmMOSlpSDhSn3BCEz2ctc1wmfXIOkGZyrQnqwfx7Uq3v+nu88hnmN6gH1dVOfSqmPi2CifFrWFYVzhYGSw6hx+W5uYWKSsvkvwCZ+yVQ7ZhWQOyvez8XqRHOGBZoad59IoBXW62K5yc5nQkvAoovn4k2qbY/xzZdnnzzRPIlBnYvlSPzhOnq7hfh7ur9k15ffVP9LXbna890MeMmC8uV4LLdGxKiofLvvp3xeMLSXV1dfTzYH6eniouKiqS0upqCfp8+r6gpFhKbN/LFeHIfr/Pl8ZE9r006C4owcJLtABxIDVUDqaod1CpuP6Jcg/rPlH22757WTSAPmHMSXLqcTeLE0etcVYv80tbs/n6xHN9MmqCO6fqfkXZBH1AQX651FTPkoljTpJX3/yl1O5dI8Hw2xpEsN1fVaQEEeQ8cYRLZMLI8yXfWyMVJbPE6xquKVEQ3DLTm0TSpMT8OHFakJjPba+tQDOCDgg+2IPampbD+rsbl8JHLo2PXB6vf9IUJw7JK4wEwCOBcU1HYAXFB3FaGhqYA+na2qBUV3uGVN0nolTrfz3rP2WV+sat8p9nvhR9/6lz/9PlJqLJWN/r8NfHlunIjhdO6evnrW363ulx52zZd0T2+yXXg+g1NTX6vGfPHhkxYkT0c7yfM2dOpiaLiIiIiPrJ5m3P6/PwYbPltON/0eVmTsgR+vrTHbL5fbPb2cy5Hhk9edD18UiLYRXT5COn/EFfI4D+wms/0htQBYJtUt+4RcLhgLR31IvhbJKNtb+J+e3hMz8vhSVVkuct0R5RsTetIiIiIqK+2rlnpfz76S9FA+JnnvTrlAPo+hufuV+26t175OhZX7X9pXM/GTcVDa9aZ77hjUWzRsaOUiZMmKCB9GeeeSYaNEdqltdee02++lV7ISIiIiKiwaahaZu8t/6f+vrwmZ/rEkBvbgzLU39v15QbUFzmkOlH2RN3k8XhcMrCedd3+by5tVbeevfPGmRHj/W9B9bq52+9e1fM94oKaqSm+nDxeYvl0Kkf05tYxa8PIiIiIkoOHRmeX/YD2bB1afSzhfN+IFUV03s1nNLiMfocCvulrf1AZ2eH6K6ZIdLWEf2+c9rEfph6yvogenNzs6xfvz7mZqKrVq2SiooKGTt2rFx55ZXy4x//WKZMmaJB9euuu05Gjhwp5513Xjoni4iIiIjS7M01d0Zfj6g+IuZvu7eE5JmHOnOgzznOI9OPxmXaDOz2RlFBtRx/9Hei7/c3bJSXXvuJdASaxOct1VydOBBrbt0t6zc/rt9594MHxOlwy8ypH5UFR/2/DE49ERER0eDx4ms/jgmgn33yHTJ6xNxeD2fSuMXy7LLv6+sOf5MtiB7ZD7bu2B7hGF550NNOgyCI/sYbb8hJJ50UfW/dEPSSSy6Ru+++W771rW9JS0uLfOlLX5L6+no57rjj5IknnpC8vNibTRERERHR4IF0I+s2/ltfz53zv9oD2tLRZshzSzoD6Gd+Jk/Kq3mZan+oKJ0o557WefLCH2iRVe/dI5u2PSflpRNk49an9fOwEZTV6/6uj4rSyeLzlYrL5ZHKsqni8RTIlPFnRHtJtbTW6cGd08k0O0RERJSbave9Jx9GOiTA8Ud/t08BdMA+V2HBcGlp3SOBQOSmQHYIoIcid6J14l4z7GSSLdK6N7xw4cLo3ZYTQUH44Q9/qA8iIiIiGhq27VoWfT1jyoXR19gvfPXxDr3ZJZz9uXwprcyemwUNNV5PoRwz+3/0YR0A7q//UJa/9SvNpw77GzqvGt2+a7k+v/HO7yQ/r1I87nxpbN6uPdenTjxHA+vI1V5WMk7/lucry9CcEREREQ0MpM3711Ofi77/3EXPSZ6v9KD30VoiHR6iHA4x8B/iqFYsFXdnp6zBLiVERERE1K/27F2tz0fN+kpML/StH4Rk5yYzgj7/dC8D6AOsunKGPiaNO03qGzaJy52nvaCamnfI2g3mlQNI/dLatlfa2vdJW+R36Lm+dsOSLsMbO/I4mTTuVAkEWjRAX5BfKTt2r5C9B9ZJYUG1TB53mhQXjpKxI4+V4qKRAzy3RERERAfPvKF7UF+fe+qfDjqAbgXRxRZED4UCsjz8mHw4+w2p2v+2nBX6rflFF/eVswmD6ERERETUb0IhfzT/9vBhs6KfNzeE5eVHzZsklVY6ZPw07oZmCnqRV1XOiKaAgRlTLtJn9H5qatkptfvelabmnRpA33/gQykqrNGDSHze4W/Qg8mtO1/WRyLNLbs0lUyiwPuommNk6oSzNK9odeWhUlxYI7tq39Lc+dG8oERERERZYFftSn0ePmx2l/v89JUnEkS30rm8sfr38p4s0yjtTv9aCQZazS8ylUtW4dELEREREaXhhqIOqa6cGQ3MPvOgmQfd6RQ59eJ8cbp4UJCNkG6xpGiUPrqzu+5tWfXe3RIMdYjL6ZXm1j1yoH6DBt3zfOUaaMf6t65KsFiB92Vv/jL6GdLF4HduV54sWvATvWx61PCjxeF06WeOyKXM+NyQsLicnjTNPREREVGnxuYd8uLrP9XXo0cc02/D9XqK9NkfaJZXV/5S3ln7t5i/G+HOnOiUPRhEJyIiIqJ+8eHmJ2Tlmj/q63Gjjo+mctn0XlCaG8zcjkcs9IovnwcEg11N1Ww5/cRbYj7r6GiUsBGK6U2OEygHGjfJ6rX3yvvrl+hBIwLv4XAg+h0E0CEYapcnX/xG0nE6nR69wemMyRfoe4fDpT3k833lenNU9LDHuAvyqyQQaNX0MkRERER9gf2VR5817ysDMyabV+31Byudy8tv3Bz9rFJGyD7ZZb4xrCA607lkEwbRiYiIiOigdfib5JlXvquvccPJRQt+rK9DIUOWPeHX1/lFDpk6h7ufQ5XPV5KwZztSxpw49zp9WD3K6xs3i8uVJ+0dB7Qn+/bdr2kaIATKDzRs1J5Z8RB4x+OdtfemND1jRsyXosIR0tpWp+MaVn6IeDwFUlRQrePJz6uQ5pbd+j3cQNXtzpN1Gx+VsSMX6EkCTKfVC56IiIhyB3KVP/T4J6WxaZu+P+GY70lhQVW/Db+kaEzM+8L8ajnH/xW5O3S9vg8Hg6J7IAyiZxUexRBRTmppq9MgT39cEh4KB/QmbEUFNRos6PXvQ37tuYeAQWF+lfbaw3CaWnbp65Kikfrc3lEvbpdP3O786HhSPcDHTgB66CX67v6GjXqmu7x0Usz0t7UfwBg0JQOWFV7j95iWQLC1y/AQQMNdxBMFUXoL84XxYnrC4ZDe+A6BkL4s30SQdsDnLdF5SG16sBzMO6U7na7o51hnHnehGEYoMr1OXU+42UyiZY3eDID1iOWLG/iVFI2OufGifb2ay7pFAoG2hDttWObWb7GckCIB75FjuKF5u97sr6J0kgaNrO+Ewn4JhwIaLLLKr8ddoPOAHMc79ryhz8MqDtF5qSibHFNPzGVhBsbwOhhsi5bJROWxrX2/Dh/js8p2vECwTcdVVDCcAatB7OUVP9NnBEQ/dd5j0fq1Y4N5I1E48Vxfv9VjGrxQz8sjudixjYPK8ikye/qno23Vlh0valvZ2LxT3lj9O20jaqoO1yscdux+XctXQ9NW2Ve/Xod1oGFDl/Fs27Us5v3GrUtTmr431/xJe8xjW4eDWgTVMS70qB9ZfaR+jnYLvJ5imTj2ZN0G5PvKNAUN0tOgl7zb5ZXSknHicnmlvb2+Xw++iYiIKD1wHL70pW9HA+hHHfZlOWTSR/p1HIfP/Ly8/vZvou8vOvPv4vzX8s4vRK7SY0707MIgOhHlBBxw76v/UPJ9FfL+hiWyc88bGvSeMPZk6eho0MDh8GGH6YEygoJ7D6zTG+IFg+3S2r5P9u5/X2+oVj3sUD2Qdjl9evm5398sbe37NJiKvK24QQguJa8omyJGOKhB05bWWg0gIgiMG60heIBxIHiK72/Y8pQekCeD3nL2y94B044ALJQWj9MgN6bDEEMK8irMwKgrXw/4cTCvAXh3nn4P04pxIwCAnnehSGAXw8R3EPBEoDg+j60FQWMEdqGwYLgGajEfCHQDAriYDgTUMS4EekfXzJW8vDId1979a/XvGB+mMxzy6/cwPTiZ4Hb6ovNWVjJel3eHv1G83mLJ85ZKSfFoCQY7pK2tQby+fGltrdOAL5Y15rW1fa+Ou7hopLhcPr0xHoJ2eI3h7DvwQXRecGMYvC8sqNZAyJ69b8uwimlSWjxWb6SH5QcIpli9IhEYwckOaxqR+xcnQqzAIL6HaS0rHifFhSOk3d8gbW37NbCyv369Bqsryw+RfQfWRacDw7DSDuA7UJA3TNo69kdOKJjLQoPZDocOt6Vtr9Tte1cDPAjao3wngvlGqgNrensL82udRMFyx7rHcs7LK9fAUFPzDnMceRVa1jGNCNRXVczQ8oSbBWLZYH6wjqZOOFOKC0fKvvoPpG7fexqAt3qdYrmVl4zXsoBljjqFkzK43LGhaZuuo9Li0TJu1AlahooLavRkAXqZ1u5dI3X739dpQl3GcDE9OImAYTQ2bdfxTBx7SvTERFnpeF3/CPRjHsaMmCcHGjbpCQ6PJ18qy6ZKRdkkXT+YB0wXyjN6sQLWzYHGzTp9OAFgMct1QJcDTtYguIaytLv2LZ0vTANO5CB1BcqJ/cQM5hPLorxkgpaZYKBNXG6f1i8EE4PBVq2/SFeBdYr1jzK2u26VjKw+SkKhdl0XBfnDNBBpDRvDxUmM0uIx0RNimG8sU7y2T0OqMP/m+nXJindu13YVpk06NxpAxzhfecxsY0oqHFJZ0/vxUO5BeZww5qTo+5lTL4o5CTdnxmejf0PaFpwoxMk/PBBUx3YdbSLqEtoEfG4dCKPtxLYI2z9sG5Ox2nzUs/fW/zP6+fotT3b57ubtz6U4Zw6tn2gnMVxsT7FNQztqje+Qiedo/cY0V5ZNlraOem1jUN+wfUVdx3RXlk+VvQfW6n4K2hmczEc7hhMKOCGBtgZtHrZvaE/wN+wnYPz4vc9bGlPv7SdIYzoJtNZpe9/QuEVC4aBeVWCdmCUiIhpK2jsa5JlXr5VtO1+JfjZl/Jly1Kwv9/u47Nvb6ZMv0BhCAPnPI31PjFAknYuLHYyyicOw9pgGqcbGRiktLZWGhgYpKTn43o80uITDYamtrZXqalyWy8ZlKLFyqCKghpt5IOhsBWRrI4FDBCPNg0av/g0HxPYWzR9o0mAYNkhWcIcol+CESHcnaBJB4BrBbzO4hJ27Qb2b0G+QhxlBLQtOLCB4hUC4BT2wEejCyaDeLDecKMPwMA6caOsP9hM/WKcx0+ny6RULaD+7TIvDLcOrZkeDejjhM2f6Z2Tc6BO6fPe1t34tb713V8xnGNclFz0TDaLv3RWSJ+8zx33CR3wyZsrB9d/gdp/6i3UItL/+Q6lv2qonzXBCDsFpBJ9xEq64aJSeyMXJoo6Oej3hjjQvuGEqTmbt3vuO/h5B+VAooCf3jHDIvLLHCGk7ipO32SC+HbBvI/TmZg6HhIIdUlg4XPz+Jg3Y42Q6bhBrh5Nu6JCAXPSo5+ith9/iPwTzcRIT+2U46YhlUlgwTMpLJup7nABGO1fftEWGlU/TKw1wEgFtp55I9RTabiJr6NUF2N/D/uCWHS9Lc3ODFBeVycypH43p1W+/eg3jwUkKjAc5+ptad+vJCAx3z941Oj/oJIHvWjd16w46UOA3/kCrrmecxAWUEUwvTohi3xRXOWBdo2ch5gcnQrAc0NZimtasu19P8h916Jd0G4srx7B8sR2xrgxDx4QJoxfqiUmcqMCJco+nSPK8JdoRYfO257VDRk3VHD0BghP4761/WE+MTByzSOcJ84kyi3WLk6k0ONlPgmcTnFjLxI2due1PHdpOtOH9cWVwrkF7fO8j5+gJZ6sDE9K9zTv8SnG50lPud+5ZKR9uflzmH3GVblMCDzwpf/Z/VwyHIZ8++h5x/+5xcVSUiu/a/g/iDwbhSN3Py8uT8vLyrIj7MohOgxo3qINLc2uteFx5GuzGQQF6cm/Y8qT2ItPLnt15elCCwA56qKIXZ39DD2MEiMaOOFaaWnbqAQ8OfhCo10vDG7eYB17+Ju1djQO8qsqZ4nQ4oylgnA6XHpjgoK2goEq87kL9PXoNI98qeqdj3hCMwrCs4WFcJUWj9IAVB5YYHnZEqyqma2/XDzb9N5qKwxw3csS+rr2IQ+EOvYwdywY9ZUuKR2nPWysA53HnSWv7fj0oQw/08WNO0vHhYAo9YstLJ2jPbCxnBAJ31b2lMT70fkavNSwXnGjAMrd64k4Zv1iqKmfIjt0rokEA9GatGTZL53vz9heksmyKLjccRGLeEJzD7zEfmG8ctKMnP3LfYnnhgBIHlxgPli16+GIZ4GAR04pesTjIxDSjVzIOqrFDg50ZDAfLF73ynQ6vtLWFZNe+5zXYh57u6P2P15gf9OBFD2PMazFuOpdXKXv2vqPjw4xjejDsqoqZOl84MEYgAPNRVjJOlxc2j0gngPUxefzpOp/4DD0TzNQkIc2xiwN5M61Jq/YUHl1zjF7JgPnEOkYQAMGY0SPm66X+G7c9qyd2Jo9bLDXVh8ve/et0WGZqAEOnA72TG5u3ye661fobjWWLQ+cfywnf8XmLNOiDacZy1Z7bkcDlxq1Pay9O/B3zijq3decr2lt6xpQLdb3gPQIeWF7oFY4dRZRltydfyy6CLVg3KHPtKEti9ub3egq0HKC8OCPlF+UJASX0UMTfrB6WCEiVl02Swvxh0t7RqPW7qWWH1O17X8vHlAlnis9TpO3BqJqjNMigJ8kKqsXnKdYyi97SVplFmUHd2b6781JHzAuuHsD6R6AEO7nYIUW5wbJBfcMyQ+93rE8sJ9Rf1DEEuHBVQUnxGNm64yX9HoaHsoxerN31Uu0Pia4y0TXtcEVSBNm/69agCB5oT8yrQQbuJMcJx3xXZkzpvJkSlu+f7j825jvoqYPyNaL68Ohna17zy9svByS/0CHnf7kzFVVfcbtPgw2CsLvr3pKighHSEcCVLDVab/fXb9B2dlftm1qX0V6iHUPbvu/Ah9qW4goU80ogs0d8PLT52EfA/ky62wMr4JwoV31/wXZDrxzQK/2auv2uNe8IIFvbdASusc1A+4mAtHWFnLkv5tEOGXYI5GtqP5dHt4f4LX6HK3awncVwD64ThkP3HeJPZmMba91Mty8wzdhO2q+ss2B7Zl11gWVTVjJB93GwjUdQzQzwD9P9NPS4rN23RqcRr819kN3RfV1ss3GyBGndsA+AZTNy+JG6j7N1x8s6TOxHbtj6tO4n48pIXOHncrp1+VlXRmBd4ko0DAfThP0Iq1xbxwc7dr+m+4/jRh0nwyqmx1zN1NC0RVrb9ul+IdYl1k+yQG5941atQxhPKnBPBCzH/Q0bzBNCIrpfM370iVrGsI+2Yw/SRxVo55wO7OOH/OYJM91OO6Vu/7s679i3w/EApg/76Fg+xUUjZOTwo3UfxDzxUiThsF+3oS3te6Ugr1L3YTA/GB/2gdauX6JBNXNaZkhbxwHd/5kz43P6W+wTtbTWiT/YovuouBoRf8ewscyxX4ErbrD/hn3J2n3v6XrAOLCfjHKO9YF9f2yTcfwwavjRkucrkb267xyQiWNOka27XpUPNj2q6w/zgpM2aGvQYxb7TpgOXAFoSFjrD47dsB+F5YdxYJli3xb1DoFIfKcj0Kz1Fqm2sPxwQgttINpC7LNivw/rBPOMYzXdF3X5tNw6jFIpKirU4wQcn+AqGfwd7SauGMaxCk52FRUO1zKOfUJMI8oVTnxinrGPh31e6zN8z9ovwTECphf77KgLWB/1DZt0OnHyyrxhdYseA6AcWp1NUFZRn3DspCcqDYl+B1eXojzvr/9AtwEow0ak7qMOoHyh7uGqIvNEYpFs2vacHhfiSlqvt0jGjlggpSVjxOn0yoH6DXo8ZJVvrEOMW68MrFul63jTtmd1vx9XG+HKJrQVqJMYN8aF9gDLCscJaB8wzbhKGFc5YRrRTuzbv05PBOM72CfGci0rGavjxDLFtK5e+3d9xjGnnjgOmZ+jbdMrJvMqtU6hDcF8Yd+1pa1W68eW7S/qcCrKp+ixCcoAvoNlhnK2Y88Kqa6codOHeUEZxO8xrTiGwOYO+5y4MhTtB8aBnnQ11XN0GeBEItoM1Nt2f722uQhQ4yR5srSRWH/PL/+BbNnxkr4/evb/yJGHflEGWuDBJ+XPHWYQ/VNH3CWePz4pjmFl4vvulyQXhRlE738Mouc2HkxnD2zYsPNUt/893RBjo46DD+wMYkcTOy3YCe7PnqCAnQgrxQSghxDypQI23NgxxgYTjzE183hWfohg3c9d2G1B24KTFzjQwQ52OnJs44AEO/L6QHoWHPQYhp4AQpuDEzU42YODARx0rtv4iPaYRGohnHgzAzIHZMv2F7Q9xIkZTbvUUS8797yugQYcVKPtxDBxsISDGvTiRIC/rW1ftI3T9DbeopigAQ6gceCMA2UcoOJE1Jp1/9BhlpaM1fsgYLw4kEXOaRycvLP2Ph0GDtjRPuLAqGbYbL2hIw6eEGTCgZumd3G4NWDy7KvX6kEzhvup8x6Njv+JF66Wzduf14PKj531gP7Gnts/HDZk3ZtBefMF82To3FO9MnnWwffiYd2nXIWUVAhKIZiFoCwCVckgUIIgAuo22smRw4/Sk5wIwuBkOz5DO4A2bdvu5VIz7DANsqH368o1f9R9twmjT9Jc76Nq5mp6HARiUM8R0EBwDvtXCMoggIR2DIEuTCNSlaF9xMlg7P+hFz/abKtnH04Son3EDWVThbYJJx9wMtYfiO0ZPxDQFmIZxPfkt+BEPVgnXrHPa+2XJoMAHdp7dLxA4BDtJzonoM3u3tC4QgxXeSDNEMoQbiicCLYvZgC+vcvxAFLEmT39S/SBQDe2c9Y6QD3BekEgztomI/i9Ydszun3FMLFNRsCRiHqmgWhvkXZ+S0dHt/6Cuo9tTiLYbuIG57r9s0Hw/2//Oit68nbGlI/KCcdcI5kQeOgpuavtuxJ2huUTc/4ovjufEUd1hfi+M/AB/WwQZhC9/zGIPniZeSpxZrnvB9U8mB5Y2Gjub1ivvadw1hi9LdADAYGfg9kJRe9GzXntKdSdYpyVDgXNngfo4YKz/9jRxUEJzp5bl70isGT1VEGPDrMXEi9dzQWs+0QDA8H6vy5ZrK/PXnSHXvUB//jPBXqQMnn8GXLKgp/E/GbPtpC8/GiHtLeau5hur8j5XyoQr+/gT3aw7hMN3n1Iq5csnhHERNAY7xG8Ry9eBMqRwgJBUpwIRKDZ6y7QYCiC76j7VVVV0taBk4cdUt+4Sdsoh9OpQXsME4e2La3oyVovH276r54EHaHpT8z7ZUwZf7qmn2lvP6DpVLBfiZORSMuD8eMKMpyYRa9Ut9sno2vmy4jqOToP2nu4rVZPkGI/eHfd29pxI1mOeKT/Qc/RUTXHaKAcVyXgfjnYj0WgJ75HpN5no2GTnjQQCWvw3sx5j6v09usyAnRawTQgzQx6QOOKO9yfA71xsWzRKxnL7L0PH9Jgl5lT39DfB0N+8x46RliHjZMj6DGq9+1p3a09znEiV1Ml6pWVIV022N/G1Xa6vFprzeF6i7VHMnpYWzBepO3BVX3YT8dxAoaPebaWdyLoNYtlgnvTZMLomnl6LwH0IN+zb3VMpx3ME06Yo1crTjbhKjcsDxzD5udX6H1vcIUmelCjhzVOvic6SWSlbrN6AvcGjoGsXrb7GzbqSXyURaxvrAOUc1x5YaZrK9EygPqAeodpRvAQx0nbdi3XqwzGjlqgvbux/rGOcFIM5QyvzXmu0N+ifGuv7vIpsvfAh7Jm3d91edRUzdKrCHHfGZRjnNjDcHFyv6Fpu9bh0SPm6f2F1m95Sus16rp1Eg3fRxlEnUB5RDnGSTv0Ft+09Vkti2NHHhe5ofRsLbf79m+WsNGiHbZQPzEOzGNR/nCttzhhApgudNyyrtTQ9EjufLNd8ZvHilZnA5wcA6zv+Kt9sAzQoxp1AR3E0B6hHFtXeoDZk91ldnxwiKa6wrErpgc9ofFbe5nHd1E/zCuhcYXyaL1CA3URPfqxnnHlrHVVAdouf7BVyxPKJpY1pht/H1VztJ7QRCcIO6R7QtuE9Y7v4eoD1H2kLcP6RtsVf48olEuUcdwvyOly6/ymCu2mdVIKy9fp8mj51HtU9QGWM9oMC9rX0qIxuiwdTremubK3OeZydUbvIWWHsop0WGEjHNOZb+G86/X+PZZ31t4rr678v+gNRI887Etp6aCTahD97rbvScgZko/P+r3k3fWcOGqGie9bX5BcFM7CIDpvLEoDEHTdqDc6xM4fLt/DRuL5136kPVWs3Lto7NFTGM/YyGHHy36ZH/U/7KRjRww9QczLYFt044RL7LBRxQHJhi1LI3k+zcv3rB2r7uAyUqRhMC8hHac7XzhwQY8o7HBhA4cddcDBE9JToIdmqqwAOlgBdOtz+9+IiOjgocc/DmjQ2/29Dx/WIDqCMQgWwNFxN1pq2BeWpx/o7DV4yBFuOWyet18C6EQ0eFmBZivPM/bhNKDdiwNpQGDDupGzlV4gme4uxcfl/5oCoBewn4krfPCAyT38ftTwo/RhGVY+tdvvIxCEq4HsrH1d64bWgKBiPMwLbo5tN2HMQhkI2LdHUA5B2e5uOovjCVy1gGAlUlYg8IZjQSt9CoKcgCulkN4I6dusdDs40YJ1j+MWpMCw8t8jxR5uJuz3N+q2CUHJyeMXa6/ZxpYdeoUWjnNw7IGAKYK36OE+a9onNeCHEypIeWHedLsTgrfrNz8pw6tmdXvVRzJIoYJgKdYf1ituAI5gtx0CxlgmODY2g8/b9GQLPkMQHOUNQWYsA/y9y/JEOjgD9zkxO6ThhBKCpdZy7I/1Gn+i57ij/l+vhoHyf9i0T/TqNwuO/Ga/nEDHsS3iDdWVuE9Dz532cGIKx71IV4Tj1+7SfmDZ4+QUHp3TGNQgOcqmpnnU8u3TE1xmmsYduk9lpYdEoB1lorfBWtQL/MZaz7jyyCrjOI5PZV6xbnfWvqnfj29zAPeTwL0rUJ9wBRTSnWJfEMftKJdIa4PYDvYJk00/YkBISampJg1NgKTLA9OK4WIZ2q9gRH02r4Q0T6T2BG0C6qmmA/UW6ck9pEFCvcYyxr0z7Gmd0G489LhZFt96966YIDpSXMKEMSen5QaivYJ7WxjmMrVS/+Azyh7siU5pgcvScZZ++Vu/0rOpvYUzmhef808zJ1r7ft15SbRTxh5p3cOyw2W3CJhjBw1noHEZr/0Mel/pTm/lDPPmTOLQ/MHYWFmpVIjSiXWfaOCseu8vsvytW2X4sNly/uK79MDogUfN/Ohf/PgyPSiyvPOKX1YvN/O8f+TSfCku69/6ybpPlJtY9+lgIeiG1Gz2bRYNDqz/1F8xqnsfOVtff+FjL+lJEPTa/9dTn9fPLj77Ie3Bn0mBfy6Vv7RcKwFXQD424zYp+Our4hg9XHxXXyK5KMye6DTUbdnxsqxc/QfzJiNxN0XD5TQ4+4/ebOjBgV4UY0YukFXv3h25oeQmvSwR0EPhzvsXRH+Ls7ropT5lwhlihEN6UznsCOHSrzzvOPHmzZGKssw2eAMNN87BzWIQHEePDZz9xo4hejG0te3VZwTQU4VLRnFW2bphFM4Al5dO0hvEYX1ZeQeHVx6ql671Vy8HIiLKblavR1w6DthuWzcBjA9G1O00t/1HnOjt9wA6ERFRX2mqIDGvhCCi3IMrPXAlCq50OdCwQTsD4n5BgPQ5uM9SxmlPdPOlEbkCiz3RswujYNQv1m38j7zz/t+0N3I83NV58QlmjikEvnH5kP0yo8njTov8LaiX+OEu8U+++E3Nm2ZBQH5X7Up9JPL6O+bdyxFknzH5wkGZ1sO647T9PW6Ag5MPCFzg0qT1W57QPHPIixafC6y7HuO4nDA/v1J7jGM5j6w5Shvlw6Z9UtcHcj4iZxouTME6RB4y5HMjIiJCDk3AQQe2E8jBmShVQChoyO6t5g5/zTgGKoiIiIgoe6BzIPZnG5t2aBAd93yA+UdclbE86PEQswF0HlW8+iKrMIhOBw03C3l++Q3RmzkgFcupx9+kua7QQxq5zi1mHsTEB9bo2Yw8bkgH8pnzn9AccE0tOzQ3FW6IsW7jo5pDG4Fm5FdDcHhX3Vvi97dJS9tuvckIHu99+E9ZcNT/06C6lU9uICHAYN380uoljnnBjWaCwdbo93y+Umlp2aN5xvCdZDfa6QkuOULucdxEBfn/sIwRBLdSrSTKoZcMNhw95WskIqLcYt2wGSdhkX8SNy+DqRPNS2ItK57x67PHK1JamR0HIkREREREUFI0UjtmbtnxouZMR452wI1sswIC+VZOdAbRsxKD6NQrCJTv2L1CA8IIeOOA+vW3fxtNuXLysT+SCaMXRgPX9ps59AaGjccImaPvcdfq+UdcqY9EOZLyC4Ly3vqHZPW6v2tD+Nizl0e+gaDwIdqLDjfbmTLhrJgbWPQ33CDj4Sc+ozfmxKVCCDbYe9T3xLyBidlo4qafuAGHFVxHKpWRw4/Ss6e4UUpVxTSZPG5xtzfwISIiOljYHmF7jvts4MZNza1mWhfr5n6W3VvNnf3hY3FTKAbRiYiIiCh7lBSZNxFev+VJcTo7syMU9/JG0wPRE12iQXTuU2cTBtEpJQiWv/zGzbJ+8xPRnNl26AH96fMfz1gKEOS3QoB9xpQL5LllP9AbROjtysWQvQfW6gNnGzEPyP09beJH9M7Lye66bb9LtOYaDwe0tziaNOR0R5AcaU8QsMeJhbAR0l7lre3mJe6Ay4Qs6JVfUTZFaqpm6TiR1gYpWtBrH0HxgvwKKSyokaLIJfMWDBtpW5Cfy+zFT0RENPAK86s1iL6/Yb0Egi3mZ7Yg+pZ1QWlpNJM4Hn2yN2PTSURERESUyCGTPiIr3rldX3+w6VF9Pmn+DT3GhQZSfE50B4PoWYVBdErJ2+//Vd778KEun6P3+aSxp8jRs7+aFTm0EZA+77Q/a0oV5FHf37BRdu1ZKXv2viPbdi3XG5fiMvSVa/4oaz54QEbVHK29xUMhv/aex03SOvyNmloGPcBr973bq57kljkzLpER1UdqnvHhww7t80040ZijFz4REVEmFRZU6Qnp11b9JvJ+uHjc+dG/v/u6ua10e0Tyi7izT0RERETZBZ0WkT54y46Xop9lVbzFwZzo2Y5BdOpR3f735bVVv9bXM6ZcJEceepk0Nm/Xm1W6XN4+p2xJJ+T2djjcmt8bj8PkE9r7e1/9B/Li6z+Vun3vakB949anezVcn7dU08x43AUSjOQ9R07yyrIpMqximgQDrTKscroU5lelbd6IiIgGWlHhCH228qGXlYyP/m3PtpAcqDV7yyw8Py9rbsxERERERGQ3Y/KF0SB6eckETf+bNRDHiuRED0fuOShZ1EueGESnHiB1yz8f/1T0/dw5X9Oc4uiRNtggHUpVxXS58PS/au/znXtWyoHGTVKQN0zyfCXS1n5A0794PcXiDzSJy+XTAHlx4UjNAVtZPiWrLvMhIiIaKEcceqm8+8ED0fcjq4/skgsdqkZyO0lERERE2Wnc6BPkY2c9IBu3PatZFbItxtO1Jzo7p2QTBtGpW5u3vxh9feEZ96b1ppwDKc9XJhPHLkr5++h9TkRElKtwhdW0SefL2g1LZETV4RpUt9TXmT1ljjzJK04Xd/SJiIiIKHshhUtWpXGx90SPvDTCQfMF07lklYyujR/84AeRtBudj2nTpmVykijOh5sf1+dDp16svbiJiIgoN50493vy8XOWyDmn/D4mZcv+SCqX8mru5BMRERER9ZXDcMbcWJQ90bNLxnuiz5w5U55+ujMvtdud8UmiiK07XpFtO1/R15PHn57pySEiIqIMwuWuZSXjYj6r3R6S1iZDX5cPYxCdiIiIiKhP7PcVsnKisyd6Vsl4xBpB85qampS/39HRoQ9LY2OjPofDYX1Q/1m78d/R18PKZ2Tl8sU0GYaRldNGROnDuk+UHdau9Ouzxyfi9qJOmgH1dGHdJ8pNrPtEuYv1n3IFynn0xqKhSDoXhyNny344C+t+xoPoH374oYwcOVLy8vJk/vz5cuONN8rYsWOTfh9/v+GGG7p8XldXJ+3t7Wme2tzR0LRRNm5dqq+POexa2bt3n2QjVKaGhgatWE6eoSPKGaz7RNnhwN4CEXHJ+JntUlvblPbxse4T5SbWfaLcxfpPuSKvrTV6Y9HWlhZ9bvd3SGttreRy3XdmUb3PaBB97ty5cvfdd8shhxwiu3bt0uD48ccfL2vWrJHi4sQ3sLzmmmvk6quvjumJPmbMGKmqqpKSkpIBnPqh7f1Nv4++nnHIKeL1FEm2VirkZcX6z6aKRUTpxbpPlHkH6sLSXG9eHXjI7FIpLkt/XWTdJ8pNrPtEuYv1n3JFsLBQHI1mED0vz2c+FxRIUXW15HLd9/nMZSG5HkQ/44wzoq9nzZqlQfVx48bJAw88IJdeemnC32DhJVqAaEzZoPafXbUr9fmsk34jeb7sPjmBSsX1T5R7WPeJBlZ7qyG7Nodk5ASX+PIdsmVt5DJTESkuc4lzgG58xLpPlJtY94lyF+s/5QL0QnfE5UR35Hi5d0TqfrbIeDoXu7KyMpk6daqsX78+05OS09ZtfFQam7fr6+rKQzM9OURERJQFXl/aIdvWh/T1SRf4pKne3LmfdaxnwALoRERERERDksPRmRM9bO5zh5xheX/tfdLWvk9mTfu05OeVZ3gic1v2hPNFpLm5WTZs2CAjRozI9KTktHc/eECf83xl4svyXuhEREQ0MPbXdt7UZ91bwWgQvbImq3YniYiIiIgGH4fZGx2MYECftzo/lFdX/kLeevcu6fCn//5D1L2MHvV885vflBdeeEE2b94sr776qpx//vnicrnkE5/4RCYnK6dt27VcavetiaZyISIiIupoM6Sl0Yi+r9sRkqb95vuSCgbRiYiIiIgOjkMk0hNdAmYQvc1l3mAUfN7E946kHEnnsn37dg2Y79u3T28Scdxxx8ny5cv1NWXGlu0vRF9Xlh+S0WkhIiKi7LB6uT/62ukSCUTeevNECkuYyoWIiIiI6OB7opvCkSB6wGXeg+iQiR9hKpdcD6L/4x//yOToKU5j805Z88H9+nrRsT8RJ46SiYiIKOfhpqIwbIRTvPkO2bnRzNNYPdqlN/whIiIiIqKDE5vOxS0BhxlM93gKMzxllHU3FqXMejcSQIdRNUdndFqIiIgoewQ6zOfJs9xSMdwlHo9fSoc5ZepsT6YnjYiIiIhoSN1Y1AhYQXTz8k+vuyDDE0fAIDpFbd/9uj6ffOyPpCB/WKYnh4iIiLKEv93sie71OaS8yinHnZ2X6UkiIiIiIhrCPdHzo0F09kTPDrwTFKnafe/KvgPr9PWIqsMzPTlERESURfwdkSB6HlO3EBERERH1N6RIdJi73GIEzVzoAaNdnz0e9kTPBgyik4RCfnlk6Rf1tcddIEWFIzI9SURERJRF/Ob+u3h9mZ4SIiIiIqKh3RM93NCoz/vaNuuzlz3RswKD6CTrNv5bQiEz2ekpC37KG4QRERFRDPZEJyIiIiJKI8TiIjnRRQxp9rRIU8cefef1FGV00sjEnOiDWGPTdlm/5UkpLBguUyec1efg9849K/W5vGSCjBt9Qj9PJREREQ1mwYAh4ZBEc6ITEREREVE/c9h6ojsMachviv5p5PCjMjhhZGEQfRBraNoqr7/9W329defLctxR35I8X3mvgukd/iYNxMNxR38nbdNKREREg1Mg0gsduxdub6anhoiIiIhoaHJEe6KL+CdVRgPoTOeSHZjOZRBDD/Sqihn6esOWp+Sef54iT7/8HTGMcEq/37Zrmdz14In62uctlZrqOWmdXiIiIhp8/GbGN/H4zBseERERERFRf0M/dHNf2xBD/G5zJzzPW5rh6SILg+iDWEXZJLng9L/K/COuin62YetSuevBhfLBxkclHDbv5puIYRjyxju/i74/7uhvi8vpSfs0ExER0eDib4/kQ2cqFyIiIiKi9KVzMXe7xXAY0u4yg+g+H4Po2YLpXAY59AibPf0zennHc8uul/3168UfaJZnl31fH8OHzZZDJp4lk8aeJm5Pvmza9pxs3/WabN+1TJpbd+swLjrjPhlWMS3Ts0JERERZiDcVJSIiIiJKs6KCmJ7om0Kr9XUeg+hZg0H0IaKqYrp87KwHpL2jXp5+5buyfddy/XzP3rf18eLrPxWHw9kl1cuRh17GADoREREl1dFm9UTP9JQQEREREQ1NrqNmimOFGUT3uwNSHzY7vhbkV2V4ysjCIPoQk+crk7NPvl02b39Btu9+XcIhv2ze8aK0ttVpAB2B9JlTPiplpRNk/KgTpKiwJtOTTERERFnsjef8+uxhOhciIiIiorRwuBGiNfe3m70t0c+nTjgrg1NFdgyiD1HjR5+oDzje+K7UN27WQHp15aHi8RRkevKIiIhokHC5HBIQQ8qreCsdIiIiIqJ0QcdXaPW26nNF6WTxeYszPFVkYRA9R/Kml5dO0AcRERFRX3KiT5zJ3UYiIiIionSxcqK3eNr0uaBgWIaniOzYpYiIiIiIEgoGDAmHzNdepnMhIiIiIkobR9iI6YnOfOjZhUF0IiIiIkooEOmF7nCIuL2ZnhoiIiIioqHLYZidVjpc5j2JfJ6iDE8R2TGITkREREQJ+TvMZ4/PTA9HRERERETp4Q5H0idGdrvdrryMTg/FYhCdiIiIiBJqbgjrM1O5EBERERGllycUew8it5tB9GzCIDoRERERJfTuawF9dnsZRCciIiIiSieP1RM9wuXyZWxaqCsG0YmIiIgooWDQfK4exV1GIiIiIqJ08oQ8Me/ZEz278IiIiIiIiBJqazFvLDrp0NheMURERERElKac6NZ79kTPKgyiExEREVEX4bAh7ZEgen4R07kQEREREQ1oT3TeWDSrZEUQ/be//a2MHz9e8vLyZO7cufL6669nepKIiIiIclp7qxlAdzhEfPkMohMRERERDWhPdKZzySoZD6Lff//9cvXVV8v1118vb775psyePVsWL14stbW1mZ40IiIiopxVtyOsz0VlDnE6GUQnIiIiIkonT4g3Fs1mGU9w+ctf/lIuu+wy+fznP6/vf/e738ljjz0mf/7zn+U73/lOpicvq7U2hWXn5pCI2VHMfDI6n/Up8hzznQTfCwUN6Wg3pPGAIYZ5zCxuj0hJuVPKqpxSXO7Qz8MhkdZmQ5oOhCUUSjxdHW2G+NsN/S6+g/fhsIgRNsTlMg/CQ2FDjJBIIIBhGtrLTcRh/h85Trc+0ifb55GvidvrkLwCEb8/X3y+DvMAP/I3azjWw+NLfPCPvxUUOcTlcZjfdYpgME43/uaQcNCImYbO8Vsj6jqd8d91u0WnLWyYyxYPzDeWua4H27rQ13gTeQ6FzN+43BigEbM+k63L6O+tP1vrOGBE11l0OAl+3+Xvce+NmDedvw0GMJ3mMoxZxknfdIVlnvxv3f+2V9+1/b3LV3ucxuR/C/ht6yjFeU82z6nMb9Lv9GH+Eg3L5UI6h0i9cDp0/eIR9JtlE3Xc32HOs70emMNzJBxufD2O/Y35jPqqr2OLWswLlMPWVq/sLAgknqnIiogryrHD6DLwLj9P+pmWd1fX8p7odwnH2Yvx9jxdiT5MPBqsM0x3sjaxP/Noo+1yOs1l5PE6tCwhPUhf2oeDqSc9fq+P7UGyYWJesUqwbUM5sdoFfWAbYJifud0O8fiyt4c31teG1eZdRatGujI9OUREREREQ15JR3HMe6ZzyS4ZDaL7/X5ZuXKlXHPNNdHPnE6nnHLKKbJs2bKEv+no6NCHpbGxUZ/D4bA+csn+upC89pQ/zWNJEinvs+4iRD1Ej5J+H8U4t9Y9EQHOyptBPiJKn4kznVmzj4XpwImjbJkeIhoYrPtEuYv1n3JJvqdMJu0dLxuGbdb3Lqc3Z8t+OAvrfkaD6Hv37pVQKCTDhw+P+Rzv165dm/A3N954o9xwww1dPq+rq5P29nbJJa3tTqkc6bP17IzkLo3+E/k4vpNbZyfq6GuHy9AepvmFYfH4zOH425zS0uSQpn0uCXQ4tDchHm6PIYUlYXFHvhfP5TLEV2CIE700HYZ4fYb5W4fZsw2s3okut6Gvu/Yy7Rxel9eGOfX+dof25mttbZP8/HxxOJzRXtid3xVBx8dQIHFPP/TK7Gg1f2d/hINmT0LMQ5fpiOv5H/86tpe4Q3t/o/ehtQx0viPLG+vM3nM+Zp1EvotlGA4n6NVr/da2Lrtb/5gXLG/795P+Pn4YkfF191vMTzjYc0/bpH/q9vyKI/XTLT315O3rd3v4Psq2XnVgLWPjIMZvm9+kozyY4af4vVAQKRwM/bMRdphXlwQd2gY4XWa9xmt7uYytC46E4+ly5YP9b5H6avTQUxkbU7+/Q3xetIHxBTIW6lDyPyZ8meSD2Pqk7ZnRy2H28u8JP0o0PykOE9Mf9JvtWzq5PIZ4POYystapriZXki7yfal3qbYLB1kPevU9zGvQnFfUHfPqDbOudF4hhauyzGXT0ebUepWtML01EwJi/H/27gPOsap8+PiTNr1sm+2V7buUZalLW3oRUJoUQfkLgoUmYEOlCgKKgqig8iKiCEoXQTosdalLW8r2XmfL9JJ2389zkpvNzE5mpyST3Jvfdz/ZJDftJDknyTznuc8JWJIrVfb0R3Rtba35DNDECwD5gbEP5C/GP/KJ98yjZexHflkS/X/i9RZIS7M/b8tdR+NjP5fGfdbLuXSXZq1rDfXkTPRRo0ZJVVWVVFRUSD4ZPFhkwhTJazqoqqvDUlXVP6cGFoC+GPvVUlVVwdgH8mzs68SZ/u5j7AP5g7EP5C/GP/LK4MEyaMoEGVh3jBQESqWkeJDk+9gvLMyduvBZDaIPGjRIfD6fbNiwoc12PT906NAOb6MvXkcvoH6Y8oGan3RQ8f4D+YexD+Qnxj6Qnxj7QP5i/CPfDOg3LttNyKmxnyuy2pKCggLZY4895MUXX2wz06DnZ82alc2mAQAAAAAAAACQ/XIuWprl7LPPlj333FP23ntvue2226SxsVG++c1vZrtpAAAAAAAAAIA8l/Ug+mmnnWbq2l511VWyfv16mTFjhjzzzDPbLTYKAAAAAAAAAEDeBdHVhRdeaA4AAAAAAAAAAOSS3KnODgAAAAAAAABAjiGIDgAAAAAAAABACgTRAQAAAAAAAABIgSA6AAAAAAAAAAApEEQHAAAAAAAAACAFgugAAAAAAAAAAKTgF4ezLMsc19XVZbspyIJoNCr19fVSVFQkXi9zQkC+YOwD+YmxD+Qnxj6Qvxj/QH6P/WAw2Cb+m02OD6LrC6pGjRqV7aYAAAAAAAAAANIc/62srJRs8li5EMrv5czE2rVrpby8XDweT7abgz6meyDoBMqqVaukoqIi280B0EcY+0B+YuwD+YmxD+Qvxj+Q32N/5cqVJt47fPjwrO+N4vhMdH0BR44cme1mIMv0y5QvVCD/MPaB/MTYB/ITYx/IX4x/ID9VVlbmzNinoBQAAAAAAAAAACkQRAcAAAAAAAAAIAWC6HC0wsJCufrqq80xgPzB2AfyE2MfyE+MfSB/Mf6B/FSYg2Pf8QuLAgAAAAAAAACQKWSiAwAAAAAAAACQAkF0AAAAAAAAAABSIIgOAAAAAAAAAEAKBNEBAAAAAAAAAEiBIDoAAAAAAAAAACkQRAcAAAAAAAAAIAV/qgsAt1u/fr2sXbtW6uvrZZ999pGioqJsNwlAH2DsA/mJsQ/kJ8Y+kL8Y/0B+Wr16tXz++edm7O+5554yevTotNwvQXTkpY8//liOO+446devn8yfP19mz54tX/nKV+T73/9+tpsGIIMY+0B+YuwD+YmxD+Qvxj+Qnz755BM58sgjZeTIkTJv3jwTRN9vv/3k1ltv7fV9U84FeWfz5s3y1a9+VU477TR58sknzeyUzkrdd999cvHFF2e7eQAyhLEP5CfGPpCfGPtA/mL8A/mptrZWzjrrLDn99NPl+eefl2XLlsmxxx4rzz33nJlE6y2C6MjL3Tqi0aicd9555ot08uTJcsstt8gpp5wir7zyivzkJz/JdhMBZABjH8hPjH0gPzH2gfzF+Afy09atW6WlpcUE0XUvFB3/l156qVx11VVmMu1rX/tar+6fIDryTllZmRlUunuXsixLqqqq5Dvf+Y6ceOKJ8tJLL8mzzz6b7WYCSDPGPpCfGPtAfmLsA/mL8Q/kp4qKCmltbZU333wzsa28vNxkof/sZz8zpZ3uuuuuHt8/QXTknf79+8vYsWPl0UcfNbNUHo/HbNdZKt21q7m5WZ5++ulsNxNAmukYZ+wD+UWz0AYMGMDYB/IQYx/IX/zuB/JTSUmJHHTQQaaUy6effprYrosK654oY8aMMXuj9BRBdLheU1OTqYmmM9GRSMT8oP7lL38pDz74oNxwww3mcptedvTRR5vBptcF4FxbtmwxM80LFiyQuro6GThwIGMfyAM65u+44w5z2uv1msnzm266SR566CHGPuBi7733nnzzm980Y1mzTu2xz/c+kB91kJcvX25+/2uAXH/3X3/99Yx/wOUi8TGs3/t60GD55ZdfLu+//74Z+0uXLk1ct7S01ATYv/jiC/M50RP+tLUcyEGfffaZqX+0bt06CQQCcs4558jXv/51OfDAA+X++++XM8880wyeyy67TMaPH29us2bNGhk+fHhithqAM1fk1j+k6+vrzRerLiZy3XXXMfYBl2tsbDTjXP+Irq6ulquvvtps33///eWf//wnYx9wqY8++sj8YXzuueeKz+dL7Ilij31dZIyxD7j3d/83vvEN87tfx//MmTPN7/7Zs2fLP/7xD3MZ4x9wn0WLFsm9995rvvvHjRtnguj6t/9uu+0mjz32mBxxxBHmt8B3v/td83lg32bkyJGJ3wrdRRAdrqWLBhx88MFmRe5vfetbZlXuO++8U2bNmmW+WE899VRTK03/oNZge2FhoQwaNEj++9//mvpJmr0GwHl0PB9yyCFm0kzH9xNPPGF+QOsP58rKSjP2i4uLzYQaYx9wl4KCArObptY9fPzxx80Paf1DWunY1zqJZ5xxBmMfcFkAXYPlF154ofzqV78y2/SPZqV/UOvfAvq9r4E0xj7gLqtWrTKBMl0s8KSTTpK3335b/ve//5m/+Z977jnzna/Zp4x/wF0WL15svvuDwaCpOnHRRReZvwF0TOvv//3228+sfXD++eebzHT9XaCX67bXXnvN/M3QEx5Lf1kALqMZaPqDedKkSfLHP/4xsX2XXXaRww47TG677TYziHSA6ZepftHq7h46G62Bt+nTp2e1/QB6Rks3ada5/nC+9dZbzTbNPDn++OPlpz/9qdm1W8f5kCFDzB/d+uN63rx5jH3ARfQP6X322cdkpP397383E2ZXXnml+cGsWep87wPusXHjRhk1apSZJNMJ81AoJD/+8Y9l4cKFpg7ykUceaTLUNOtMFxjU733GPuAeOqZ//vOfm2Otd650/F9xxRWmJrIGynfeeWfGP+CyPU+/9a1vmT1JdO+Sp556yiTQXnLJJSZQrmFujfdptrmWedJxr8Fz/b1wwgknyJQpU3r82GSiw5VWr15tvkRPP/10c15np3SmSWep7dpHOuB0YE2bNs0clB1YB+BMDQ0NZtzrWLfdcsstZvEQ/QLVTBR7ISHdzUsPirEPOJ9mneiPZV1QSEu4/eAHPzDjWks4aTBdL3vnnXf43gdcpLW11STI6CSZlnT40Y9+ZH4LHHDAAaZcg37fv/XWW3LPPffIrrvuag6KsQ+4J3nuww8/NEEzmybSadLcBRdcYBYSfPHFFxn/gIsUFBSY73lNkNPkGY39aek2ZQfSdYzrWNcFhvVw8sknp+Wx+eSAK+ms8le/+lWTcab8/th8kQ4uXWDQDqLrwEpeUIAvU8DZ9AtTv0jtzJK//OUvppSDBtE0kP7//t//M1nov/nNbyQcDicWImHsA85n1zY89NBDzQKDurCQrouivwF0cl13+dRyLkrHv2LsA86mWWV333232dtUJ8Z1TGsd1BtvvNFMnl188cWyYsUKk42aXOaFsQ+4g66FoMFxDZonLx6qe5/opJqWb3399dfNNn73A+4QiK93qOWalJZr0RjAnDlzzGfBypUrTbxPf+/rXmnpxKcHXJuJprt1Kp2Vtr8o9Yt106ZNiev++te/NgE2+wc1AOeyM1AGDx6c2HbMMceY4LlOqo0YMUL22msvKS8vN1+mGljr6YIiAHKXBs+1XJPS+ojr16+Xs88+2wTWNTs9eXIdgPO/94cNGyZ/+MMfTNmm73znO6besf3bXv+orqmpkQ8++MCcJ3gGuMvQoUNNRqqWadMJNN0DXWkATbfr3ir2JBq/+wH3KC4uTgTKlf7G1+98/dtfA+laM10n0nSdJL1OuiqZ8xcEXBlAT6YDSweMHmsGmi4sqPSH9g033GB2/+IHNeC+sa9flpqhpgdl10YbOHCgTJ48ObFNPxsAuGfs60JCo0ePNmsh6JoHmpWiQTWdONfT1dXVUlVVlbU2A0j/2Nc90XTSzP6dby8spnugau1TzVQH4K7xb5dlufnmm02pBt3TVPcy14lzzVRVEyZMMBNtANz5u9/v9yc+C+xkmX//+99m8eB169aZ3/7pTJ4hcghX0QGlQbFrrrlG3n333e0u1y9TrZv0i1/8wtRJ1uvYtdEAuGvsd/Rlee2115ovUl14WBFAB9w39vV7Xmsjv/322/Lkk0+aAJoG0S+77DKz8BABdMBdY1/XOlA6zu3AmX2d3/3ud6aciy42DMBd41/3KJ87d64UFhbKI488Ymqh33nnnWbhwNtvv13OO+88s5igngfg3nifN17/XNmB9M2bN5vPhz333DOtbSATHY6mJRl0EA0YMCCxTRcQ0i9PXWxg5syZZqDZgTIt53LXXXeZXT+0Ntoee+yRxdYDyPTYt7388svmx/W//vUvef7552XixIlZajmATI59rYmsf0zrQoK62Jj+QW3T9RAAuHPs62/65O/9F154QR5++GF58MEHTRBNM9UBONPSpUulsbGxzR4lyeN/9913N6Xc/vGPf5i1EPSy++67z3zv66LDOpkOwH1jf2bS3/waSA+FQvK9733PlHDTQyb2QiMTHY61ZMkS2Xfffc0A2rhxY2L7l770JfPl+d3vfne7XT00+0x38da6qATQgfwY+zorrV/AWiPx1VdfNT+0Abhv7GstZP1BbWekJgfQAeTX9/6iRYvMH94aQJsxY0aWWg6gt7T0qgbK9O/3ZMnjXwPoOsmme6Kce+65ZgJN6yJrAg17nQPuHfu+dvE+/QyYPn26WQchU2XcPFa6qqsDfUx3z7z00ktNMFxX5f3GN75h/mjuyJo1a8yigjozpT++9TSA/Bn7+ge11kgsLS3t8/YC6Puxv3btWhk+fHiftxFA7nzv6x6oZWVlfd5eAOmhi4TrOic6Sa71znfEHv8A8mvsr+3D3/1kosOxdFB9/etflyOOOEJuvfVWueeee6ShoWG762nt8/PPP9/MZOnMFF+sQH6N/Y8//tjs3kUAHcifsa91UOfPn5+VdgLI7ve+/vGt3/sE0AHnWrhwoVnL4PLLLzdBtHA4LM8884zce++9plyTJsd0NP757gfyb+yf14e/+6mJDsfSnSjef/99M5h0YN1xxx1SUVFhBpbutvmzn/3MXE8DZ3V1dSwkBuTp2B84cGC2mwwgC2NfFxgFkH9jP1WWOgBn0L3HdcJMx7SWclK6OOjKlSvN+giaca4Taz/84Q9l5513Npfz3Q84X8gBY59yLnA0zUjRBUSGDh0qV199tfz2t781dZEeffRROfTQQxPXq62tlcrKyqy2FUD6MPaB/MTYB/ITYx/IL5pVquP8iy++MKUaNGB20003ybhx40yN5FNPPVVOOukks1aCjfEPON/8HB/7lHOBo+munLpgkNJZKY/HY2aiPv30U6murk5cjy9TwF0Y+0B+YuwD+YmxD+QHO8dTA2c/+MEPZNSoUWahQA2q6TYd97Nnz5bbbrtN7r77blm+fLlEIhFzG8Y/4FyWQ8Y+5VyQ87TmkdYy9/u3dVcdLJp9MmvWLHN80UUXydNPPy3z5s2Tv/3tb2a3Tq2FqCv26jEA52HsA/mJsQ/kJ8Y+kL+Sx78G03SibNq0aXLDDTfIokWLTBaqsi9TEydONGUb9bMBgDM1O2zs80sDOe2zzz6TE088UV588cXELJOyB8v48ePllFNOkUceeUT+85//yIQJE+T666+Xyy67TI466ih+TAMOxdgH8hNjH8hPjH0gf7Uf/xoos7NSdawfffTRJsim7CCarpMwduzYxHkAzvOZA8c+NdGRs3T3DB00ujqvzjT9+c9/lgMPPND8mLZnoT7//HOThXLGGWeYhYXsbBUAzsXYB/ITYx/IT4x9IH91Nv47otfT9RFuv/12eeONNxKLCwJwluUOHftM2SNnV+V9+OGHzW4cS5culWHDhsnZZ59taiHaM1Rq6tSpcs0115gf04of04CzMfaB/MTYB/ITYx/IXzsa/+1paYcLL7xQHnjgAXnllVcIoAMOFXLw2CcTHTkpGo3K66+/Lhs3bjS7bipdREBnq+6991454IAD2tRLtG/DrpyAszH2gfzE2AfyE2MfyF89Gf9vvfWWCbiNGTMmS60GkM9jnyA6clZHu2kefPDBsmzZMjOw7F09nnnmGTnkkEOksLAwa20FkD6MfSA/MfaB/MTYB/JXV8e/Liis47+oqChrbQWQPk4d+wTR4QjhcDgxE2UPrLvvvlseeughszvHnDlzZOjQodluJoA0Y+wD+YmxD+Qnxj6Qvxj/QH4KO2jsE0SHIwfWoYceagZScXGxvPrqq7LHHntku3kAMoSxD+Qnxj6Qnxj7QP5i/AP5KeyQsU8xOeSMHc3n6IDSgaV23XVXGTBggLz77rs5NaAAdB9jH8hPjH0gPzH2gfzF+Afyk+WSsU8mOrKqtrbW1ELS1XmHDBmS2K7d0uPxdHibP//5z/Ld735X3nvvPZk5c2YfthZAujD2gfzE2AfyE2MfyF+MfyA/1bpw7BNER9Z88skn8vWvf90MKq15pAPlxBNPlP3226/TgbVgwQIpKCiQcePGZaHVAHqLsQ/kJ8Y+kJ8Y+0D+YvwD+ekTl459gujIilWrVslee+0lX/va1+SYY46RFStWyG233SYjRoyQc889V0499dQdzlABcB7GPpCfGPtAfmLsA/mL8Q/kp1UuHvuxqu1AH5s7d64MHz5cbrzxRiksLDTbpk+fLr/61a/kj3/8oxQVFcmXv/xlxw0oAJ1j7AP5ibEP5CfGPpC/GP9Afprr4rHPwqLICh1IGzduNDNU9gzUrFmz5Oc//7mUlpbKfffdJ2vXrs12MwGkGWMfyE+MfSA/MfaB/MX4B/JToYvHPkF0ZIXuxtHS0iKvvPKKOW9XFdKVd3/4wx/KE088Ie+8806WWwkg3Rj7QH5i7AP5ibEP5C/GP5CfRrh47BNER5+yB8+ee+4pF1xwgVx44YXy0ksvidfrNQsOqEMOOUT22WcfmTNnTpZbCyBdGPtAfmLsA/mJsQ/kL8Y/kJ+sPBj7BNGRcWvWrJH3339fotGoqXlkD56f/vSncvrpp8vxxx8vTz75pBlYNp/PJ0OHDs1iqwH0FmMfyE+MfSA/MfaB/MX4B/LTmjwb+ywsioz64osvZPfdd5eJEyfKPffcIzNnzjQDxq6T9Nvf/lZKSkrkxBNPlO9973syaNAg2bx5sxmEd955Z7abD6CHGPtAfmLsA/mJsQ/kL8Y/kJ++yMOx77HsfHsgzTZt2mRmnqqqquSjjz6SQCAgd999t6mD1H4V3r/+9a/y+OOPm8UFhg0bJtdff73stttuWWs7gJ5j7AP5ibEP5CfGPpC/GP9AftqUp2OfIDoyRgfSHXfcIeecc46ZnbJnpZIHlnY/e4A1NDSYWarW1lYpLi7OdvMB9BBjH8hPjH0gPzH2gfzF+Afy00d5OvYJoiNjmpubZdGiRTJ9+nQzmHR1Xh1Mfr/fDCxdbECFQiEzawXAHRj7QH5i7AP5ibEP5C/GP5CfmvN07BNER58IBoNSUFBgjnWWyh5YO++8s6mT1K9fP1MjCYC7MPaB/MTYB/ITYx/IX4x/ID8F82jsE0RHnwmHw2Yw2QNLFxoYM2aMPPXUU/Lhhx/KtGnTst1EABnA2AfyE2MfyE+MfSB/Mf6B/BTOk7FPEB1ZGVj19fVmNkoPL774osyYMSPbTQOQQYx9ID8x9oH8xNgH8hfjH8hP4TwY+/5sNwD5RQeU1k664oorpKioSF577TXXzEgBSI2xD+Qnxj6Qnxj7QP5i/AP5yZ8HY9+b7QYg/2zatMksQPDyyy+7bkABSI2xD+Qnxj6Qnxj7QP5i/AP5aZPLxz7lXNDntMvpyr3FxcXZbgqAPsTYB/ITYx/IT4x9IH8x/oH8ZLl87BNEBwAAAAAAAAAgBcq5AAAAAAAAAACQAkF0AAAAAAAAAABSIIgOAAAAAAAAAEAKBNEBAAAAAAAAAEiBIDoAAAAAAAAAACkQRAcAAAAAAAAAIAWC6AAAAAAAAAAApEAQHQAAAAAAAACAFAiiAwAAAAAAAACQAkF0AAAAAAAAAABSIIgOAAAAAAAAAEAKBNEBAAAAAAAAAEiBIDoAAAAAAAAAACkQRAcAAAAAAAAAIAWC6AAAAAAAAAAApEAQHQAAAAAAAACAFAiiAwAAAAAAAACQAkF0AACQcZ988omccsopMmbMGCkqKpIRI0bIEUccIb///e8l36xdu1auueYa+fDDD7t0/XfffVcuvPBCmT59upSWlsro0aPl1FNPlYULF/a4DdFoVP72t7/Jl7/8ZRk1apS535133lmuv/56aWlp6fA2d999t0ydOtW8fxMnTuzwvXv00UfltNNOk5122klKSkpk8uTJcvnll0tNTc121x07dqx4PJ7tDt/5zne6/DzWrFljXot+/fpJRUWFfOUrX5GlS5d2eN0NGzbIt7/9bdP39Dno45977rldepx33nlHvve978kee+whgUDAtDOVO++8U7761a+a90mv93//93/SHTfccIN5X4YMGWJur32lI915rTvrB7/61a9k3Lhx5jXZdddd5YEHHuj0NqFQSKZNm2badsstt+zwMZqamsxzmDNnjqTTqlWr5Nprr5W9995b+vfvL4MGDZKDDz5YXnjhhQ6vr6/L+eefL1VVVaa/H3LIITJv3rw219m8ebP8+te/loMOOshcT/vVvvvuK//+97+3uz99Ph31Xz289dZbXX4eTzzxhMycOdO8/tpnrr76agmHwx1eV5/boYceKpWVlVJeXm76Y0dt602/XLdunfzkJz8xr48+hl63O+/dggUL5NJLL5X99tvPPCe9/fLly3d4uyVLliSu/95776V1XHa3rwAAAHTE3+FWAACANHnzzTdNQEaDN+edd54MHTrUBDU00PS73/1OLrroIsm3ILoGdDSIO2PGjB1e/+abb5Y33njDBMA0yLl+/Xr5wx/+YAJv+hpq8Lu7NLD5zW9+0wQINWg9ePBgmTt3rgngvfjii/LSSy+1CUj9+c9/Ntc7+eST5bLLLpPXXntNLr74YnM/P/7xjxPX0yDl8OHD5ayzzjLvt06eaFv/97//mYBlcXFxm3bo89fAb7JJkyZ16Tk0NDSYflVbWys//elPTRDt1ltvldmzZ5sJioEDByauq/1t//33N6f1eWggXd8HDcJ1hbb///2//2defw1adzaBoe9XfX29CdhpQLK7fv7zn5sxsvvuu8uzzz6b8nrdfa078rOf/UxuuukmMy732msv+c9//iNf+9rXzHt/+umnd3gbnTxZuXJll5+P9hHt70oDl+mibdXX+oQTTpCzzz7bBJ7//ve/m8m5v/71r6Z/J08WHHvssfLRRx/JD3/4QxNEveOOO0x73n//fTMppHQM6GvypS99ybwPfr9fHnnkEfNafPbZZ4nnkUzHgb52ySZMmNCl5/D000+b9ms79HXV91AnsjZu3GiC3snuueceM+mjz++Xv/yl+Hw+E7DWvt0VXe2Xep96XX1NdtllF/OadIde//bbbzcTLTrp1tXJQg286+vd2tra5cfq6rjsTl8BAABIyQIAAMigL33pS1ZVVZW1devW7S7bsGGDlW/effddS3+C3XPPPV26/htvvGG1tra22bZw4UKrsLDQOvPMM3vUBr0/vd/2rr32WtO2559/PrGtqanJGjhwoHXssce2ua4+dmlpqbVly5bEtpdffnm7+7z33nvNfd51111tto8ZM2a7++yOm2++2dzvO++8k9j2+eefWz6fz7riiivaXPeYY46xxo0bZ23atKlHj7V+/XrzOqgLLrjAPG4qy5cvt6LRqDmtr8/ZZ5/drcdatmyZOa6urjaPc/XVV3d4ve681h1ZvXq1FQgEzPOxabsPPPBAa+TIkVY4HO5wvFZWVlrXXXedeZxf//rXO3ycHT2Pnpo/f76572QtLS3WlClTTPuT/fvf/zZteOihhxLbNm7caPXr188644wzEtuWLl1q3r9k+poceuihZrw1NDS0ef3b32d3TZs2zdptt92sUCiU2Pazn/3M8ng8pi8n94ni4mLr4osv7vFjdbVf1tXVWZs3bzan9bnpc+yor6Wit9X7UNo/9PZ2n07lmWeesQoKCqyf//zn5vr6GZnOcdmdvgIAAJAK5VwAAEBG6W76WopESyO0pxnQ7d13331m93zNpB0wYIDJAu0o2/KPf/yjyT7U62l2pWZHa0ZncrarXXLhwQcfNFmkmoGsJQq0tIxmMGvW4/e//33TjrKyMpOR2FEmZFfapI+rWeGasaoZ0lpiQx9Py2Ukt8fOWtXHsss/aGmVVLQsQkFBQZttmiWqr+nnn3/eZrs+py+++MIcd0bvT++3vRNPPNEcJ9/vyy+/bMpcaNmEZBdccIE0NjbKU0891eY16Mp9JgsGg+Z+uuvhhx82r2VyFvCUKVPksMMOM++3TV8PzfjVDGTNTtdyNVqSpDu0tEpXMruVlizqrNzLjugeCl3RnddaM4/1dUh+3pqdq+eT31dt93e/+11ZvXp1hxnIWuZDy8Zo9ntXaBkPLYuidPzZ/T25RI3u9XDggQeaEiv6GaEleVL1lWTa/zWjPFlhYaHJItf2a9Z1cl/R9/Ckk05KbNN2aSkgfR3sMa9lbfT9S6bt1QxmvU6qUkH6WKlKsKSinxN60D0KNAPbpu+HZVmmzbY//elPEolE5LrrrkvshaHX6Y6u9kv9fNTPuJ7S2+p9dJX2wUsuucQcxo8fn/I62n/bZ9B3dVx2p68AAACkQhAdAABklAZvtGTC/Pnzu1QP+hvf+IYJEv/2t781AW4tL6I1ipNrPWupA60TPnLkSBOk1iCcBro0INKRG2+80ZTG0CDgOeecY+pJa1kPPa0lADSopwE2DWbrbv89aZPaunWrHH300bLbbrvJb37zGxPU1XInGsRVWt7ADoRp8Owf//iHOeh9dYcG0LTGd/vA0GOPPWYeQ497QkvFqOT7/eCDD8zxnnvu2ea6Oqng9XoTl3fnPpMDqDrZoBMYGjzW8j5doeU5Pv744+3apHRCRSdu7MCYXfdYA24aYNegmx6OOeaYLtVqdpJUr/UVV1xh+oXWkLfp+6aBa93e/vWzL0+mpW/uvfdeue2227o8SaCBarssiQb47f5uB7P1vTnqqKNM+RIdg1oqSMs/aemdnr43+hpon9JD8nPV8kfaX9s/Vy03s6P1BTrrwzoZpvX4tZ63Tp51tZ53qnGlJXr0cy359dfXST9LtHyJXqZBap0QuvLKK81YcDLtT/q5qeVzUtF+q/1U+3E6ddRXAAAAUqEmOgAAyKgf/OAHJmCp9a81aKUBbw1masBJ61jbVqxYYWpya01grXFt04Cb1ofWGsa6XTOXNXikGcgahLWzOLUuri6Wp0Gm9jRL9JVXXkk8XnV1tfzrX/8yAW8NTNkZoIsXLzY1cq+66qputcmmdba11u7Xv/51c15rGOskgi7Kqa+BBnL1WO9/1qxZXc7obe+f//ynCSzZAfl00QkJDQhqG22a/an1l9vvNaDZ7BrI0+fcGZ2U0Ntr9n8yfb8OOOAAk9msme46gaETFHp/7Scy2tuyZYvJDB42bNh2l9nb9H70vhctWpSYtNA+owsxak1vzYw+/PDDTTDeLUG0VK91R/R9tRcvTfX6JU/a6NoFupCp9tuuBrg1SK9t0ex2fb/b93fdO0AzlzXr3c5+1skwHVs67jRo3x06fnWCTNcP0Nch+bl2NFGV/Fy1/neqvqZ1t/VzK7m/af/XNQI0m1mD65pVrgut6vV0IkCfQ2fsrOpUfTj59dc+rM9HA/Y/+tGPzCSdPk/9XNLPNp0kdCINYv/iF78wr5t+7vSlVH0FAAAgFTLRAQBARunibRok+/KXv2wW9tNArWafaqmTJ554InE9DWhoVqWWWNi0aVPioIssaha4lhVRmumpQVddDDG5DMKZZ54p/fv377ANmkmeHLDfZ599TGBQM9GT6XYt02KXZuhqm2yaUZ0cKNRAm04cpCoD0RNa1kBLqWgwUxfJS6aTCPq89Li7dLFCzXjVhSaTS+80NzdvV07Gptm3enkq999/v5lA0MVD7cUbbfrea0BQy3fo+6CTHNovNNs/1R4FyW2ySzJ01Kbk62jpC6XvmZae0fdSJ3buuusuk7GubXSDzl5rnaDQfpFcKkZfn668fvbtddHLHU1udIcGkXXRSe2ryeVDNNiunxn25FZXaUa5BkR1LwPtw8m681yT6djXzxXd40QX/kym5ZC05Ir2Xf1s071cdKFfnZToSsb0jvpwcpu0D2u2tk786MSZBu91Ik0nAXXvDaeWI9G9dLQk17e+9a1Or6f9VvtvZ2Wv0tVXAAAAUiGIDgAAMk4zgDUgrYEgLQuhQSYN/GiWqmZw2tmWGijRAKCWgUg+aI1kLflgZ4erCRMmtHkMDainqic9evToNucrKyvN8ahRo7bbroEzu6Z4V9tk0yz49pm9GtjX552uzM1jjz3WtFMDeOnKoNTsbC2noJnzmjWcTANNmv3fEa0vnqomsdao1/vTwLiWxNkRfd0uvfRSM4GhteOVBhL1OScf7DapjurXa5uSr2Mfa/A8uZyHBtG0z2jWsB2oTH4c3VvBKbr7WtuvS1dev7q6OjNeNWu8/XjpDXsc694C7WnpDp2s6mqtfK0XrusU6GeJjgstidKT59qeZt8/88wzJhNds793RD+TdFJIJ9e0TXYme3K/sj9bdtSHk9tknz7jjDPaXE/P6xixS79on01+LHsCKRfphIOW9rn11lu3K7OTSTvqKwAAAKlQzgUAAPQZzWi2F4OcNGmSKU/w0EMPmdINGrzWQKrWD+8oOKxZ3j2VKticaru9aF9327Sj++sNDb5pmRXNitWgabqCP88//7zJ1NfgvC5g2FFpCQ086YRBckkXDazrHgEdtUP3ONDsXF1oVQNVyXsMdMYO0mrg0Q7uax9p/1pq5rJm8LZfaFDZ2+x22cdauqT9e6XlaOwJDi0poZm+Ni3D44Sa6T19rfV91WCvvp7JEz/tXz99XfS91lIu9uth7ymgr51u0+um2luhL+heKU8++aTJzj700EM7fK5d6SvJtC9ouSbNVLbLM3W1D9uL5WqJEi39pHtZ2HTvEc2otsu4aBvaT07oNrs2vd0+ndBr34ft8Wj3Yf1ctScnlH6uJi/imkt0LxQtfaOLudr9SidO7OevJZfaT372RV8BAABIhSA6AADICntBPTuQNX78eBPQ06CKBthT0eCmXdNW66rbNINZgzFaDiJdutqm7ujqooztM1OPP/54swCillyZNm1aWtry9ttvmwUf9b148MEHOwzAai17u4yO1n+26XmdZLAvt2mJFC0zoQE+LcnRnckPu+yNZvorzazWIH97mrmqNaw7WsRRn5OWiNDFF+0FUFXyoppKA50atLMfSycStEa7LVV2ci7pzWut75tmWOseFcn9SV8/+3KlwUwN0k6fPr3DEkB60Ezo9v1gR/3dHscLFizosGSR1hnXmuo7ohny99xzj1mgsn2mdvJz1Ykn7a/JWc/6XLUefvux/cc//tEEn7VGv5Yc6Q7tw1qOxX4vdIHh5D1R7IB98rhKDphrLXSdpNAa/jbtwxpE1z6sfTv5usruwxoYTi4Dk3zdXKP9SgP++tnank4K6d427Rdu7q2u9BUAAIBUKOcCAAAyys52bc+ueWyXc9CMTc0O1gzQ9tfX85r1rDTgqxnEWtParl1uB5DSVTbF1tU2dYcdGOxqgEizwDULWOvKa9a+1kLvLFtdA5B2yYjOaPBUs8+1BI5mZqYKGmu2pmZ+33nnnW2263kNQOp92LSExJFHHmkClc8++2wiuNeeZprb5S5soVDIZP1qRrM9OaLZurr4Z/LBpqWA3n333TaBdA3I6mKzWqrFdvDBB5sgs/YPu3yH0mxgbYPW37YDjsmPs//++0su6+prbU9Uab/Q19imZUd0nQDNtk7u07o3gq5XoDW/1cUXXyyPPfZYm8Of//xnc5nWM9fzHQVCbfaire37u763GkjWxUOTL5s/f74899xzbSZsUvn1r39tMuV1cd9LLrkk5fW0r2zYsMGUlLLpBIqOJ52cSq5Lrns/6HPWWuhanz+Vjsr96F4BWuvffl/sAHhyv7InLHRSYsqUKfKXv/ylzVjQcaUTD8mLw+r4V1rz3qYTAhoQ1rFpTxRpn01+rFwOouvzbt+vtHyO0vdUx6tN+6323472JuiqrvYVAACAVMhEBwAAGaWBEV3ITTOeNWikGcBah1qDVRrAtct1aNb39ddfb+ova0b5CSecYLKJly1bZgIsmpmpC0JqkFWzRPV+NcCrta71+hoU1fvoSaZ3Kl1tU3fvUxfu1GCl3pcG1XVB01SBSF0oUgNzGuzT4PN9993X5vLkhUy1Tfp6anCts8VFtR69ZnnrpINmZ+qCm+3baAfrNbj+i1/8wixmqsFpvZ1m9Wo7tP528qKQmhWtmbhaquH11183B5uWorAD1vp89HXVQKE+b31eujCmBlA1s1kXAd2R733ve2YiRYP4+h5oQFiDnvo4+prZNECqATQto3HQQQeZ0hyaBasLMmo5CZ0o2RHNmNX6zcoO2mv77Yzq5HIf//3vf00w1Q7+ffzxx4nraobtjvaU0MfRx9Mxo1599dXE7fVx7Azurr7WSvuvBqu139rrBmj9fs201tdG26mlQB5//HHz3moA0y5NNHPmTHNIZpff0ECwjonOaP/RwLGOd8341v6ipWf0oI+tJYq0r2lNd82i1gU8NQt5R2VItK/rc9f1CrSGevtxoc/fLn+i/Wzfffc1Y0NrYWuWu04eaPA6uYSPrtegeyToJN1hhx3WJpCrdGLBDkxrYFufm27TSRq9Xw0M66RBVxer1OevfUKD7lqnW/v/H/7wB7PQpj6n5AkPbc+NN95ogv9an13fK33PdUKjo8VJ2+tOv7S3f/rpp4k+afcvXTuhMzqBZy/C+sYbb5hjfU76maeHCy+80GzT59yePZkye/bsxJ5KSjPw9fWwS+F0d1x2p68AAACkZAEAAGTQ008/bZ1zzjnWlClTrLKyMqugoMCaMGGCddFFF1kbNmzY7vqPPPKIdcABB1ilpaXmoLe74IILrAULFrS53u23326NGTPGKiwstPbee2/rjTfesPbYYw/r6KOPTlzn5Zdf1vRx66GHHmpz23vuucdsf/fdd9tsv/rqq8326urqbrdp9uzZ1vTp07d7PmeffbZpZ7L//Oc/1rRp0yy/328eT9uTit6vXifVoaPn1dn9qWXLlnV6n9rm9v7yl79YkydPNu/f+PHjrVtvvdWKRqNtrtPZferzsL333nvW8ccfb40YMcLcn/YLfX0ffPBBqztWrVplnXLKKVZFRYW5j+OOO85atGhRh9d94IEHrN122830lyFDhlgXXnihVVdX16XHsfvRjp6X0tcu1XV39L7s6P3WdnT3tU5uk77vySKRiPXLX/7S9E99H7T/3nfffTtso91/fv3rX3fh1bOsN99804xNfQy9nY4z2wsvvGDtv//+VnFxsXkftV989tlnO7xPe6x25bVSW7Zssc4991xr4MCBVklJiXmN2o9/e/x05f373e9+Zz53BgwYYMbxsGHDrLPOOitl/0vlscces2bMmGH65ciRI62f//znVjAY3O569fX11iWXXGINHTrUvI677LJLl96rnvTLrn7edPezpf3nYHupPpft+2z/udTVcdndvgIAANARj/6XOsQOAADgDFreQEtaaGaxZigDAAAAAJAO1EQHAACOo7Wt2+cB/P3vfzdlQbQGNgAAAAAA6UImOgAAcJw5c+bIpZdeamp0a/3iefPmmUX3tN7t+++/b+qmAwAAAACQDiwsCgAAHEcXRxw1apTcfvvtJvtcFyvUBQF1QT8C6AAAAACAdCITHQAAAAAAAACAFKiJDgAAAAAAAACAW8u5RKNRWbt2rZSXl4vH48l2cwAAAAAAAAAAvaQFVOrr62X48OHi9WY3F9zxQXQNoGtNVAAAAAAAAACAu6xatUpGjhyZ1TY4PoiuGej2i1lRUZGWzPbq6mqpqqrK+gwH3IN+hXSjTyHd6FPIBPoV0o0+hXSjTyET6FdIN/oU8rVP1dXVmeRpO/6bTY4PotslXDSAnq4gektLi7mvXO5EcBb6FdKNPoV0o08hE+hXSDf6FNKNPoVMoF8h3ehTyPc+5cmBEt65/yoBAAAAAAAAAJAlBNEBAAAAAAAAAEiBIDoAAAAAAAAAAG6tiQ4AAAAAAAAAndUADwaD2W5GTr0eoVDI1EXPZk30QCAgPp9PnIAgOgAAAAAAAABX0uD5smXLTOAYMZZlmdejvr4+64t29uvXT4YOHZr1duwIQXQAAAAAAAAArgwWr1u3zmQ7jxo1KqtZ17n2uoTDYfH7/VkLXluWJU1NTbJx40ZzftiwYZLLCKIDAAAAAAAAcB0NFGugdvjw4VJSUpLt5uSMXAiiq+LiYnOsgfTBgwfndGkXpl8AAAAAAAAAuE4kEjHHBQUF2W4KUrAnN7RGey4jiA4AAAAAAADAtXK93nY+8zjkvSGI7nBWXYNEq7f0+PbRtRt7dXuniW7YbA5AplihsERXrDW7RnVHdPUGiW6uyVi7tnu8zTUSXb1enCS6pVasLbXZbgbQZ6yWVgnPeUesYCwjw6qpl+jytel/nGhUosvWmGMn08/dyKIVEl1X3eHl0fWb8vo3gNXcYl4fp7/P7UXXVkt045aM3bfV1NJmmxW1JLJ45XbbAQBA70RXrBNra912263GZonqd283/8YG0o2a6A4WWbBMQn9+aNsGbzdnbqLtPoC6e3unyfLzrbREgi5/idGun3W1jyXfRm/SxVnYHvcpfbjkHyBOGPvaXqv7rxG6h8+pHBP/bAg/MSc2Tnvy+dKNx0n7/fZ1v+rsez7ffvNk4X3uS4k+lcn3NdXr5aLXMd28u06Sgm98JdvNADKawNb6u/tEauu79FmQi7+rvDuNksB3ThMPn1/ootD/XpXIy++0/fuxJ9p/Z9vsvtjmb2JP7G++jm7bg77bWl4i1hF7SLSoWqL+gGSU1yueqv7iKcjw4yArCKI7mdXFD6Wu6u3tnaaPn6/5qM+zlzjv9aSPtQ9w90WfctrY78ZrhO7hcyqHtR+nmRq3GbjfrPWrzp6L0z730s3hzz9ln+rrceHw1zHdoh8uEOu0oHgKqTkLd4ouXS3SQZZsqs+CXPxdpdm8Ut8gUlme7abAIaIfLRCJZHAvto7GT3ICVVeuvyOd3V+66R5/rUGRLAXRr7nmGnn88cflww8/zMrjux1BdAfzThglhVd/N3amp1mZejuCURkXjUZl06ZNMmjQIPF6qaLkejqmujsmuzkW09KnnDb+7baShZ4RfE7lKH0vkstvZGrc9uRzKxf7VcAvEo50/Bo57TMvE1zwGmzXpzL5nFKNCxe8junWes0d5tiqbySIDtfSkhLKO2msBE49ypR28g4bJJ4OAtK5+Luq9Vd/FWlqEau5tcM2A6nKlarA+V8V7/Cq3pUiXlst3vGjdKVPU6LQO3qYeEqKuvb92ovvXivYKp4N68UzeIB4ipIeL82s2gaRxmaxWoPiKS/N2OMgewiiO5jH72cG2SE80ahYLU3iqSgTT478iIKz0aeQbvQpZAL9CulGn8pRpcUmcJDRbEUgy6LzF8VOlBaJZ0Cl+PfexVGfVRqsNOs5sKYDuiMeRPf0Kzf9uaf0tt6RQxPnvYMHpqV5XXrsFr9ItVc8Pp85ZIplT7w79LswGAxKQQET4Z3JjU9zAAAAAIAz2UGJcCzYArhScTyD1ePQMIom4WmgT/fYAroqvsB9tsqTZIIuUKrZ4uk+aM0YKxQSKxhKfb1uZNP/5S9/keHDh5s9W5J95StfkXPOOcecvummm2TIkCFSXl4u5557rrS0dH2S7Jvf/KacfPLJcsMNN5jHmTx5stk+duxY+cUvfiFnnHGGlJaWyogRI+SPf/xjm9dPy8aMHj1aCgsLzW0vvvhiyQdkogMAAAAAeszj98XKzYadmX0HdEkkFnz2jhshjuRnsgvdYwK+dia6lsxzi2BIWq+4LSsPXXjj90W6WPbsq1/9qlx00UXy8ssvy2GHHWa2bdmyRZ555hn53//+Jw8++KAJZmuA+4ADDpB//OMfcvvtt8tOO+3U5fbofffr10+ef/75Ntt//etfy09/+lO59tpr5dlnn5VLLrlEJk2aJEcccYQ88sgjcuutt8q//vUvmT59uqxfv14++ugjyQcuGgUAAAAAgD7n87YJMgKuZPdvOxjtNHa7HVpqAlmQvNeCm4LoDtG/f3855phj5P77708E0R9++GGz1sIhhxxiAueafa4Hdf3118sLL7zQrWx0zTS/6667TEZ5sv33319+8pOfmNMaPH/jjTdM4FyD6CtXrpShQ4fK4YcfLoFAwGSk77333pIPGAUAAAAAgF6Xc7EIoiMPAoqZrKmcSdru2B4jZKKjm6Vc3BZELwjEMsLTzJRxqd5ivhO9QwelfOzuOPPMM+W8886TO+64wwS6//nPf8rpp59uFiz+/PPP5Tvf+U6b68+aNctkl3fVzjvv3GEddL2f9udvu+22RIa8ntaM96OPPlq+9KUvyfHHHy/+eMkoN3NoMS8AAAAAQC5luFprq7PdEiBjErXEnZ6JTk10dFW8lIt4Y4tyuoXH4xFPYUH6DwUB8QQCZjHhlNexFx/tIg1Oa1mdp556SlatWiWvvfaaCaynS0lJSbdvM2rUKFmwYIEJ7BcXF8v3vvc9OeiggyQUSpp0cSmC6AAAAACAHrM21WyftQi4jV0GxalB9HjZpcjnS7LdEjiEtWmr+7LQ+6K0mb527RYD7amioiI56aSTTAb6Aw88YBb/nDlzprls6tSp8vbbb7e5/ltvvZWWx21/P3p+6tSpifMaPNcAv9ZgnzNnjsydO1c++eQTcTtGAgAAAACgx3wzJkvkrY8p5wJ3s8ugODUj1y5HU1Kc7ZbAIaz6xtiJ1mC2m+IIHr8/VjJJRa20pS1r5vlxxx0nn376qZx11lmJ7brY5//93//JnnvuaWqYa6Bdr9OdhUVT0Rrov/rVr+SEE04wi44+9NBDJhte/e1vf5NIJCL77LOPyWS/7777TFB9zJgx4nYE0QEAAAAAPWfXeKVMBNzM4Zno3oljJLpwBeMU3e7z3kljs90S59BqLSaSngin99qhhx4qAwYMMCVUvva1ryW2n3baabJkyRL50Y9+ZBYTPfnkk+W73/2uPPvss71+zMsvv1zee+89ufbaa6WiokJ++9vfylFHHWUu69evn9x0001y2WWXmWD6LrvsIv/9739l4MCB4nYE0QEAAAAAPWcvJkYmOtws3r8dWxvaXruAhUXR3XUAAg7t89mgNc8tK50xdLOI6Nq1azu87Kc//ak5JLv55pu7dL/33HOPhFN8Hmjg/MEHH+zwshNOOMEc8hE10QEAAAAAva8DS4YrXMz5C4vGJ7sYp+gqe2LUqRNHWRFfOFQD6XAdgugAAAAAgF7VgVXRVeuz3RQgc8IODyjGg//RZWuy3RI4BUH0HsfQc0FZWVnKw2uvvZbt5jkS5VwAAAAAAD2WWFC0sTnbTQEyxy574NRMdC0zoVpas90SOIXT977I5jjLgUz0Dz/8MOVlI0aMSHnZ8uXLM9Qi5yOIDgAAAADoMe/IIWJCLQH+vISLS7k0tzo6oOgdMaTtQsDADkQ+W2KOPXbJLnRdDgTRJ0yY0OnlVg600WkYCQAAAACAHvOUFMdOsLAoXMraUps47aksF0cqjAfPWVgUXeQpLTHHVksw201xXiY6XIkgOgAAAACg5+KZuYmFFwG3sft2abF4HJqJ7rHrWoej2W4KnCI+MeqdPDbbLXGOHCrngvQjiA4AAAAA6Dk7qEgQHW4ViWdvO7lkkT1OIxHKOKBr4p/piQkY7JidiM4QcyWC6AAAAACAnktkuFImAi7lhmBicgZ9hGx0dGPRaIfufZEdZKK7GUF0AAAAAEDPkYkOl0uUKnJyMDF5AoD1C9AVbuj3WctEJ4juRgTRAQAAAAA9lqgRTWAOLmWtXOf8YGJy25nwQlfYn+lO3gMjSzK9GOv//d//yQknnJDRx8D2CKIDAAAAAHrOH68THbXEilImAi4Uzyq1ttSJU3m8XhFvPE2WCS90hT3ZQhC9++yxBlchiA4AAAAA6DkyXOF28Rrivt2niKPFg6GJ8jRAZ+KTLYm9jbBDnsKCbDcBGUQQHQAAAADQcwTRkS8LLDo9IzexCDDjFDuWmGxxer9vx7IsCYWbM3OItMQOoaYOL9fH7o6HH35YdtllFykuLpaBAwfK4YcfLo2NjYnLb7nlFhk2bJi57IILLpBQKJSBVwy2+H53AAAAAAD0gJaJ0D3XNTYQDme7NUDGMtHF5/A8RBYBRo8WFnV4v28nHGmRu/+9f1Ye+9zT3pCAv7hL1123bp2cccYZ8qtf/UpOPPFEqa+vl9deey0RiH/55ZdNAF2PFy9eLKeddprMmDFDzjvvvAw/i/xFEB0AAAAA0GMej0fE6zO7/kc3bhFfRVm2mwSkl1sychOLADPZhS6w98Cw171An9IgejgclpNOOknGjBljtmlWuq1///7yhz/8QXw+n0yZMkWOPfZYefHFFwmiZ1DGRkIkEpFrrrlG7rvvPlm/fr0MHz7crB7785//PPYjK74LxdVXXy133XWX1NTUyP777y933nmnTJw4MVPNAgAAAABkKtjS3JrtlgDpF3VJJno8FhNdsU68o4ZluzVwzOSRw/t9O35fkckIzwSrsVmsrbEFiL0jh3T42F212267yWGHHWYC50cddZQceeSRcsopp5jguZo+fboJoNs0K/2TTz5Jy/NAxzI2Em6++WYTENdZkc8//9yc110Qfv/73yeuo+dvv/12+dOf/iRvv/22lJaWmo7R0tKSqWYBAAAAANLMM35U22A64CLRddXm2OP0TPSmljbBdCAVK2qJBGP1tT0uy0TXxF4tqZKRQ0GJBHxFEgiUdHi5nVTcFRogf/755+Xpp5+WadOmmXjq5MmTZdmyZebyQCCw3fOK2hN+yIiMjYQ333xTvvKVr5jdCdTYsWPlgQcekHfeeSeRhX7bbbeZzHS9nvr73/8uQ4YMkccff1xOP/30Du+3tbXVHGx1dbEZHu0o6egseh/aNjoe0ol+hXSjTyHd6FPIBPoV0o0+leN10fU9CofF46D3hz6FrrAzS7V/d6Wv5Gq/8k4bL9EPPhcr1LXngdzR133K2rB522MXFzjqcz3Va2cfMsqecEjjY+23337mcOWVV5rY6qOPPpq4LPkx7NPdedye3CYT7Pemo9huLn1WZSyIrm/wX/7yF1m4cKFMmjRJPvroI3n99dflt7/9rblcZ060zIuuLGurrKyUffbZR+bOnZsyiH7jjTfKtddeu9326urqtGSw65tTW1tr3jxv/Icg0Fv0K6QbfQrpRp9CJtCvkG70qdxVGgmL5sTVb62R4MaN4hT0KXRFud8nmoNeXxyQUBf6d672q+JQUApFpKG2VlodNE7R933Kt2GzlGtw0+OR6q1bxclCoZB5/bS+uB4yKhIxnxW60nZvH0uTkF966SU54ogjpKqqypzX2KcdY7WfU/tAdFcfV6+rpbhVdzLkM0HbrM9n8+bN22XY64Kqrg+i/+QnPzFZ4lrcXndB0DfmhhtukDPPPNNcrgF0pZnnyfS8fVlHrrjiCrnssssS5/UxRo0aZTpURUVFr9utb5p2Hr2/XPqyg7PRr5Bu9CmkG30KmUC/QrrRp3JXqLhYNI+tvLRUfIMHi1PQp9AVwXiAqXL4UPF2oX/nar8Kl5WJ5nSWFRVLpYPGKfq+T0WbwqKhWE//Chns8L6iCbcaiPX7/eaQUR6v+S7U/3r7WFr7/I033jBlXDT2qYuL3nLLLXLcccfJI488YvpB8mNo/9BDdx+3fdA6G/x+v3k+AwcOlKKitnXj25/Ppoz1ngcffFD++c9/yv3332+K3X/44Yfy/e9/3ywwevbZZ/f4fgsLC82hPX2x0/VBop0unfcHKPoV0o0+hXSjTyET6FdIN/pUbvL4fSZw4InmVuZtV9CnsCNaykL7tzcQC/Q4tV/pODUi0ZxqFyT3+lS8hIb2Gaf3FW2/HWDOdMa1Zu4n683jaR30Z555psPL/va3v2237Xe/+1237l8z0e32ZTsT3RN/bzrq37nU/zIWRP/hD39ostHtsiy6muyKFStMORYNog8dOtRs37Bhg1lB1qbnZ8yYkalmAQAAAADSzRf7I9dasyHbLQHSzorEa/J6Hb6waDyIbi1fk+2WIMdZ9iLRTl9Mt68lx6K1zjiL+LpKxsL5TU1N280WaFkXuyD8uHHjTCD9xRdfTFyuuye8/fbbMmvWrEw1CwAAAACQbk2x9amslmC2WwKknx1Ej08WOVZrsG2AFEiloTl2bO+9gK4haO5qGctEP/74400N9NGjR5tyLh988IFZVPScc84xl2uavpZ3uf7662XixIkmqK4rzWq5lxNOOCFTzQIAAAAApJl33AiJfrGsbRYe4BZ20NnhAUXv2BESmfsRgT7sUHR1fK3CMBMuPaaZ6HCVjAXRtfC9BsW/973vycaNG01w/Nvf/rZcddVViev86Ec/ksbGRjn//POlpqZGDjjgAFPvJ5eKxgMAAAAAdqCsJHYcIuACF0qUc3F4JnpxfH05AqPYkfhik54S4nPdoQnDidA5MXTXyVgQvby8XG677TZz6KxzXXfddeYAAAAAAHAmjz/+pyXBObhR1CXlXOxxSjkX7Eg4bI48o2LrGaIbdE8PzUInE911HP4NAAAAAADIOrvMRTzwArhKPOjscfoii/YkAJNd6OreFw4vYZQViWpJBNHdhiA6AAAAACAtGa4sWAi3sTSb1CULi7LHCLqdie70iaNssNccIIbuOs7+BgAAAAAAZJ+drRgiEx0uE02KhDm9Jro/1n4mu7BDZKL3QjyIzjhzHYd/AwAAAAAAcqecC0EDuExyIMzhmejiIxMdXROZvyh2gkz0nn9mMM5cx+HfAAAAAACAbPMQRIfbM3LdkJUbz0QnQxY74ikvbbuoLrquqCDbLUCGEEQHAAAAAPROPLhImQi4TnKf9jo9iE4mOrooHjz3jh2e7ZY4j/05oesp5JCxY8fKbbfdlu1mOBpBdAAAAABAespEUBMdbmNn4no84vHGax07FHuMoNt7YDh94igbnP0xgU4QRAcAAAAA9E4gHmhpbBaL3f/hIlZdgzvqoSfXt7Ysxik6legfbuj37ViWJc3hcOYO0Yg0RyLSFA5td5k+dndEo1G58cYbZdy4cVJcXCy77babPPzww+ayPffcU2655ZbEdU844QQJBALS0BD7zFq9erV4PB5ZvHixHHzwwbJixQq59NJLzTY9oPvi6QIAAAAAAPSMp7I8cdraUiueQf2z2h4gXayt9e7J3k6u6R4MiRQVZrM1cEQmuvuC6C2RiBz8xL+z8thzvnyaFNtllbpAA+j33Xef/OlPf5KJEyfKq6++KmeddZZUVVXJ7NmzZc6cOfKDH/zABOdfe+016devn7z++uty9NFHyyuvvCIjRoyQCRMmyKOPPmoC8Oeff76cd955GX2ObkYQHQAAAADQK56CgEjAHyvnkrwQI+B0oZA58owfJY6nYzTOWlctnnEjs9oc5DAXZ6I7RWtrq/zyl7+UF154QWbNmmW27bTTTiZI/uc//1nOOOMMufvuuyUSicj8+fOloKBATjvtNBNY1yC6HmugXQ0YMEB8Pp+Ul5fL0KFDzbbuZsWDIDoAAAAAIB00q9UE0V2QsQvEWcF4EL2oQJzOo1nFOuEVDInlhsx6ZI6LM9GLfD6TEZ4pVkOjWDUNIoV+8VYN3O6xu0rLsDQ1NckRRxzRZnswGJTdd99dDjzwQKmvr5cPPvhA3nzzTRMw17ItN910k7meZqL/8Ic/TNOzgiKIDgAAAADoPTtjkSA6XMRavjZ2IhAQN9BSS9bajewxgrzNRNd64N0pqdJdVmGhWL5mEa9fvL14HLu2+VNPPWXKsiQrLCw0pVu0RItmnM+dO9cE2w866CCTjb5w4UJZtGhRIhMd6UEQHQAAAADQax6fT8zO4WGCc3APKxyOnWhsEldgsgtdEe8f+rmO7oov2tnLcinTpk0zwfKVK1emDIbr9pdfflneeecdueGGG0zZlqlTp5rTw4YNk0mTJiWuq+VetPQLes59U0oAAAAAgKwtWmjxRzrcJB4I804aK65gLy5KJjpSMLWyI+7NRO+rGHpvg+hav1wXDb300kvl3nvvlSVLlsi8efPk97//vTmvtHzLs88+K36/X6ZMmZLY9s9//nO7wPvYsWPNwqRr1qyRTZs29apt+YrRAAAAAADovXjGorW5JtstAdLHDiZqzX8XSGQWM9mFVJLr5SctRosu8thR9N77xS9+IVdeeaXceOONJsNcFwzV8i7jxo0zl2td9Gg02iZgrkF0zTjX42TXXXedLF++XMaPHy9VVVVpa2M+YTQAAAAAAHrNqovVb5Xa+DHgBnaw2S0ZufZkF+MUqdgljFQGa4e7lh1DT8PivVq//ZJLLjGHjmj5Fg2iJzvhhBNiexO0s++++8pHH32UON/RddA5l3wLAAAAAACyyTtuZLabAGQsE90ttaGtUMgcR9dVZ7spyFWh8LaMardMHvWlpM8Ki7JJrsJoAAAAAAD0mqeyzBxbaci+A3KGyzLRPf3KY8eU6UAKVmNz7ETAbzKh0T1tJtzI9nYVd3wLAAAAAAByZMFCguhwj0QmqUsy0b3DBrfNNgbasTZsjp0IxvZaQA/Ykw8E0V2FIDoAAAAAIH1BdDLR4SZhd2WiSyBeEz257jWQLB749Qzsl+2WOBdBdFdyybcAAAAAACAXdmG3ttRmuylA+kTdlYluLxRprY9nGwOp1gEgiN5zdhUcguiuQhAdAAAAANBrVjxYEF21PttNATKQie6SILo3Ft2z1m/KdkuQo6zExBEhwx4jE92VGBEAAAAAgF7zDupvjj1lJdluCpA+8YCix++O8Il3yKDYCcYpUrHXAfC6o89nN4ie7YYgnRgRAAAAAIBe8/Qrj52g1jLcmInudUkmemlR7Ji1C5AKmei9Rya6KzEiAAAAAABpq7VMcA6uLG3hkkz0RFmaCOMUO8hEJ4jecwTRXYkRAQAAAADoPX98YVGC6HATl2Wi2wsAE0RHSlG7zxMyzObCogcffLB8//vf79Ztli9fLh6PRz788EPpKx6PRx5//HHJB/FUAQAAAAAAeh9Ep5wLXKWhyV2Z6PY4jUTFilriiS80Ctisxpa2Ey7oPo83K5noo0aNknXr1smgQfG1D/rAunXrpH//2JoobkcQHQAAAACQxiA6Ga5wB6spFkxUnkBAXCE5MKoZx17CQmgrumx121JGyEomencFg0EpKCiQoUOHSl8a2sePl00umUoFAAAAAGSTh5rocBmrsWlb/+5fIa6QXOeasYoOeMpKYseFBeJGlmVJOJThQ9hjdsoKB6XNdn3s7giHw3LhhRdKZWWlyS6/8sorE/cxduxY+cUvfiHf+MY3pKKiQs4///ztyrnMmTPHnH/xxRdlzz33lJKSEtlvv/1kwYIFbR7nP//5j8ycOVOKiopkp512kmuvvdY8dnfLuSyPP/6jjz4qhxxyiHm83XbbTebOnStuwJQjAAAAACB9mehRykTAJewgc2mxuG6cJi8gCSSL9wvPiMHiRpGwyL9v3zZBlrlwa3n89LbHOu3iEvF3Y6eWe++9V84991x555135L333jOB8tGjR8t5551nLr/lllvkqquukquvvrrT+/nZz34mv/nNb6Sqqkq+853vyDnnnCOvv/66uey1114zgfjbb79dDjzwQFmyZIl5HLWj++3s8bRtEydONKfPOOMMWbx4sfjtyXaHcnbrAQAAAAC5F5zTDLYCl5S/QP6yF99M7tsO59HFInWCK2qxuCg6Fu8X1ETPPq1xfuutt5rs7smTJ8snn3xizttB9EMPPVQuv/zyxPU1E7wjN9xwg8yePduc/slPfiLHHnustLS0mKD2ddddZ7adffbZ5nLNRNcM9x/96Ec9DqL/4Ac/MI+hNKt9+vTpJog+ZcoUcTKC6AAAAACANGe4ahCGIDocLhx1ZzBRn09Ua00QREcH7MmV5NI/LuLzxzLCM8mqbRSrvkGkpFi8AyraPHZ37LvvviaAbps1a5bJKI/E3yMt0dIVu+66a+L0sGHDzPHGjRtl+PDh8tFHH8kbb7xhAu02vX8Nsjc1NZmSLN21a4rHI4gOAAAAAEByoJHgHNzArgnsokz0xFgNhcWKRBLrHwI2Kz551OYz3UU0KN2dkio9YRWIWBpx9VviDWRulJWWlnbpeoGkhZHtoHw0vnBsQ0ODyRY/6aSTtrud1kjviUAnj+dkBNEBAAAAAL2WnC0XXb9JfOVd++MeyFXRTVvdGUy0JwWY7EKelDHqc/b3YffWEd3O22+/3eb8W2+9ZeqM+9L4maQLiupCoxMmTEjbfboVQXQAAAAAQHoFQ9luAdB7DbEFAa2tteIqdpmO5tZstwQ5Xc6FIHqvg+itwV7dzcqVK+Wyyy6Tb3/72zJv3jz5/e9/b8q5pNOVV14pxx9/vFmw9JRTThGv12tKvMyfP1+uv/76tD6W07mzwBEAAAAAoM95xo2MnSDDFW4QzyL1Th4nrhIP7EXXbsx2S5CLInY5F0KGvWb1LhX9G9/4hjQ3N8vee+8tF1xwgVxyySVy/vnnSzodddRR8uSTT8pzzz0ne+21l6nDrouXjhkzJq2P4wZkogMAAAAA0sLj98biji6ofQrY/dhTWixu4ikvFasluC1bFuggE911C+r2pYJ4TfBeDLE5c+YkTt95553bXb58+fLtto0dO1aspMD9wQcf3Oa8mjFjhtmmh3B83QcNpOuhJ6yk+2//+Kpfv37bbXMqppUAAAAAAOnhpdYy3MOyM3K97gqdeEYOjZ1gnKLTTHSC6NmuiY7cktFvgjVr1shZZ50lAwcOlOLiYtlll13kvffeS1yuMxFXXXWVDBs2zFx++OGHy6JFizLZJAAAAABApvi9bYOPgCtqQ7sriJ5YMNJ+fkASy55ciX+eowcSe3k4O4r+z3/+U8rKyjo8TJ8+XfJNxsq5bN26Vfbff3855JBD5Omnn5aqqioTIO/fv3/iOr/61a/k9ttvl3vvvVfGjRtnitnr7gOfffaZFBUVZappAAAAAIBMZqITnIMbRF2aiW5nGDNO0REWFu29pBi6JhB7HFo66ctf/rLss88+HV4WCMRL1uSRjAXRb775Zhk1apTcc889iW0aKLdpJ7rtttvk5z//uXzlK18x2/7+97/LkCFD5PHHH5fTTz+9w/ttbW01B1tdXZ05jkaj5tBbeh/atnTcF2CjXyHd6FNIN/oUMoF+hXSjTzlAPGM3ummrI94n+hQ6Y8WDiZbX260+kuv9yrLHaSics21EFvuU3e89Hlf0D/u1sw99TgPpOZqRbr8eqV4XO+t8R7fvLfu96Si2m0t9MGNB9CeeeMJklX/1q1+VV155RUaMGCHf+9735LzzzjOXL1u2TNavX29KuNgqKyvNDMfcuXNTBtFvvPFGufbaa7fbXl1dLS0tLb1ut745tbW15s3zumy2GdlDv0K60aeQbvQpZAL9CulGn8p9pfUNorlprWs2yJaNGyXX0afQmeKGRikUkabmJmnpRn/O9X5V1Noquu9/8OMFsmXmpGw3BznWp/rporMisqlmq1hWSJwuFAqZ108X0bQX0sy4qCV2Hn84FBLx5l4muvaliL2IbJYz5UPx92jz5s3bZbjX19eL64PoS5cuNavHXnbZZfLTn/5U3n33Xbn44ouloKBAzj77bBNAV5p5nkzP25d15IorrjD3mZyJrhnvWi6moqKi1+3WN007j95fLn7ZwZnoV0g3+hTSjT6FTKBfId3oU7kvPGKIRJeslsLyMikdPFhyHX0KnQkXFormQJZWVEhFN/pzrversD9gnlegslwGO2Ccou/6lLWlVuyw+aCRI8RTrNNIzqYBWg3E6mvo92csDNqWZlbHT/p1DYIc/BzIpbIswWDQ9OuhQ4eKr10ZoVwq952x3qOdc88993M+TnMAAQAASURBVJRf/vKX5vzuu+8u8+fPlz/96U8miN5ThYWF5tCevtjp+iDRD6Z03h+g6FdIN/oU0o0+hUygXyHd6FO5zTuovwnOaTkAp7xH9CmkFF8g1+P3dbt/5HK/8k0aI9G3PjLPLxfbh+z1qWjrtsxzX2mxuIEm85aWlpoKFhow7os+b8qThGOvpaelZds6BDlE26iZ+ZqNnq1MdMuypKmpybw3uoZmRwH9XPqMylgQfdiwYTJt2rQ226ZOnSqPPPKIOa2zC2rDhg3mujY9P2PGjEw1CwAAAACQKZpxp1iwEG5gl34I9FH2al+P0zDjFO3YfaJ/7ys95AoNEGvcUctKr1ixos8e19paZzLSPU11OZmJbtcg1yB1tsu59OvXLxEnzmUZ+ybYf//9ZcGCBW22LVy4UMaMGZNYZFRfoBdffDERNNfSLG+//bZ897vfzVSzAAAAAAAZ4rF3lSc4BzcIxftxX5WA6Ct2ViyTXWgvEk7sfeEmmo0+ceJEUzakr7Q+dq9IMCSB878q3gGVkmvsGuQDBw7MarZ3IBDYroRLrsrYN8Gll14q++23nynncuqpp8o777wjf/nLX8xB6SzH97//fbn++utNR9ag+pVXXinDhw+XE044IVPNAgAAAABkSjzwYhFEh4sy0T1koiNPJD67HRLU7A4NFPdpfe3moEhjsxR4feLNobreyUF0DWDra5JLJVNyWca+Cfbaay957LHHzEKg1113nQmS33bbbXLmmWcmrvOjH/1IGhsb5fzzz5eamho54IAD5JlnnsmpovEAAAAAgC4iwxUuEl24PHbCZUH0RJYx4xTtWDX1sRMuy0TPCr4PXSej3wTHHXecOaSi2egaYNcDAAAAAMDhyHCFm1jizoAie4wgBat6a+xEc2u2m+KKySr9CLHWbRIZMSTbzUEakK8PAAAAAEgPguhwYX/2Dh8sruJj7QKk4IuFCT2DB2S7JY5nbamNHfdhHXZkFkF0AAAAAEBaeOzd1+O1pAFHi0bbBBZdwx9/PpSZQIo+7xnUP9stcTzv7lPbLlAMx3PZNwEAAAAAIOtlIiLx4CPgUJZliUTj9Vzctuien0x0pBBx6cRRFiQWJGacuQajAgAAAACQHpRzgduy0F0YUPTYz4dMdKTq926bOMrq9yF7ZrkFowIAAAAAkN6gQX2jWHYWL+BEyXtTuDUTPWqJlTxZANj93m19PhvimejR1euz3RKkCaMCAAAAAJAWnsryxGmrpi6rbQF6JTm47LaAoj3ZpRqbs9kS5GpNdJftfZEVwZA5shhjrsGoAAAAAACkhaewQMTriZ0JsQs7HMzNmegFgcRJa1NNVpuCHEMmetp4Rg5tu+A2HI9RAQAAAABIn9KS2DF10eGaTPT4xJBLeDwe8QweYE5b1EVHMmqip42nLPZdyELb7sGoAAAAAACkjSdeB5bF1OBklr0nhddrgs6ukxinBNGxTaJGPuVceo8FfF2HUQEAAAAASH+9ZYJzcDBrXXXshFsX3rRLTBDgQzK7PxBET98Y47vQNRgVAAAAAIC0Bw6iy1ZnuyVAz1lW7DheksF1mOxCR0Lx/uCP76mAHksszspElWsQRAcAAAAApI1V3xg74eHPTThYPLhs1w53G3uxQ6ulNdtNQS72e3uSBT0Xfw2tjVuy3RKkCb9qAAAAAABp49tlYuwENdHhZPHFAN0eTIwuWZXtJiCHWPbntl0zHz2X9BomXlc4GkF0AAAAAED6xMsAWOzCDgdL9F+vS4Po3thiqZ6iwmy3BLmktj527PLJo77gGVa17UxLMJtNQZoQRAcAAAAApI9dB5Zay3AyO4jud2fYxDtuZOwE4xRJrE01bRfFRI95vF4RPSjGmSu489sAAAAAAJAd9oJ0BA3ggnIurg0m2vWa2WMEyWI7KLh2LYA+xzhzFYLoAAAAAIC0SdSQJogOB4t+uiR2ws4kdZvEOKVWM2KsaFTEip2mzE+a8H3oKi79NgAAAAAAZAWZd3CD8pLYcdCltYwJ7iHV3hfJZbnQO0xWuQqjAgAAAACQPnb5C4JzcLJ4//VOHCuuRNkltJc88enWPTD6mIdx5iqMCgAAAABA+pDhClctLOrOmugeJrvQXjRey0WRiZ4efB+6CqMCAAAAAJD+oEGE3dfhYGF3B9ETZZcoMwEbmejpF5+sYpy5A6MCAAAAAJA2LCwKNwUUPW7NyGWcIlVNdK9HPB5PtlvjDowzV3HptwEAAAAAIJuZd9Glq7PdEqDHrEQmerymsdtQqxntWFE7iO7SvS+ygXHmKgTRAQAAAADpQ61luCkr16WZ6PYeI9bGzdluCnKEtSHeF7xkoad9nLUGs90UpIE7vw0AAAAAAFnhGTHY3bWkkR/sGsb2pJDbBOIZspHotqx75Dc70BsMZbsl7hGfj4guWpHtliANCKIDAAAAANLGkwjORcSyrGw3B+hdJrpLJ4M8w6u2nSFLFioUmzjyThmX7Za4R7y2vKe4KNstQRoQRAcAAAAApI+duavx8yhBdDh8YVGXBtHNOPW0fa7Ib1Yo5O51ALLAO25k7AR7e7gCQXQAAAAAQPok15C2S2IADpMoceLSci4ezZD1xYKllHOBsmobYifsvYnQe/bnBxNVrkAQHQAAAACQPsmZu3ZJDMBpXB5EbzNWmeyCBtFXrI2fYA+itPHHwq4WQXRXIIgOAAAAAEgfb9KfmSxQB6eqb4wdu7WcS/JzCxHgg4iUxOp2e0qLs90SF2aiM6HsBgTRAQAAAADpLRMRF11XndW2AD1h2QF07c/xwKIrxcdqdNW6bLcEuSASy0D3DB2U7Za4bqLK2rA52y1BGhBEBwAAAACkV0Egdswu7HAgK2kPCk95qbhWc2vsmOodUNHo9utaIC2sDZuy3QSkASMDAAAAAJBWnpFDYidYsBBOZE/+FBeKm3mnj4+dYJwiueRIckku9IrXzuovLcl2U5AGjAwAAAAAQFp5EgsWEpyDg4OJbl5UtE29ZsYptmWie8hET5+SeH15vgtdgZEBAAAAAMhMHVgCB3CiPMnITUx2EURHcj9web/vS0wouwsjAwAAAACQXj5/7DgYzHZLgF5kors8ZGI/PwJ80EnPaLw4PkH09ElMVIXFslh8wOkYGQAAAACA9PJ6zFH0i2XZbgnQfdFIfpS1iJdzseoast0S5FImutv7fTZKJllJC7fCsRgZAAAAAID0isXQRcpYTA3OY+VLTfR4Zmx0xbpstwS5wA7ykome/kx0xR4fjhffxy7zbrrpJrniiivkkksukdtuu81sa2lpkcsvv1z+9a9/SWtrqxx11FFyxx13yJAh8ZXcAQAAAACO4x07UqIfLiBogLSy6hsleNfDmc+ctvuty4OJnvLS2HFZfPFD5KTQk69I5L1PpSISkWBHWeJ1jbFjfT/tCcyeiN+Pp6iwF3eCNvz+tp8rvLSO1idB9HfffVf+/Oc/y6677tpm+6WXXipPPfWUPPTQQ1JZWSkXXnihnHTSSfLGG2/0RbMAAAAAAJngjwd6WLAQaRRdsFys1Rv67PE8wwb1+LZa/3hhzVZZXF8jy+pqJeD1ysTK/rLn4KFSWZAbkTTPkIHmmAWAc3uviMjL75i9BnY4pVMfD6b3UKtX5N2hFWJ5WqR8w1oZU15h+mqhzyc+T+pHbwgFZUNTk2wNtkhjKGR2cCjy+6XE75eKggLpV1AoFQWF4vX0JsLvTKYklD5vfVH4PnS8jAfRGxoa5Mwzz5S77rpLrr/++sT22tpaufvuu+X++++XQw891Gy75557ZOrUqfLWW2/Jvvvu2+H9aca6Hmx1dXXmOBqNmkNv6X3ol1067guw0a+QbvQppBt9CplAv0K60aecw4pn8FqhcE6/X/QpZ4l8sdQceyaPFd+xB2X2wTweE2RO1Te2trbIA4u/kOqWZhlcXCL7VA01p/+7YqksqasxgcVwBwsJahhxav+BMrqsXFo1szgSkbpQULa0tkgkaonf6xG/12uC7oOKik0Qs8DrM9drjUakPhQ0gcqo9luxTGzOPm2Orfi2+Pmw9vH443riQUz7tCcUFtlziHj8UfE887h4xSP9i4pkcmV/mdyvv0yq7C9jyipMQBTZYenizPF+VPe1o6T/4MHiia85kbjO+k0iW+vFM3lMt/eeaIyEZX59rbyytVpe2bpRasIhkQ/e3u56RT6fDCwqlv4FhaZfB6MRCUWipt826m12QFtc6g+YQLoetCtqfzP90Cyjocd6Wex6BT6fhKJR03/1uCUSlkiK8dTm/A4C9V25/nbX2cE12t/FdtffY3CsJvrrz4vH65XyggKZ3n+gGV+T+vWXIcUlZpz39SSDU77/ojnUvox/El5wwQVy7LHHyuGHH94miP7+++9LKBQy221TpkyR0aNHy9y5c1MG0W+88Ua59tprt9teXV1tysOk483RAL92JK/Ld91C36FfId3oU0g3+hQygX6FdKNPOUegsVG0UESwuUW2btwouYo+5SwljU1SoP0qHJbGjEczLJFNmzq8ZGVTg1z0YdtA498Xfrbd9QIer1QEAjKlvFLK/AH5vK5GVjY3ymdbN5vDjiyqrZGMK4q/kE2xLOY1TQ0yf0vb563BThPc9+ghFuAv9wfMcxtWVCKjiktkTGmZjCkpk8qAvkNIm9ag9Iuf3Fzkl7BfP6vaBVtHDoodOqCfbXcvXyRvba42AerpFf2l2OeTja0tsqKpwRwnG1BQKMOKiqUuFJI1zU1mMka1RCKyprHBHDpS7veb977E5zeBaZ0g0tvUh0MmUK/30tCFYLsr2WOspSl23CTyRc2W7a5mjzP93Cg3Y6tYhheVmONhxSUyrbyfye7Pt++/+vp6yRUZ/drRWufz5s0z5VzaW79+vRTobh397I+DGK2HrpelonXVL7vssjaZ6KNGjZKqqiqpqKhISyfSAa/3l8udCM5Cv0K60aeQbvQpZAL9CulGn3KO6LqtEhYxGbSlgwdLrqJPOUvIHzDBuKJdJ2etX2kW+EVvvpg4f8b4ybKxpVk+3bJJygMFcuCwkXLg0OESrKuXySNGSKE/0Ob2G5ub5L3qDSaTXctk6BgpCwRMlq9moGvmrR40CLm5tVlqg60mE7fQ65NCn99ct8QfMMG25Oxdr8dryn1of/aZTF+POa/3qSFXfd00kdeKB0X1/8iqdRJ+5AWRARXiP+t4k+m7rqlRFtRulYU1W2RhbY3JfNdAajBqSdDcMlaSYnMwViHg49qtbZ6fZhEPLymVYXooLTPHmmmr7dayHvo8s5F16yQ1dStk7ge/lSk7fUXGDthX7NBzvwH9pWrw4G59Vl365hx5a+O2hWM3VG+/iGxVUbHsP3SEzB42UvasGmL6jNI9GYJ2MDwUlM0t2h+Dpu8VxPuuvpf6/hZ3EtwNRSPmdqbUi9lDInbfEj8d26Z7U8SC/nXBoESsaGKPjIDp+z4zgbNN26z09jnq7ZPW7X6fSvKl2oZO73u7G3d+/eDdj4g0tYr/9GPEU9VfNjQ3yWdbNplxtqK+zmTz6222jbOomXhY39IsH8i2YPvfDzlaBlf2l3z7/isqKhLXB9FXrVplFhF9/vnn0/qECwsLzaE9fcPT9aabL5s03h+g6FdIN/oU0o0+hUygXyHd6FPOYAXif2pGIjn/XtGnnMMTiZhgk7eoMCvvlwbXTnn+v4nzv9v/ENl3yPAOg1MbQxETQG/fzqGlZXJcaZnkgmhdqwQbQuIpjEjhoNikxAwROSbpOnXBVhNENcF9KxbgbwlrCZpW2dLSIisb6mRhPBi4urHBlPZYVFdjDqloMHRISYkJqmspnOGlZTK8pEyGl5aa07pNJwry1fyFD8iqtW+Yw3nHvhTbqBMj8c+prvb9F1avSATQDx0xWo4ZNU4W1201gXGdzJhQ2U/GlVdKv8KOY3ZmEsbnkxLNUC8uljEVlT16PoVerwzO4/ezJegRqQ9KoDEsvqmxcXbkqLGJy03WfjhsJstCVqxMzqaWZlndWC+rGuKHxnoZVV6R9s89J3z/eXOobRkLomu5lo0bN8rMmTMT2yKRiLz66qvyhz/8QZ599lkJBoNSU1PTJht9w4YNMnTo0Ew1CwAAAACQaXZWIgupIZ3s/qSL9WXBbz9+X2riGdjfnrZrhwF0R/H7zJEV1v1GOqYLQnZ1n//mcFjWNzXK2qYGWdvYIGubGmVdY4OpFa814mtaW83rp8F4uzTIpx2UtdHceTugPkyD6/Gsdg2uVwQKzLFmtu+o/rVTWda2z82Wlq1i3qUeBBJ/9eG7iXrmN+5zoDl90PCR6WsoumZrXWKNkI5olr0eko0ur5CZVUP6pHnIgSD6YYcdJp988kmbbd/85jdN3fMf//jHpgRLIBCQF198UU4++WRz+YIFC2TlypUya9asTDULAAAAANBHwTkJE0RH+ljh+AJz7QJOfeGDTRvkwSULzOlZQ4bLOVN2EddMdqVpnGpJj3EVleaQimaya5atBts1qL6+ubFNwF0D8E3hsMlq10MqWrJmQGGRyageWlIqAwuLzIKNuhhrVVGJDCouNsH2gUVF4mtTBiT3eb3bsrZDoeZYEL2bE0fPrlpuSgGpOw88It1NRDd4d5ss0Y8W8H3oAhkLopeXl8vOO+/cZltpaakMHDgwsf3cc8819c0HDBhg6plfdNFFJoCealFRAAAAAEDu89hBToIGyEQmuj1J01cPa0XlO6++kDj/m/1miytkYZxqnWsNeushlU3NzaZMjAbU1zXGMtu1Vnt1c5PUhYLxmtmWyXDXQ0eLNCYH22PBdA2uF0v/eOBdtw0qKpKyQIFUFBRIRaDQHNv1wDOlqXmTqXneGqyV1mCDBAIl0tpaI6FwixQWlMm4UYdJJBKrPq/Coeb4E+leu37/yTxzrAtRThswML1PAj0bZ+yZ5XgZX8+6M7feequpbaOZ6K2trXLUUUfJHXfckc0mAQAAAADSVSaCoAFyMIiui4C+um61qUW81+ChJrO8s4UuL3o9XpdaRP500BGOy2xOxZOje4xoFrkeZkrH5SxaImGpbW01CzJqVvu6eFa71m83gfXmJrNdD/ZiqXroCi0XM6CoyCyYabLci0tkcEmpCbprEL5K21ZUbBa7TEWD4Gs3vi+hUJNs2PSJNDVXS0uwTtZtnCfhcDwonsKct65rcz4RRO9GcP/F1SvM66B+u98hXb4d8mucIceD6HPmzGlzXhcc/eMf/2gOAAAAAACXZd7V1Ge7JXATOwjVi3Iut378vvxr8ReJ8w8s/kJ2qqiUH8/YW2bEF9dM9sTyxfJ+9QZz+vARo2X3Dq7jWHZwTxdstSzH1Bgv8vmlqMQvQzrJZrf3INjU3CIbmhvNIqh6rDXZN7c0y8bmJrOtPhSS+lDQHJTJdI+f7kypPyAjS8uksrBQ+hUUSEG0SZrrFkqwcblEW6vFLyEptFqk2GqSIqtRiqRZvGZZXH3Zi6SwoEIamzdKSfEgc9i0ZVufTBbeuDF2ohvvzc3xWujKVf3VBeMMzpbVTHQAAAAAgAsVbqvpa7W0iqeoMKvNgUvEg1CJckHd9P8+/yQRQNfA+S4DquT51ctlaV2tfPvV5+XIkWPkx7vvYxatVO9uXC83zHvbnNYs5BviizO6hl0TXTU2i5SViJvoHgNDSkrMYUc04N4QDJns9s2tzSbTXbO5NzQ1yobmJpPdXh3Pbg9Fo9IYDsmC2q3t7mVA7FDQ8WMUez3Sr7BYBhQVmwVb+xUWmlruUlAogyYFpNTvkyIJSmPdAvn8k9skYIUkbC/62tDUpef8vxVLE7XQ/3TQ4V26DTIs/nllbanNdkvQSwTRAQAAAABp5RmwbWFBq7E5ZRBd6x4/t2q5+D1eOX7seFM+AUjFsjPR/d0vp9IcDstdn39sTmtO7/2HHWsyry/ceYZc9/5b8tq61fLc6hXy1oZ1ctJOE8WyRO5d+Gni9v887FhxnaJt0V5rU414XBZE727AXbPK9TBOtl8YVTP1l6x4Tlavny8fLfmfNHlKpcFTIa2eYmnxFEurFImnaKh4C4eIp6C/RH1lJstdg/JaakZz0JujljQ3N8m65i4ExIvOM0cPbA1J6b7DpMzjk7KP3pEhZeWmrntlQUE8Cz52Wmu762Nd+/7cxKTP7oM6LoeDPhaNLYgcXRvfqwCORRAdAAAAAJBWpixESZFIU0uiBIdmchb4fKbWsPpo80Y5/5XnE7f524JP5fLd9jTBdKBDkWjKci4a5FSpSpLcMO+txOkXjj81cT3NCL5l1mx5dtUy+f0nH5hsY+2LtmElpfLHAw8zAUu30dfAM3iAWBu3UGoiBa1r/uFn98r8hQ+axUCV9gQt1TJAtsroYfvL7tPPkaoBU8XnK0iZ5V4fjJWJ0YC6lpHR07XBFtmsp4NBk9muNd0b4uVlqptqJRwP2TX6vdIolmxorJcljV0rkXXvocek8VVAb3j6V8SO9TsRjkYQHQAAAACQsTqwLa2t8qPXXpR3q9eLVzxy6IhR8n9Tdm4TQB9ZWi6rG+vl+nlvydOrlsl5U3eV8kBAPtxULa+vXy1rGxtNSWANaGp25X5Dh8v4in6dLgaJ/KmJvrV2mTzy9JkybMgecuwhv9/uZsFIRJ5fvcKcPnzkmES5lmRHjRonhwwfLf9buUw+3lwtYSsqewwaYiZ1XN3P7FITLHrYRijcLG9/cLt8seQ/Eo60JLYP6DdBxow4UCaNO076V47rcpZ7v8Iicxhd1rXH//eTX5XNtcvkwIEXS+Frm6Rp+k6yasYECRUVmAx3DbhvbW01pVv0oEH4Qq9PpvUfKOdN29VkqyM3eAb2M8dWOD4JCMciiA4AAAAASDuP329KGHz703dlYXOD2RYVS15Ys9IcbH89+CiZWNlfbvzgbRPA1EUc36/eFmBPtry+TuZuWCd3fPqhWdivoqDABKj8Xo/4PHrwmoBnoc8ng4tLZGhJqQwpLjFlYvoXFprbFPn9UuYvMFnxAc+2DGY4gJ0tbS/UF/fqO780gc5Va9+Q1tY6KSyMZX7a/vrFJ4nTV87cN+Xda584YdwEc8gbLHrYRmuwXp588btSveWzNtvHjTpE9plxkfSrGNsn7fCIR7wSlYpIRIY0h8XrLZShAwbJ4MGDxevtfjkj5MAYs+vbw7EIogMAAAAA0s/vk3mVBYkA+lkTp8qkfv3lr1/MN8Hw8RWV8v1d95DpAwaZy6/ecz85bfwUeWDx5yZQHopGZEq/gSbrXLMro5Yly+tr5Y31a2Tepo0m81IPvaXBdy0xowH5ikCBKe9RGgiYbOX+Wm+4sFDKAwVS4vebIHyJP2AuN6cDfin2+VOWEEH6WBrkDYY6XFh0a+2SxOl11R/I2JGzt93OsuSeeHmWmYOGmEkUdBTgy+8guvaTxSuelRff+JmeS2wfP+ZI2X+PH0hJcexzqs/EP1Isu4RRu4kjOHGiikx0p+PbAwAAAACQdlZTizwxojRx/sKddzfB5iNHjjXBbw1Ctw8+T+k/QK7da/+U97nX4KHy1fGTJRyNyor6OmmOhE294UjUMkH2sGWZ803hkGxsbpYNTY2yobkpvrhfi1lcUh+7KSkjMGJZ5nI99ISWqNFguj4fDbxXFhTJwKIiE3gv1qz3QMCcLrWD74GACbzrZXowQXl/QPxkl3bK1O22JS2AGY2GpaW1JnF+0bL/tQmi/2f5tgD7T2fu0xdNdRSdkLDiCwDncwD9zfdvkU8WPJDYNmjAFDlm9u+ktKQqK23STPQ2ewh0sA4AnCEx6cfeHo5HEB0AAAAAkH7DBsrLVbHMux/P2DsRMNfjskDHC/B1lQacx1fG6sz2hAbcQ9GotIRCsnLDegmUl0tjJGzqDOsifxqErw0GpSbYYo4bQrotLI2hWPa7Xt4UCpvyNHrQxQD1sMHEIWOLD3aXBtY14K6BdQ2qa/1kPa8Z8MUmA94O1Mey4ot8/kTwXgPzdtBe6yK7MjM+FJ/4KCsRT1JWbiTadm+EJSufl8OtmxKvwf2LPjfHfo9XRpWV92WLHcGKxsaotXKtyKzdJN+sWjdXnnv1RxIKN5rzg/pPkeMP/7MUFmS5r8T7rxXtuIQRHIS9PVyDIDoAAAAAIO3erypPBJSPHNU3dYS7yq6bHvB4pKqwSAb369/tOsOavdoSiSQC63pcH9LAe6tsaWlOZLxrAD4WmI9lwev5lnBYmiMRaQ6HJBgPYmpWvR7SUZ4mVnbGb0qXxGrHF24LvCdnwMfL0uhrUeTzmfO63c6U14C9bs+JoHw8iO4pLmyzORIJbnfVzxc/KtMmnizvbVwvKxrqzLbfHXBIHzXUWTxFBbHiJYW9m9hyYu3zR545S+rqVyW27TrlLNl390vE682FgHX7THT2VHH84r1kojseQXQAAAAAQNot90VNaeFy0czzgLiNBpbtkiyDpLjH96OlaTS4roF2E2CPhKU+GDLBeJPxHg6ZMjR2QF4z3jXYroH45EC9XaJGy9PUaeA+FEzP89Qs+XjQ3Q7Ca3DdDrzbAXo9rwfNmjfZ8fFtepuixO1j1ynUbT5ft0rYWHYmeru+FE1konukrGSINDStlzfn/VamTjhJLnj9xcRz2GPQkLS8Hm7jGTlU5LOleZUlu3r922bxUNuo4fvL3rt+V6oGTpNcsa2cSzSxUDMcikx012AUAgAAAADS7hGryRyfINvqV2N7GkjWhU310BtaC97UfDeZ8bEge1MkliGvJWk0OK+Xa/a7Btzt+vB6eWs0Yq7fEA/Y2wctVaNZynr95Dry6aIlVuyJiOJ4sL0skQ2vAflYKRsTcF+yWgIjyqSo0idlK5YkAvqRYI1Ue4ZKoc8je+15vTz92hUSifrlglf+l3icW/c7JDey6XNQojROHmTJavb5a+/cKItXPJPYNnP6ubL3jAsk58S7a9Qu50ImunPZNdEJojseQXQAAAAAQFppqZM1EgsYDI8SvOwLPo/XZIj3tt588nuogXMNvmuwvdk+HYoF4e2gvGbQ62ktbdMaiQXbm+zgfCRittv3oef1YAtbUVMCRw9dMq4ydvz+W223F51pjh559zORoq/Htm2JLTa635DhMmvo8LS8Jq4UD6Jb8Yxnt2pu2SIPPHGCBEMN5vzYkYfI7H1+LsVF/SUXJTLRm3XBY58ImejOn6giiO54jEIAAAAAQFqtbKhPnD4ozJ+dTqSZ26ZmeiAgA3tRrqaj4LxmvpsAezgizZGk7PeIlqaJlbDR4HtzIngfkeblq6VlXbWEBveX1iEDY5dFItIUbJKtDRsk7CmUosJK8URbJRzcKoXSIieM30W+NePgtLXd3Vmy6d/TIFesXPuGPPvqDyQSaTXnD973Gpky/suS22JB9OiaDToVmchMhwPl0d4ebsevGQAAAABAWq1tjGV7+qKWVITdneGK7gfnTZ10n18qu5E0H1rdJJGFC8U3ZCcJ7L9tkdDqLZ/LI0/fIKXFg+Xrx8XKdPzrvydJTd1yqVwzQWTGEdtqY6CTILo7x+nKNW/I/+ZcZE77fIVy+P6/lHGjHLDIbLz8kKesxKzP7KnUhZrh6DFmWWJFo+Lp5iLWyB28cwAAAACAtFrf3GiO99nawi7sSI9oPMjrbRsQj0RipWB8vm0R+UP3+4U53lK7WJavfrUvW+k8Ls6SXbPhvUQAXX39xKedEUBPKucSW5VARCrLstsg9H6M6ftZs20vLTgPmegOtq6pQV5Zu1p8Ho+pf+f3xo7Nea9HCrw+s9ubXm9zS4vZJU5n+ysKCswCLnod+7Z67I2fDnjbXhbbHjsEvLr6eoFZQV53tdvY3CTrm5okFI2Yx7NXardvr7f16v2LfdpjFs6JtVW/FmLbNBtBr6OTrfb1vOa8Xt8jwUjE1Mnb1NJsnovSxyvwec3q7qad8dub+43f3r4vc7/x8/YK8Loboa5cr/+iVux8NH46amrzxVZ5TzyPpPuzH8MXv79tj7mt/bHnFjuvM45bmhultaFeAj6faXswqrsnxl7DDc1N5jUt8vukzF8gZQVtV5xPe121tN5n+mVmzR93Pfdo1JKauhpZ5/OIt90fEtvdp0Oee4be+My8Txm5zwy8T924y2g0Klsa6mRzwCfePs6OyPZz7/J9pv8uM3evOfL89bNqS2OD1NUWdPhZ5ZT33infIfr7S8sj6G+a9rbfknRZigsTf7h3Q0/uy+rGds3g2lpfKxv8sd+oXb8vqwdt7oyVtvtK2bZObtQ3z7P796W/pcsDBbLrwCr5fOsWs21ES0TE494yEehDdsdr9zslGo31L693W3hj8MDpUjVgmlRv+UzWbZwn40ZR0iUVj8+dQfQ169+V/7747cT5r5/0rBQV9hOn0e89g4VFHctTmLTLTW29yID42g5wHILoDraivk5u/fj9bDcDAAAAABJOGDtB5m5Ya07vtbVFLOniopFAZ+yFL9sF0SPR7TPR1bhRh5ogemuwru/a6OSFRV20x8ji5c/KC29ckTh/6rEPSWlxlTiJJukZVnK/7/7kNnKDZ1iVWOuqxQq5Z5zlI4LoDjagsEiOGDnGZFNHotHYsWUfW9ISDkuR3y9VRcUyuLjELAij2+pCQYlENePa2u42mg2th44u02262rpmaGu2erHPL/0KC2VEaZkU+vwm86kxFJTGcNhc174Pzeredn/bHieWAW4fpN15zQrXjPDYdQt8PikLBGRQUbEMLCwyaWaanW4Opr3RxG2sdveRnHFuP7adJW5nrCcy4OPZ7/p1pRn1sSTybc9/+/ZFJWQeP9bW5OegX2/J28L6o8/jMa+pvlaaQa+v28CiIhleUmZeU91zoC7YarL80y1VxlCv7tMhX+JOee7dukdLk0XC4vP5O01nzMRzz8SPt0w0MzPP3Un9vvvtjESi4uskyyUj71Mm7jNTb34eP/fe3Kvu5dDR3g2519K+/A7JwH3Gf6/obybdQy5d2e+JP+K7cX8pt/dgN4Ht91awJBKJiM/nS3l/O9o/q+O2defaqdrW+X1Jmu8r9evf/RulfPzU99ThZYvrakw/fHz5YnO+wOORGXWtYtWs7uSegO5morftfdFIbA9ir7ft3ryFBbEa0guWPiGHzLqmr1rpPPZvP5cE0T9f/Ji88nasnE/AXyonH/NP6VcxWpwnXs4l3u9j33nO+I2LTkq6uHgB33xAEN3BJvUbINfvfUC2m4EuBhA2btwogwcP7vMyCXAn+hTSjT6FTKBfId3oU7lNk0X2e+yBxPk9KwZIYXS1SIZKFSLPxMtatF+UrzUUX8S2XRC9uGhA/HhgnzXRkfx+15Rz0QD6q+/cYE4XFJTLacc+LKUlzspA3yY+WZRczsXOSodzx1mIILqT8csTAAAAANBruk7RV8ZOSJyfPWxk7ASZd8jgwqLrqz/s8OqDB+1sjptbNjtmL7WscEEmumVF5fHnzjEZ6Hp61LD95OsnPuPgAHpyOZeO1wKAs3gCsUz06OoN2W4KeoFMdAAAAABAWlyx+96yy4BBsrm1WY4fPk5MoY2oZRbHa59BDHSL1tBU7fqRXQu9fU305EUkN239QqoGTO2LVjqOx66JvmGzONWr7/wyMZmy06jD5LD9b9iuPzhVYgJI+71z5znynlUb22NGgrHyU3AmgugAAAAAgLRlTx4/drw5bbW0brtAs1wLCKKj53Qipv1iAcFQg3zyxf3m9PDBe7a5vt9XmDjd3LKlr5rpOJ5+FYnTVmtQPIXOCj4vWPpf+Xzxo+b0sKrd5ciDfi1ukFirIrGwaE9WNkGu8E4bLxGdqLI/x+BI/IoBAAAAAGSuBqzDS0UgN1jrN8VOJC2Cvmrt3DYB9faGD4kF1luD9X3RRGeqKHPsOP104cPy8tyrzemCQJkcd9id4hr2ZFGijBHhOyfzFMcn9Shv5miMQgAAAABA+iUFO63NNVltClxUDiEU6TBwPnbk7O1uE/CXmONwuLkvmuhIHh2ndpazgwJ8C5f9T15795fmtNfjlzNPeMo1JVxiYu+JZSegE0R3NrtsksMmqtAWoxAAAAAAkLmF8TRwUN+Y1bbA+TwlRbHjoQMT20Lx4PjIofvKsMG7b3cbX7ykSySSVFoIKfcacUqAb/nqV+SlN3+eOH/Oqa9KYUG5uPXz0yhy0wRBHu+Z5ZAxho4RRAcAAAAAZIRn7PDYCQIH6K34Aoue4lgwXW2pWWKOy0qHdHgTfzwzORwJ9kkTHcvnc8w4XbryRXnmlUvNaa83IN846Xnx+7f1CbexxNJVcsVjv0dwdCa6k/b2wPYIogMAAAAAMiIR+InkfnAOOS5RG3pbhm59w+pOg+R2eY8IQfSulZrI8bJL6zbOk+de+2Hi/BlfflxKirftmeD2vTDgXB4y0V2BIDoAAAAAIDMCBA6QJlFru9rQdrmWgf0mdHgT+/LV69/uixY6V7zcUi6XXdq05Qv5z/PfSpz/+knPSnnpMHErT7wmevuJIzh8oipEJrqTEUQHAAAAAGR4MTUCB+gdq4NMdLvWeVnJ0A5vEwzWm2OfL9AXTXQs79SdYici8dc4x9Q3rpOHn/5a4vxpxz0spcVV4mqJmugWi4q6qpwLE8pOxkgEAAAAAGQGgQOkOxPdsy2MEYmG2pRtaW/ksH3NsRWvp44UCgtydpzqe3f/f45PnD/xqHulf2U86O9qsSC6pUcE0Z2PvbJcgZEIAAAAAMgM6sAiXexAeJtM9GCnQXSfN7Y9Gg+2w1mLHmoA/b8vflssK5YhP3ufK2XIoF0kH7Qt50Lozi3rg1gbNmW7KegFRiIAAAAAILMLixJER28lyrlsC2NUb/ms0yC61xsr40IQvXMef24uAPzmvN/I2g3vmdO7Tf26TJ1wouSNRAxdy7lQE93xAvGSUpGoWDk2ztB1BNEBAAAAOFo0GjEZqXrQ08lZjE3NmyUUbu6zcg52eYkdqalbadrV2eV6X5qB2dJaI+FIqzS3bDWHjp6L3pf93MPhFnOdxqZqc3u9TE/bl+tlkci2dra21iXO29ePXScowVCD9Ao10ZH2ci6e7cZaSdGgDm/iiwfRuzou832PESuHJrsWLn1SPvnifnN68MCdZdbMSyWf2JnopteTie54nmFJn1FBvg+dKr5vHQAAAADkPg0Eb9j0ibz69nWytW5ZRh5DA3Iej1camze22T5q2H6yat2bHd6msnyM1NavSJz3+4pkYP/J0ti0QQKBUtlau0TcbNbMy2S3qWdtfwE10ZGhTHSdLLJVlo/u8CZeXyzkEY0QtOpUjo3TrbXL5KW5V5nTXo9fvnLE3ZJ/KOfiytJmikx0xyKIDgAAACCnLVn5grz69g3SGqztk8draum4ZmmqALpKDqCrcKRFNmz6SPLF3Hm/lZ1GHSrlZcPbXkBNdGSoJrodRPd4fOL1dhza0ACs2lK7uK9a6Uw5VHZJ94R58KmvJs5/89RXxOeLl8LIJ/E9LiwP5VzcwKPvoR50jxqC6I5FEB0AAABATolGw7K1dql8vvgx+WLpExLupOyJGla1uxQUlEswWG8CD6XFVVJbv8pkk2/cPL/D22jgzbK2/0O2tGSItAbrtnvMiWOPkcLCfjJ/wQNdeg4Bf4kMGzxTVq593WSkb966wGzvXzFOwtGg1DesMW0YO/Ig8fuKpam52gSPautXmsfvVzFWykqGStQKy+jh+0tdw1qT1V5c1F+aW7ZIU/Mm/atcSosHS0PTOpOJ6/MVSkGg1Dz28lUvS3PrVtl9+jlSU7dMigr7m+cUCrfIouX/k0ik1bRnwthjZNSwWRIKNcjcebfJwP6TpLRksCxb9VKsvZU7ydCqGbJm/TvSGqyXoVW7yvrqjyUSDUphQYU0N282bVSLlj8tM3c+t+3rHM9wjcz7TAInHtal1w7oLBPdY2eiR5oTe3144gHH9gKBksRpLVmUl8HY7tREz4GyS/97+aLEQqLHHnqHBPzFko+SFxa1+zxcMFkVDZu66HAmgugAAAAAskrrb2vt7SUrn5dX375+h9c/eJ9rZPzYIzoNnmXKAXv+cIfPRQNAXm88KJUl+8y4MOVlB+97ZYfbd558eo8e69FnvmEmK9ZtnCci53aY4eoZUNmj+wZS1UQPhZrMcWdBVp0ESt47hCB6ComFRbMb3Ju/8MH450js82jUsH2z2p5cEd20NdtNQDr4vCIhMQuLsm+BMxFEBwAAANDnNNisGdIr174pny16ZLtyKMmmTzpVdp50qpSXjpRNm7bI4MGDxZujmXka1NcM83wyavh+Joiu2fLteUYPjZ0IZT/DFc5lFtO1a6JrIEpENm75zBwXFfVLeTt7YVF7Dxfk7gLA1Vs+l9ffvcmc1r1c9t+j8wlLt7MniC2xxLfLpGw3B+ksm0QmumNlNIh+4403yqOPPipffPGFFBcXy3777Sc333yzTJ48OXGdlpYWufzyy+Vf//qXtLa2ylFHHSV33HGHDBkyJJNNAwAAAJAFi5c/K+998mdpbtnaaY3zA/e6QqZOOLFNreOoHURDThk+eKa8b8plxErEJPME4u8fQXT0RvLYjweitCRSckZ6R7Skk36GaAA9Eg1lvp1OFV+7wFqxLisPr+/PI0+fmTh/5glP9fleRrkn6fkXsAeFK8QnAKU1mO2WIBeD6K+88opccMEFstdee0k4HJaf/vSncuSRR8pnn30mpaWl5jqXXnqpPPXUU/LQQw9JZWWlXHjhhXLSSSfJG2+8kcmmAQAAAOjDLNIVa16VL5Y8LstXv7Ld5Vpze9rEk2XcyEPa1DDuSGuzR1YuCEuw1SM+v4jX5xFNSjcHnykTLgUFHiks8UhxqUf8gXwPxGReedkIc9zSWrP9hfEgurW5RqxQeFtQHeiO5MzNeCDKZKfrmgiDd+/0pl5vwARpowTRU4vv2WPV1Gfl4R977puJ06ccc79Z2yHfJSYRPEnBV7hC+I0PpGBMu0W44QgZ/QXzzDPPtDn/t7/9zex6+f7778tBBx0ktbW1cvfdd8v9998vhx56qLnOPffcI1OnTpW33npL9t2X+lcAAACAU4XDLfLeJ3+RDz/7W4eXjx5+gAmejx05O+V9RKOWVK+JypqlEVm3Iiw11WWae9rlNhQUiRQWeSRQ6JGCQj1vn/ZIWWUsAK+Bd6/XY441bmEH5G3xWJ3uV7/tKPl08vnEhran9fL2141GrDaxwUS4Pynu3+EUQEeXd3DFDhM529y28wfqyn3ra1U2oF+i5rQujtqmRnVhQeJk5KW3xX/U/h09I6Bzkch2meh2ULykuKrTm2pJl7A0E0TvhHf8qNiJSERCj74ggZMO77PH1onV6s2fmtOTdzpeBg2Y0mePndu2lXPxEER3Bc+g/mLVNUr0vU/FOuZA8fSvyHaT0E19mgagQXM1YMAAc6zB9FAoJIcfvu0DesqUKTJ69GiZO3duh0F0LfmiB1tdXV1i18507N6p96Ez2uwqinSiXyHd6FNIN/oUMoF+lb8Wr3hWXnrzZykXBZ0w9uhOy7REwpasXxmV1UsismZJRFqb295HxQCPOUTCGoiOVXqworGAu54OtYq0NFnm8mCLHjRqnRzRRjrpHgGe/oPE8m2SdRs+kJFJiwFaSQuKRmvqc+7zgM8pZ9C9GGxRj4gnGpVIJBYU1zUIOnv/NBNdhcPBPnufndavrAHbgnmRz5eILxpLcsy0puZN8swrlybex9n7XO2Y16yvNBY0ieXxOK5PYXv+kw6X0C2xpIJIbb14KzUpIHuc0qeiOdQ+f18+6e9///uy//77y84772y2rV+/XgoKCqRfv7YLgWg9dL0sVZ31a6+9drvt1dXVpr56OtqpwX7tSLm6WBGch36FdKNPId3oU8gE+lX++XzpfbJg2f3bbR/Uf1cZN+I4GVa1rwme6+Kg7enfSFvW+WTjyoBsXOmXSHhb+rO/wJKqkWGprApJoGyLDBpcvsM+pVng4aBIa4tXwq0eCYf0vEdCQY85DrZ4pLXJI5alh9j1NRAfO9aAxQ6yujWQ1+Z8LEhvtnk6u9628yYD3tcuuJ+cyd7hE9s+LbxNxnuHt+nk/lJc0D5rPtXVazboeyUSaD5agmX3yYaNy6XAt1Ob6xceMEOKX/9QmpubpXnjRsklfE45g6e+SXQ6xvJ6zN/+qrExVnqkublVNnbWr6zY+1q9ab2Eg73L/NxS+4UUFw6S4qJBrutXvjOPlvJ/PiORcKTz1zONnnntrMTpw2f9vz57XCeoq9+YCKI3xD87ndan0I5XpKKiVLx1jbJ161aJFGV3EXKnfE7V12enzFRWg+haG33+/Pny+uuv9+p+rrjiCrnsssvaZKKPGjVKqqqqpKKiIi2dSGtP6f3lcieCs9CvkG70KaQbfQqZQL/KD5pJ+Pjz35SGxu0XpJu1+2UyefxXUta31T/cajZZsmJBRJZ9GpaWpPUBi8tERo73ycgJPhk8wmtqn2ufqq4O06dyyGtPtMrqJVEpDxwkm+U+2VT7juyx2+ltrhMpKxUtxlFcXCTlgwdLLuFzyhksX40p4uTx+UyJWFWwNBbOqCjvl9jWkUCgUJpbRSory2VwVc/738JlT8qr711j1gA4/bjHO1340on9KtocFs3393m9nb6e6fLux3dKSzA2oXrwvtfIuDG7ZPwxnaRq4ETZUvuZBCIBKausMJ+dTutT2F4wXo6qf//+4s3y96FTPqeKiookr4Louljok08+Ka+++qqMHDkysX3o0KESDAalpqamTTb6hg0bzGUdKSwsNIf29A1P15uunSid9wco+hXSjT6FdKNPIRPoV+5VU7dcXp57jWzY9PF2lx2871Uyeacvi0dTrTsQDlmy8MOQLP4kLPVbt+U1a83yMVP8MnqSX4aM8nYYpKJP5ZYho/2yeklQglunigzVv8v827030Xg/0HczF983+lTui6zbFDsR3ZYxaVmxEi8+X0Gn753WRDfXl0iv3uM5b11jjusb1kgwVCfFRf3d1a+S2pnpNm/c/Jl88Ond5vTA/pNlyvgvZ/TxnKisdMi2mujx98RxfQrb0fdQf/V44+9ltjmhT3lzqG0ZDaJrZslFF10kjz32mMyZM0fGjRvX5vI99thDAoGAvPjii3LyySebbQsWLJCVK1fKrFmzMtk0AAAAAD2wtXapzHnrF7Jh00dttg+onCBHzf6NVJbHF6jrQM2mqCyYF5LlX4RNeRWlfxsNG+szwfMxk3wm4xzOMXysT963z1gF0tKydfsr2W8pZenR24VFkxYYXbT8GXOcvL5CR7y+2OK2vVlY9JMvHmhzvjW44yC68/TNZ6/GiR5/7puJ8yccEQumo63EJLSWAKuKrSsIF+H70JH8mS7hcv/998t//vMfKS8vT9Q5r6yslOLiYnN87rnnmvIsutiolmPRoLsG0DtaVBQAAABAdkSjYXn46a/JlprFbbaPHn6AyTwvKR6UMmCydllEPn0nJNVrthUaLy7zyPS9AjJ2ql8KiwmcO1V5f494fbEFXj2RgdLcujVrwTm4WCT22eGdvC0xr7J8tNTWr0hkmqdiXx6NL0Ta09IjyUKhRnGdxGRXZqN7c+fdmpjQOPbQOyQQKMno4zmVJ/6GmEz0io5LosGBEnvYEUV3oowG0e+8M/ZFc/DBB7fZfs8998j//d//mdO33nqrSc3XTPTW1lY56qij5I477shkswAAAAB0kWVF5Y33fyOfL3pEItFgYvuuU86UvXb7ngT8xR3eLhq1ZMUXEZn/dlDqtmz7Y1HrnE+c4Zeho33i9RJcdTrdFbykzCMNtZb4QrtKTd2zEomExOcL9HlwDu5lheMZ6P5tu/VbVmzbwP4TO72tnake6WEm+qp1b0kw1GBO+3yFEom0yqatC6Vq4DRxlT74OG5o2igff3GfOT1iyF4yahjJk10KtsbraMNF+Dp0pIyXc+lKgfg//vGP5gAAAAAgdyxb9bI8++rlbbZNm3iKHLjXFSkX1YtELFn8cVgWfRSS2s2xvwc0U3nCLn6ZuqcukJY7tS2RXv6W2RIuflZqG1bJgMqdki5hsgS9ZJdxSQom6t4xyuPxdykTfe2G92Sn0Yd1+6Hf+SgWqwj4S0xWsGpprWlzHV0b4qPP75NdJp8uwwbPFGfK/Dh97Z0bEqePnn1rxh/PFZnoeuQniO4aiWFGFN2J+mRhUQAAAADO0Rqsl2fmfF/WVX+Q2DZ8yJ5yzMG/S5l5rgk0yz4Ly/tzghJsiW0LFIpM2T0gk3YPSFEJgVS30nr2n74dkkBkmuhbv6H6o7ZB9PhbT8gAaQ2ixzPRvZ7OA4wFBeXmeMXa1+UA+XG3HjYUbpbqzZ+a07P3vUpWr5srXyz5j8lGt1Vv/kweeza2p/3SlS/IhDFHyeCBu8jg/oeKI2VojxF9nVasec2c3m+PyynjsgPbFua2xOP38/kJ5ACC6AAAAACMpuZN8vnix+Xdj7eVVxw5bF/ZZ8ZFUjVgasrbrV8Zkfdeak1knvsLRKbvHZCJuwaod54HdpoeC6JLeKD4WvaXxubqjq9IORf0kLWlNnYiKSPXLufi1V1dOjFtwkmybNVLUt+wxgTFU00EdmTJimcTp8eNPDgRUNf7sX3w6T1tbrN4xbPmEPD/Sfba9TsyZcKXpSBQJjkvg6WadZL16VcuNaeHVe1uyoFhB6ykIx97cLmGvRcfX4eORBAdAAAAgNTUrZR//feENnWEZ+9zlUwad2zK0i31W6Py3pygrF0azxIVken7BGTnfQLiDxA8zxcV/b1mT4OWJktKam6SBR/9TvbcJdutgptY6zfHToRCHZRz6TyIPmzIHuL3F0s43GxKs+y/xw+6/Li1davMcUXZSPH5CsTvK0qUhrEnHpeuetGcPnjfqyUSCcpr794Ya2q4Qd6cd4s5DK2aIVPGnyBlJUOkpHigBEONUl46XAoKyky7tNZ6QaA05Z5Bfl+hefyauuXyyRcPyOBBO0v/yvHmtk0tm6W2fqW0ttaauu+bty6UuoY15jYNTRukqKBSwtFWKfCXytSJJ8nM6eckZTn3TRR97rzfSlN8cu3AvX+a9vt3M1NCyO+e0J1OqGg5JO3PoVCT+P1Fsnr929LcssX0bV0suKS4yoyZosJ+UhAokTXr3zH19Kfs9GUThNZxprfXPl+95XMZPHC6GUMTxh5tXrEBleOlJVhrbr9o2dNSU7dMhgzaVUYO20dWr3tbWoN1UlY6zCQH6FjQtV/0/nSi1+P1SUtrrWypWWQWLy4tGSw+b4FZF0H7sJa5KyyolHGjDjGlory+gCm9o2snhEINErXC0q9inAyt2m1Hr0QfveJIJ/eMRAAAALiC/jHT8R/4O7pd7A8S/UNH/0ArKR4k4bAWl7DM/ekfTQF/qQRD9SZYUFY61AQZNOjQ0Lje/AGm6hvWmgzHIYN2MYGI0pIqiUYj5o82/SNPgzH6h5IJehSUmT/SWlvrpLhogHkMPWgwpLhooFmIc/PWBVIQKJfiov7muenp5tYt5v50mwaAGps2aNjanNfbB4MN5r71DzddoFEfS7cFAsWmLq8Gj/Q56W2bWzab56qLOdoL6IXCTRKN6vlA7I9Db8D8QakBl8KCCtNmDa5oQKiosNIEYZID6PrH5ZEH/loGp1g4Lxyy5KM3grLgg7BY0di2cdP8stv+ASmtIGMuHx15epE88dd4du7Gb0tDbXRb/ftE5h1BA/RQUYE58gzol9ikn8tdyUTXz3ldxHLFmlflky/ul/1mXp5yYrC9JSufN8c7Tz4t9pjx7Hd7keXPlzyeuK4G8PSxdN2IjZs/kw8+uV+Wr33aXLa++kNz6PQpFvaT/pU7mUC7z1ckjc0bZdXaN2LPwV9stmsQXX266CHpqqaWTeY4GKyXdz+6w3zu7zb1rD6LoW/YNF8+/uKfiT2bBvQbn94HcClPNP5G5EBNdP19pb9NNJitvz021yw0E0Fr178rBQUVEo60mBJeOs769xtvgsw63rT0kf4GUgP6TTC/r/T3yY5osHppfOwl++Cztnt92Kq3fGaO7UVr+8Jr7/4y5WX62+0bJz8vhfFSUm2Qie5oBNEBAACyIBxplcXLn5bV694xC5JVlI+U5uYtJpjZ0rpVdp50mkwcd4xUDZieCBDobTQ4rIHcWPbZFvNHdV39avMHfVPzZvH7C6W5ZasJtA7sP1E8EgtiaQBVb1daXJUINOsfPfpHkQZ7NUCrwWX9A0kDsnpfmummgV69ztbapSZwq4Fn3S1db29nAWoWUWNTtWytXWLOD6icIFtqF5vTpcXDpLF5XeJ5a2aRBq31/vQ56HW1HXqfGrjeXLPIXK+kaJAUFlaK36fZP03m+VWUjTCXaeZRY/Mm0wbNsNO6qpqFl2vs12nHPN3+a8rr8Zs/ZDVIHgo3ttmuWVDds+3xNbhywlF/k0H9J6W89pqlYXnz6dZE3fNBw72y16EFMmAIC5/ls/L+Xvny+S3y+N3V4o2Mkvlvt8i+R5ZkvEwE8qsmumdQv+3LuexgYVG165SvmaCeeuXt6+Xgfa/sUuCwrmG1OV1eOswcDx88Uz749K9ixQP4C5b+1xyPGr6/CaCbNno8JsN1xtSLZPasH8qSFc/I0lUvycZN8833XSr6/b5u47wOL9PvSzuA3p4GLHUCVYPT+h0+duRss02D8stXzzG/J/S76N2P7zTXf3/+XR0H0TO0sOh/Xzg/cfqY2bdl5DHcyBNNykQ3awFk5wNUA9//m3Ox+d3WGS11ZI+Hjmypif0u7Mpvpj12Ps9khOtv09Zg7bZxNmyW1DeuSzkWcoX+ZtWx2OlCxkwqOxJBdAAAgD6if5DrH/Gvv3uzNDStb3NZfePaNufnL/y3OWg2S2XFaLNrqb0rdF+xM3uS2QGFztgBdJUcQFftM/GSr9s+c87OnrM1NK7r8HpmJcMc1LUAuur+H1ImUG5ty4Zss73btj3+HrucnzKArqU65j7b2qZ0y96HF8iEXTWgT+kWiJSVDZBI/1+Jd9NVsuQTS/Y+3BKvV/sG/cPtolFLImExB91TJXY6dhwOaQzcEo07ayw8GrEkHIyfjlqi87GJ0+ZyPR27TcTcViRcv5dEB+0m0Y8GSPSLJvH6PFJQ8yuRkofFE98DpzPDh+yVOP3Fksdk6oQTzN5GnUn+vhs5dF9zrIFpFYxn0+rktxo1LHZ5e7rnz86TTzcHpXsGqa11y82o0AnofhVjZOXa1yUcCZrLdYJaP8s1c1wnSwcP3NlMPuseRRXlo6R/xVhTekK/Y3ZUa33WzO8nTo8Yurc8/tw3zf3qpLwd9E/IwDDVevH2xIEuTG1KZqBrkjLRPV6PWPb5Pvbh539vE0A3e8GJ1yRVVFaMkbr6VfE9+rb/jab0d6yWUdHfwP0qxpqxorfVMkWa8KHJH6XFg01CRiqHzLo25R6LukeKJpvo/ZvkDk8s2UIfd9PWL6Rf5TiTQKLJIfobtLJ8lAzsP8ncTvumLhKse5iEQs1mz8NNW74wSSejRxxgEkm0FIzu9adt18e0x43+Llfabt3rUSf1NAnlvy98W9ZseNesi9BhED3+e4kQujMRRAcAAMgw/WH//Gs/StRNTTZs8EyZMOboWGa3FZalK19M7PqqNEjaPntH/4jXjDVlZ41rEFP/qNCsci3vMWjAFHO5/kGhGXNaGqS+YV2spqu/yPzBU1zY35T00D829A8azezRPzoKCyvMbrr6B7tmsOkfHZopr7Ur9Q8mbev6jR9KUVF/88fHiCF7m6x3tXLtmzKw3wSpqV8hRQX9JRT0SVGxVzZunm+uV1TUz9yvBg70Dy7dvVcfS7PoLCts6mBuqP5YiosHmPIk+keLZubrbfSPGP0DRhd00+evdSr1DyO9rQYF9LkEAqXmj7JBAyabP370jx1tp2br62ukAQjNWtTgiN6nvr5a4kVLsejz0gXnYm2JSm396kR5Fb08VhalwOyKrBmFgwdNj19Xy8V4pKWlxlxPL9fXNxRqNM9HM+U1m0qfr+6qX1O/Mr5HgCURLcsiHnM73XVfAy7afp1k0de2tGSI+eNV63Laf6hpVpiWeAmFW8Qbz0bXP8qKC/tJXcNaWbTsKbPLvNYI1b0VtI0lRQPMaS0zo9n7W2qXmGN9PXW77jY9ath+5g/djqxaFJa3nm2VYGvs/OhJPtnnyEIpKCQ4im10HEQLYwsvqnde+0T2nb1rUnCOsEEm6GeQxmY18GwHqoMtVlIwOx7YjgewzXXi22LB6tjtNEinp81tNICdFBC3g9u6XYPiej+x6+nt+iKpcqCIxq50brJBH8wSn+wuRaHJEgl5u9Q3Tz/+UfnXf08y5x979mxTduWgTupza21xm36OJx/r57sG0ew9gcaMOKhLz8IO/GnpC6XfCUrb0l3dXaxUa0Lrd4x+p73/yV2yz4wL27cudpSmN1Mzht/+8PfmdNWAaTJmxIFpud+8W1jUTERmz5IVz5lj/Y1wxIE3pex3+rtHf+vUNawyAelhg3fP2KRJcsk/e29NHeP628j+bWxPHNl0LYLkoLbersBbKmKvRRBfb1jbbdPfkDpuOqK/D23JixUPGjDVBNGXrnyh8ydBJrojEUQHAADIoPXVH8kTL5xv6lMn//ifPvGrsuuUM01wOtkeO3/LHGsAWf+CWr3+HalvWC1FGuQN1pvrjxy6T7wmtgaVcyuIqbuR26LRqGzcuFEGDx4sXq+3R/eRLu1rsGomUnvJtSv1PepXMXq77crnq5TRI/bf7vb2H5Z2kKX9H1v2H1ypMr3tcgHtT2swPPHYBQEpHDC5w9vHHmeADBm0c+K8/YdkcgZl+/ssLxve5jbJNFD2/pygLPwwluFeUCiy79GFMmoCf0agY5MnHixL168Wb2SkLPtkgOxrhnN+1YC1M6o12KxBaJOJHbYkpNnX4bZBbBOgjmddaxKluX48aK3Xjd12W1DbbIsHxk0gW68Xyq14jM9vHzSoJeIPxE5rrEvPaxZ5YptX2mzX0/a2xPV8ItHn3xDPxmopOGxvCUwcKcHWsLz2RFg8VomsWhiRym2J5inppOz+e/5I3njvV+b8Z4seNutRaIZ0R3RSu32AXCddlU6W1jesSWzXhUdznf5e0IxfDaKv3/hBm8t00rmpdbPEQpDpob99bMcf/uc03nN+8LTG9jSzsvwzz15Md9rEkzqduNHEhOTfOvlq+qRT5aPP/25OV2/+TKpSrC0DZ+LXLwAAQIYyA+fNv1ve/fiOxDYNep998gtmIccdsbNpxow4oMPL7QUkgUzR2ufvvhiUxrpYdG78zn7Z67ACE/gCUtl7twukdt0DsnXBaRJt7W/60ZAcy0Q32dTBWMA62BqV2k1eibZEJBKOSqh1W4Z1ohxJMBa4toPiycFsvSyRva3bgrGgeLbphJe/wBMLZptAduy0BqZjAevYtkQA2yviiV8Wu41H9GtGg9j+eAA8dt1YYDtxP3qfnuTAeCxYm26tz68Sq2WdBIbuLr7RPgmGWqSl/FYpqr9UPnnDJzt3IYiudp50qixe/qxs2PSROb9izWvyp3/OlHNPe6NNNqmyJ7+TFy61J0l1zyRdw8TOJs+1Ce1UDtz7J/Kf578l66o/MHs22Xs4/fWhg8zk/JdKD5fB1vaTzN01f+GDiUmGA/b8sdkLCt3U0NJmPYBsl6brX8mCsF1RUTbcLASs6xjM/eA2+fLhf2l7BYd8VqBj/PUFAACQAVrP3A6gl5UOkyMOuGmH9VeBXDHnsRZZE699HigQ2WVWgUzZg9rn2DENRO613wHyXLwq1ZzHWuXLM3yxDFerd9ndodZYmZJga/y0OY5leIeC8ePWWBkTDXjb2zTQHUrKCo+XpU6iAb626wukiwaV7axrHUvJGdnbAtvxoHV8m1+3xQPZ5rYmG9sOiMeD2Hag26eBXT3v2RYM14PbxqodSIzv1aSZ06HiF0wQXbP2Vy4Ky+iJOw5v6F5GJx51jykLdve/t+1RpKf1O/qIA39tAr560AWs1YSxxySulxxo1zJlqiDQdm+lXDZ44LbfIa++e6Mcsu81psSNvVD4mop1Mriud0F0LTH3+rs3JSb8d558Wi9bnedKUtcKzzSdLNKSez0pH5TPpk88RT76/B+ydsN75rOqw7I2uTGnjG4iiA4AAJBmm7cuSuwuPm7UIXLEATeTOQ7HmPtMayKArrXP9z68UAqLXRaQQ0YNqBwnjQO/JqWb/2bOPzt/JznCWyjFSVGDmk1RqV4TkaISj5RWeGTLxqhs2RA1QXLN6g6FLGlt2hYMD7akv2RJLGitgb6oFBb7TAA7UBgPbpuM7Fjg2gS2NSPbHOLB7PhlgcT52DY7YG5viy2sil4Lx4Po/lhWuK4ZId46sTyN4rFKzZoN/atiAfbyfjsuH6bB8K8c8Vf5z/PnJLZt2PSJ3PfY0YmAoZ2BW1I0qMNazJu3xtYr0TVHnEL3chtWtbvJRF+87GkZPnj3Ngud1xbVi9T2bqD9b85FidNfP/HZXt1XXrPfhsC2PSH6WiyAbjmun2ebLtKuQXSli5u2qauemOAkiu5E/DUHAACQ5jIuT728bbGuw/e/kQA6cp5m6K5aFJHlX4Rl7bJYsGrIKK8ceHz2MuDgXBqoiwaWSFP/y6Rk62+lNeyXJ4d9S06IvCyaj7dgXkjmvRLsUdkTDVLrgrb+glhAW4PwGvgOFHhMRnbAXBYLZptt8evp9TXgHcvsjl1XS5D0dO0G9LF4Z/Ho7IZmO8dLqYQH3CqBzT83eyY8cXez2TZgiFeOPKPIZOp3ZtjgGfKdM+fJqnVvyVMvXdAmqGUH0FVF2Yg2t9OFm1eve0uWr37ZnC90UCa6OvKgW+TeRw4zi5nPeeu6NpctH7BSZEPP73vl2jdk05YvzOk9d/1O3tfH7g1P/PMxmzXRdaF6pQuY6yKb6Brdk2X4kD1NJrqOsdOOe1g2bv5MPvz0HhlbVCijpYIYukPxFx0AAEAaPf/6T6SpudqcPu7QOzvehRPIsYmf//2jWeq3bvuLTjODD/sqAXT03Iih+8ia9W/L+L3ny5J3YgvXPrn1QKl6uEXWrYhN1JSUeaSgSKS5wZKKgV6pGu6T4rJ49rdfA+R2ANwjRcVi9ojQwDfyj2VnoseD6DW1y8xxi/9Z2WnC1bJ6SSSxp4Lu0fD4Xc0yZXe/DBzqkyGjvZ2Wtxk1bF/5zpnvm8/CmrplsrV2mTz32g/NZSOH7iulJVVtrl8QX1zUFtaseAfRwPZuU7+eyJRVM6efK/M+vducXli5UJLyZrv3XfLytiz0PXY+Ly3tzVeeqNVpEL2haYM89+oPpal5k1lUfObO58jIYbO2q+3fG2vWv2uOCaB3ny7grkH0rbVLZdOWBfLoM2eZ7UsHiJy49ksyiCi6IxFEBwAASJP5C/4tS1c+b04ftPdPZeSwfbLdJGCHFn4YTgTQJ+7mlyGjfDJqos99NZXRpwoLYtm5NdF/yYzRF8uHKwdL2PInAuiTd/fLzIMLKHeC7tVE98X2FghHYzXsh1TtIgcdVSTRiCXiEfns3ZB89HpIWhot+fB1XRg0JAOHek1fGzyi87IY+pmni4TqQTPUtR50cvkWm9Uu+KVBeKfRLPHkIPq0iScngujzB87vURD97Q9/nzitpXL4Dukle1Yoxcv47/+eIqFwrF65luR59tUfmNPHHvpHGTVsVlqasKUmVrJoxNAurtyLhL13u1A+/Oxec/rhp89oc9ljO/9PzgyeLM7ahwWK/dUAAADSQBflev29m81pj8cn0yae0qP70Uyu1hZLNq2LyPqVEVm7PCwb10TM+ebGqLkcSCctraG07IXWPx8z2U9gE72mAUjl9xXL5OE1csSGf8rkomWy874BOeasItnz0EL6GbouEm1TEz0UX+ywvGykOdY9FLQ/7bxPgZz0nRKZObtAxkzWOvcim9dH5fl/tciLDzdL9dp4ML4LOgqgqxFD2gYUS4oHitNotvLu075pTmvJucLCSjl816vM+Yin66+RTbOhP/wstgZC1YCpplQOesdj/9zr4GOyuWVrIoBeER8DNi1NtG7jB2lpw6LlT5vj8aOPTMv95ROv12dKP6XS0LqxT9uD9CATHQAAIA3mzY9lcKnTj3+0W7fVRfOWfhqWlQvDZnG9cCzBLuVCeFpqo6zSK+X9PFJS7pGSMq8pe6C1gbXWr5ZI0BIIQFcnbdSso9ldG+kzevgBsmzVS7Ha0gUiFeEa2bVksRTsHyvtAvQmE90O7nVUuqK41CNT9wzopdLcaMnHbwRlyfywrF8RlfUrWmSn6f5efd75/W1vGwi0Le/iFPvsfpEMrdrNTPzr61hRPMxst+xi3N3w4FOnJU4ff/hf0trOfBVdu0mkrONyLp8tfiRx+mtfeUJC4WZZuvIFeXnuNWZfCc1KP+bg22TIoF16/PhaLsZeG6Bq4LQe308+O3TWdfL3R7dNQJx5wlPyn0dOlwZfvVhW9yerkH0E0QEAANJg09YF5risZKhUlo/q0m00s3z+WyFZ9nnYLIqWTOsCa2awrnMXDsXWVNO6wdGImNIb9Vsjsk46D7br7XVhPU2mKyzymNsWFIsEW2K1hXVPaw3ga8KoZuvprteRiCWRcOxxtRynXqaHSNiScFikoMiTSIpK5MTHs+PN/0mJ8ro5HC4Rv7+lzQ3a5NInb2uXZJ+cdG+f1uegiwhqveQ2d9FJGzp8rB08vtXN+0g+r6+7voax17SD+2gv+bG6cJ1Uz2G7m3TymPZl+r5qf9B2DhvbeakDoDsKCsrMsR1Ej2FPGnSf2QOrJZhYWFTP24tXlhR1ngWuAfV9jiyUKXsE5KPXg7JqccRMWut330Ff7tm6Dz5f29sF2tVId5IxIw9qkzmrookU6K5ZtOxpaWndak7P3ucqs6gies9bGPvgtOwJpCSfLnzIHA8bvIc51kmQyTsdL2NHzJbHn/umbK1bJo89e7YcPftWGTtydo8ef8mK5xKnu/q7Fm2VFA8yr//y1a+I31ckpcVV4rXi40x6sLI2so4gOgAAQBrUN8ZC2vvufkmXrq91gV96OB5c1l3S+3lk/C5+GTnebzLNO8ok15qvTQ2WNNZpED0q9bWWCaw3NUSlud6SYGssKK4BXA02tzbrIfbHcH2agldN9d29H1/aA2ctTbkdjGuJ7WHtKJN297P3AtLKDqSZIPp2M29A11mba7adKS2WcGTbd+eU8V/p0n1UDvTKQV8pkuce0JIuUVm1KCLvvNBqSlh1l8+rWe7buCVo7I0/r+4E94KhRnnxzZ+Z0yXFVTJ1wgkZa1/esT8vS7af7InEF7Od3q50YGFhhRx3+J/kH48eZc4/88qlcs6pr/Woj9bWrTTHleVjetB42Pbf80emrNlOow8zpZM88aralmbHdEEkEpKnXr7A1Kc/6ai/S0V52/I96FsE0QEAAHopHGmVzfFM9AH9xu/w+tVrIvLyo9uCAAccVyijJ+14IUet+VpWqQcxiz92RDP0tBxMa2vsWIPsmlymwXTNSA+1WuLze2IZ6JZmS3vE59OM8dhfa1pT1u+PZaFrNrrexuvxmPvw+WO338aTCI4lWu5pWxO5pqZG+vfvF6st2/66SWfa394ctXs59OUx2fOamR9vb/INOmpDm5c0+TopHqt39+ExKd46maGvlU5mdCr5Mbp4ve3OduOyNhclXaZ7K5T3J4CO9CoIlG1bmI7+hd4Ib8vE9RQWSEt80lqDvmWlsRIkXXXE6UXy5N+apW6LJYs+Ckt9TVQOOK7I7K3VVVrzu6O+7nRa1kVZ3chE16xn27GHbFtYFJmrid7SWiOtwbrt9iSwabbzKcfcLw8//TVz/qmXLpQTj7qn24+/eOVz3ZqoQsfKS4fJ4Qf8MnHeawfRu1jO5fnXfyRrN7xnTkfja40gewiiAxkSDrfIF0ufkNLiwTJmxPZfbojRXTFr6lZIWelQU7OtowV8Vq97W+Z+cKtZsEYN6DdR9tzlfBlaNSNlwCkSDcnnix6V6i2fycD+k2TnSaeZmV9d+E8v08BO+xn5hcv+Jw2N68TvLxKft1Am7XRsmzqLGpjaUrPILOSyZOVzsnbDPFMfrrFpo/x/9u4Dvo3yfOD4o+E9s5y9E5IQEkLCCnsEwiYFWmaZBVo2FCjQQgtlQ4E/lDJayigbWkYZYZVNEkiAkBAICWQvO8t7Svf/PK98siRbtuycLMn+ffkcmpZOp+cup+eee16/v87Mz/jtfiH5uQPD58VXL1vLlpledQV5QyQrs0fwMX2t7398ReZ8fa+Zxx4FIyU3u8i8llYSBP6+TjZtXWrmraGhWoYM3FvS0/LMP7ybS3+U4o3zpVePsdK7x3ZSW1cu9Q1V5rNq7OlpmWUVa6SyaoNpMeHzN0hOVm9JS8sWv98n64q/NPPrcntkS+kys4yLem0vhfnDg8u2umaz+Y68ngzp1WNM8FRPm76n7sjppMtIdxQyMwpMZUpGel7YOlFVs0lKNi2SFWs/kRGDD5BB/XYTj0dPVXRJWcVqKd64UDLS880p4KHxoH9TU1cmPQpGmO8kzZvdbD5C32fl2k/NDqY+v2/viWHP1e9Bq+Iy0wvNctLTT0vLVzXG1kiprS2T9PQ883k1TjQe9G/0dTVmijd9K1tKf5RRww4J+9Gk3+UPy16XTVt+kJzsIhnQd2fp13uiWdaRSjZ/JytWfyTllWtl9PDDzHLQZVdZXWw+f2jc6Xv7ffXBeFBlFWtl4eJn5KeV75kYHDnkIBkxZFrUZWK/TmVViSxZ9rpUVK2XUcMOlQF9p4RVU+nnrahcL26PR3Kz+wa+39oy+XHlOyauMjN6mBjZWrZcdhp/pvmuNQ51GWmc6o8vn7/OrGf63YWuY/o3eiqjfk5dNn177xBcbl99+6iJw5FDDxKvN8vEUkZaXtiyW7thnvzvs2ulf9Fks05vP/pY8/q6ftXUbTXfhb3cNmxcIKvWfmaWr8eTITuMOUF6hMR0aOxu1O9izSem76N+/wOKpkjfPhPDlot+57qsNaaKeu9g1uOeBSPD1uW16wM7t0ofb83mYp+8/Wwgga6J1iPOyJLcfOfGetfPqe1O0jICn7ewtySM3+8SV4ZPioo84taMPIBuIyO9IHi9zqqy0wYJnCOkLLtiMy8nuG+iMtML2jz4HEmff8TpWfLxq7WmtYv2SX/9sWo5+MRMM9ZILHQ/sWtWogfSQ/4Ye6LrvpE5SCYik3f4ldk/g4O0msFsNcO3m/bvFv2t19KYAKp3z7Hmd+m3PzwvGzbON/vFgwfs0a63t/eFexQM7+AHQEtcjU3u/TEk0fU3n/5+UjpIaV5Ov7jPH1pHEj2FaWJs0ZJ/S6/C0TJ6+KFxeX1NgmiSJN5Ky1fKoiX/kVFDD47roBWaNPxi/t9k/cb5MmXC2SZxFQ+aoPvn802J8yMPfFg8kvqn3WjyS5PSy1a9bxJsmuTUhF9WZk+Zu+Bhkxgs2fStDB+8v+w47peydPlbph+bJt0yMwpN0ikvt7/k5w42SWVNWhdvWhh8fa0m0USaJgP1+1mz/nN54/0Lm83HmvVzzKR69xhrdvh058Lvr5eamq0m8aYJu1CfzftL4D1cXvFbTaWBmnzctHVJs+erj7+42STmdIdQY0d3QCKVlq8IXtfE3fzv/tWYuN1R0ryZZpmsL/m62Y9KPQ1Ll1WohT88F3ZbE3G67DR5GWlQv91lzYbPTdIzHnKy+0rvHmOkqrrEfAabx50u/Yommf6TxZsWmcR3a0fRNXGvO16awI1cDj/89F9zqUlTPTgQSZOfmmDXxzQpG2lw/6mmJ6XOY01dqdlW1dRskdKK1c1eT7eTDb46qa0rNYlQJ3z0+c3m8+lBBp1Pe+Ad21ff/jPw3j3GmO+yIG+QDOy3q6xY87EsXPxs8Ltb3LgcsjJ7SXXNJnM9O7O3OZAQGpf6GoX5Q00ifOOWQB9QpQlxHbhNPr3aLDM9vVNjTH8k6nIvzB9mlp++Vuh3Zb+vHpDQ19Xnhr6ffjbt4xe6joZa/NOr5lLXP32uxkIk3bnX+I1cNmq74UeY737RkkBPR/XJ3NvCnjOw7y4m3r5b+rL5ntWS5W+YafZX90hezgCTKLfpZ9EDcltKfwp7nUVLXjSXebkDzTZCl43+ANYDU6F+WvlO8Lpu33Sbp68Vug5+/+PLweu67dMBg/Qggx4Ms7/vwIGhlpVt8cub/2qqQD/qzCzJziO5DKDrCe2hW9GwRcyhYHLo2IZkorgDySc9eK5CCwzaQ/cDtLXLyiUN8unrtWbw0Vf+US3TT8qU3v3bHhtCiwfCb7ecyEw1TT3R2/59ob+N3v74CnNdez3vMvE3cZ+/7lqJXlITvl9bWhb4/VmQ33rRxp5TLjdFO/pb4fX3L5AD9rhRtht+WEzvrUUq+htHbcvgpGjOHlkolt/xc795IHj9sP3ua7VgCp2DJHoK02TH14seM9c1Ubnrjuc59tqfzrtTFnz/tLl+wpEvmQRLvGj13zOvBnqnzf/uCfnZ9CeCFYpO03/ol6/+wFx/7b3fyNknzG412dFRc77+a9jtjz6/Ufbf9UFJJetL5puEsCbGtMJUk3IbNn4T068fTc7ZCbpQK9Z81OrfaRJc4/rLhf8wUyhNju264/lSsuV7Wbv+C7MzoEKTiUHV4QP91Dc0NacNTaArTX5FGtx/D1m17jNzXZN/Wq0dSZeHVlkHqmJHm2paPailSVpNukUm8ULpc0o2lTa7v0/P7U0lsl1xrweXQmkCsLxijbm+ev3ssMe0AthOkOqyainxHnkAoTlX8PvVynWdImmVsR7caIkmFO2dLZt+T/Z3ZdOKYN1m2SIT3vqdNfiqTTLY7vfXklXrZoXdLmusyjCfxOUxlRl28lYPlMSD/dl0udj0h4QeeAmsL2JafNhtPrTiOho7ga6qajaaKZTGQ2RMBA7wNATjzV5eGmM2+72j0VgJPVAS+tnsz6cHwYp67WDiRyvCdV216fu3lEBXGs/R/LDsNWnLmg1fmCma0AS6/VnsZaEHgjLT88O+e3v9US0l9vWgmX2/HhRri8a7/igJNXTg3tHnd6tf/vvPpnjXH+sk0AF0VZqo1F66WnCwtvq7QBKdLDq2pRK98Yym6urNwUr0bTFktFfyT3HL648H/m1+6+ka2eeoDBk82tuunujtrYZPVq7GSnRt56KFBK19rg9mXx+8PuPgR7vMMkgmdpK13h/+e+i7H/9jLrUwrTVa6PKLI54PFvf977M/mPEEth91TJvvXbxpQfA19HcAnBNrOxctvNKznJUOEEsCPTmQRE9h9qn2aunymY4l0dcVfx1MoNuVgUcc8DeJl5feOj3stlb5xSOJvmnLkmAC3bau+CsZ1N/ZanRtD6CnTYXSxJcZVT4J1ddXmWSkJp40eaTVqHZFeWs0SamtDGpqS8MSakorPOvqyk2iWY9c52T3kfqGGnPf2uJ5pk1DdnYfU82srTYmbX+aSThqcnbugofMGRD2ToNW1k4ce5LsuuMFYTtnmrRe+MOzJvGrSdjq2q2mPcjmrT+aRKL+nZ7ypK1J9CDAlwsfMRXIw4ccYN5zffFXsvin12Tluk+lIHeQ5OcOajwNcbR5fW35ogmyj+bcJCvXfmISu4UFw+SgPW8xvRdb+kdst0kXmKP9m0t/kp9WvmuWi56mpT8g95hymanq1cS0nnWhpz7q41qRvs+uvw9rY6OfXduR6GvpTose6NHT8bRFSln5avlq0aOS5s2VhnqP7DLpVMnKLDSnter3VtR7fGMisNL8o6sJdV1qgVYfXtO3WVumbNi0wDxPl4V+j3rdbr2i36mebaAHNNwujxkERauZLfHLkmUzZdHSf5vnalud0cOmm+WhldP6/ZhezL4a0/qkunaLzF/0hIktfe8hA/aUHbc/NfjDo6p6k/msun5oC5QhA/cMtsHQONy4ebH54a3LUNui6EEE/a7tViiBVitZ5nvRH1HfLX3JtIvRgxH77hZYprpc1hYH2mxoCxxdhhozuv38rrGiWOOkMG+IeR9tL6RnEmjyWE/N1e99c+lSUzEQuH+DOaNCP4+eFllVs1kqqtZJbV2FSdprLNvLUQeZXLX2UymvWCdby5ebliH6nfcsGGViRdvyaDJh9brZJmb0sbEjZ5iEtK6Xuvw1DrMyCmVAv11l0+bvTVJa1yU9eKP9OENb7ugy0+9OW81oZbp+9/pa+vn0e9GE+/BB+5v1Q5ebfsYtW5sO9ujfl5dXydjR+8t3S/8t3y39j6Sl5cjoYYfIxLGnhMW8vp62wdF5qa3daj6rJrA1FrTqX9uxaAsePUNE26VoeyBtN6Snlur3plXbK9d+Jm6X27yHDsSpcahVL5qo151KrT7XAwAaSz0KR5ozh+wWTnqfnlGkA/Po+qEV6fo+W8qWS17uANOSxW61ou+nMfPjirdNDOjj+l1lZ/Uyu7FaKaln1NjLUtvZvPPJVaZNk37PGuf77Hq1WX9D6amVesDZTszrQQY9O2LyhF+1uM2srbHk1UeaEujTfhFbtRsApDL9t0L5zEF8Nzl0bFMS3dWYRF+8LFCso/uA26qwt9sc1NYEuvro1VoZvaNPdjkwvdslhnW/36b7T3aP9Ej6O8Yuhhg97DDp3XNMp81jd9Kzvo+5THNnhu2D24U6eoZ3W/Q33qH7/Z+8+UFg0PuP5two9fWV5qzx1mzYGDgT1W7RCOcr0VsbwFe/5zc/uCR4tj7tgZOHy0rWzF6MysrKpKCgQEpLSyU/v2Onc4Xy+/1SXFwsRUXaSzj5Nxaa0HnypUPNhu2cEz93ZAP34FOTm913zolfxOXIlyacH31h32D1pibfNIl24lFNp8s75a2PLg+0PBAx/Xh1cAZt5XHQ3uHtA7aV9oazT207ecbr8tTLh5vr++92v4wesVvC46q8Yq18seAhk0TWtieavIxGE0hTJ19mklSa1NOkoiaK7ESz0k2IJsf1tew+ekqTefr80PtipYleTWRpsiuy0gOpua1C4LRX/UHi8SRnTBNTHaMHarxpWa1uq/w+S565p+nskP2OyZCBw7tHHQNxBacRU6nlo89vMW27dupxtOz4bpa4tx8h6b86TpIJMZX8/D+tlrq/Pi2uPj0k4+qz5bn/HmvOatxl4nkyJcrB6/aqq7Xkg//USMnaQGKrVz+3HHBcpqQ3ji/S2m/mX5/8ZZeIq9oNa+XRd48w1391wiwzPlBLnvj3wcEzJuOVJ4DIxnv/Ki/2+qd4XOly9kmzTUx9NPse+X7Zk+bxX/7sLVOsFgstlnnkub2CZwNp+x1tnxrNa++dZ8581oMkB+55o0OfCOqlx46UDWlr5MChl8jovU5t8Tlzvr4veAbzMYf8S4p6jY/LvKTKdqrM4bzvtugev+C6sKyMpko7rbLVdgrbIvSU/n13u04+nHNDsGVGPFbc0Ir3I6c9aKrStSpVK4edbLOiiV47ga47W3Yfa3tQDictWPxssNe29mi2Dw7U1Ia3uehsq9fPMQNtLlv9fov9pbUSW3uZaxxpdalWkE8Yc2KbO0VaodFSP8JAxW/H6A5bPFsIAYkQWJf4kdHVxNKP9bM3m07D3e2g9G6TQAcAHR9GNVja9qxr9I1G57Mi2rloIZYaPGCqY++hyfKDTsiULz+ok++/bJBN6/3y5pPVsveRGdKzqHvsv7l0tPNGlt/X4m7rvAV/DybQtcc2CfT48fgD8e636oM5jRVrAq1I9czbWBPoSs/C1Fa2T758qDmD9YtvHpB+RTvJwL47t/h8u1VlW33X0X56Jm1rPdH1zOj5i/5lrut4WvFKoKNj+BWX4kITzTpo3/DBB2zT6y3+MTBQnBo3aobM+fpek1TVtifxWHkXLf1PsDJc/yGwVVaXmAEmnRLaN3vC2BNk5ZpPTauLFvtpb6N1xfPMpbZ+UDoonrZrqK0LnM7amXQ5ap9u0+aicVA++7QubYugrRfGjz5W8vMGRx3ZGwDQcRtW+WTF4kDPw0GjPDJqYnKeiQAA8aDFJMpnkui0REcHRSbR68vDCsqcosVBU/bPkB593fLFu3VSsdUyg4HvcViGDB8XnjrpXzTF/O7bbnjgrOOu1s5F2xxG+mHZGyb5qrYfdWzMg1SidXrGYk2VJVXlllRVNF6W+6Xct5dkbR4jLn8fefGBSqmrscTrf1FyPKtk6ID250r0bNhf/mymPPzMrsG+9scd+nSwFWXk+GhqYN9dHPiECOW2GltI1oWPf6W02PHFN08y17XLxPR9/tLp84fWkUTvAjQRWlEZ6Mm7rZY19gwfO/Jn5lJ7wGoSXV/faaZ3cuOAgjttf7pp+6EDwWnP6LUbvnA0if7tDy8Erwf6QE8Ia7Hg1BH00EEVtxsROBVOe0WH9oTsjHYtusH9YPYNzQafVHvtfKWMG30sbVIAIM6qKvzy7vOBHqtqr8NbPi0aALoqb1glOkl0dJC/MXDcLtP20R5IXcdhiocR26dJnwEe82+4JjQ/e6NWVv3QIHsdmSFudyABduAefzaJxm0tYksmoW04I89c1t/MOjClbe9dr+nUeUtFmu+orxWpLLekssxvEuTVdpK8wi/VlZbUVFpS2zRkToSR4q0baa7Vmq8jEHtu32BZ/KVIj971MnJCWru/4yOnPSz/ffccM67Px5/fLNP2uiXsOTr2mD3opeZn4Kxad+C3QXldcbPH3vnkd8HrMw5+zLTHRXIhid4F6IB7SyvXmUHTxo48qsOvowO1aQJbjRgS2BnQQeh04BDtb+007WOnA1nqPwb9+04x99U1Hghw4oBAqNq6wPzr4IoqJ7so5LGy4AB020oH8bPZBwECg9fp+2yJ+z/S8xY8bAbmDKUHQvR7HDn0YBnQuJwBAPFVX2fJSw81/SrSU8Q93u41QBkA2JXowSQ6WXR0hN8vKwtWy5qCb2VK9cGNd7rimmDKK3TLUWdmyayZteaMslVLffLsPVVy1FlZklvgltycfjIqp590JVqI5bJcYrksM9B7qPc+bUqan3DkS91u0NVIZlywGjHJ8cqypgryyvLG2+WaHLfE17yLaot0cWbnuSQrx2UudfJ8/oXM7TFTLM9GOWzajfLWJ7+W+oZSyS0JjB/35Ud17U6iK23hsvcuV8vHX9wiS1e8JbtPvlRyQ/IjW0p/Cl7X9rRwVqGvt2zyFovHFf7drdkwV5atet9cH9Rvd+nbe4cEzSFaQxK9C3A3tnSxq7o7amtZoE+4Gtw/0F8uP29Qswprp6xZ/3nwtDF70JLRww8zgw+tL/lKdhx3imPvVbI50LZlzPAjgkdgtSJdk/hOJtF/XBHoUda/qGmgmazMQBI9nj3RdaAQHcBUzxoItf3o40zleUcG9wQAdNyHLzdVoO9+cLoUDaRnKIBuXIne2F+XHDo6wr9ynfxv1Cfm+uJXAr/ntBWlJn3jSQ9+73VEpvQdUi+fv1MnliXyyj+q5ZhzsyQrN3kH4eswlwST6KYneqMtpcvkx5WB37k7jDmhW4xdpcUQgWS43yTEKxoT4xWlgSR5exLkGVkiOXnuQJI8tzFJnhtImGfmuM1leqbmKMIPTNR8vFC+TntPatJqZUv1u1Jn/WT61E85dKnMe3OUSeKvWtIgg0e3/7f+mBFHmiS6+mzeX+TgvW8LPrZq7WfmsnePsd3+YEk85PkDY8dFHqia+82DweuHH3B/p88XYkNmrQsYPmhf+eGn/8q6kq/M4AQd3Zn4dO7t5lJbndivYQ9Uqm1B9GirkxvR4o0LzeXEkGR5elq2uSwrb6ro3la1tWXBdjRaMWDLSM83SfRNW35wbEfAbofjcTf1qs/JCgz4sab4I4kH7Vf/yjtnBW9rtb0egCjMGxbTgHcAAGdpX8sNqwL9W0eM93aoSggAulIl+srKrzRtQxYdHVJvNQ3QHfpbrrOMnpgmmVku+ejVwHz856FqOfHS7GZJz5Tnconbcotf/wvpif5JY55A7TnlCukKtL94hbZYKWtKkGtVeUWpJZWlfqltqoVoVWa2VpC7JacxQZ6T75acfJe5nalJ8myXeNM6GCd19eb7UFvLVwXvHj1mB5n3ZmAGNSZ/do4m6N3tPsC526QLZc7X98lPK9+RsoqLJT93gHms3hcozkxPz+3YfKNV7sYRe30hSXTNSa0r/tJcP3CPmzh4kcRIoncBA0IGe/j7s1Nl4tiTZPIOv2r36W2lFYENc0FeU0K5T89xwesPPT3FnLrlRMJZk/1Llr/ROP9NLUaGDNhLvl70uGzausQM/Dlq6CHb3K98bePGSPUoCPQUC/XxF7eaAwehCfaO0r5iakxIW53CguHB6wu+f1p23N6ZCvu6+kpZtW6WvPPxlcH7+vbeUabteQujpANAAn35od22QGT36U0HVQGguwkt6NDEXBes3UUnmF/xVrP7xo0+plPnQat99z5S5OP/BhLpbz1dI4eekiVdjVaih/ZEL6tYK2vWzzHXd9/pkpRI7tmtVjRJXqlJ8bJAm5XyLYG2K5os117lbdHqcJMY1yR5fqCCXFv56HU9qKKPdThBHovKahmydaB8X7RUFi5+xtyVmdHLfAeHnpIpbz4ZSKT/99FqGb69VybtnS7pGbHPz6TtT5evFj0mdXXlpjXwTuNPN/dXVQcGvNSWsHCefWDEXsf0UnNStpFDD0rYvKFtJNG7AB1NecKYE2XB4mfE7683SWid+vWZJJkZBabVhw4E0qvHdlLUa3txu7zm1BE9PUdX2AZfjelFbm8s95jy2+BrayV6ft5gKWs88vnsf39mBpfQBH2fnttLj4LhUpg/3PQY1811eeU6KTKDdmqVicu0adEqgch/bL9e9ETwet9eTYN8ajI7I73A9DD/32fXmmnHcadKQd5gGTJgT8nIKAhW2+uAMpkZhW0un0VLXgyOIB6aXN5uxOEyb8HfzYCfT74cGFl8p/FnyIQxJ5lBN3WgmvbsJJRsWhS8PnTgXsHrvXtsZyrTff46mfXVXWYaPGBP6VU4SrzeLCnqOV4K8geLy+WV6pqNUllVYhLk+t3V1pWbeXG5PVJdvcl89rXFc2Vd8dfNBiodO3KG7Lf7dTHPLwAgPpVNyxYFdoqHjfOkxI9NAIiXgf12DV5fm79BhlgjEjo/SE3z694Nu62/0yaOOanT52PIdl7Zfhe/LPqiXjZv8MsX79XKLgd2rUHD3Y2DV1ZUrZdP5t4mq9fNDj6mOYdkardit1exq8e1olyT5mVb/NJQH2OblZDK8ZzGBLnpeZ/vkrR2JKSd5i8JtILNaAiPr4y0QCuQnn09sveRGbJwdr1sKfHLkvkNZvr5+dmSnhnbfOs+6rCB+5jixbLGgkr108p3g2OrwXnuxjSs5uQ0bzf/u38F2ydP3+dOWvEmOb6dLmLPna+QEUMOktf/d55Jiqv1JV+HPWdd8bw2Xyc/d5BJ3oY6ZvoT8txrx5nWJ5q41sFHKyMGnGhLz8JRsnnrj5LmzZb6Bv3rAE3sh1aoeDzpcvTBj8jzrx0XvG/+d00J91j0yB9uBi3VSz2AoDsAql/RTmHPmzTuNNm4ebGsWNPUZuWrbx81k60wf5ipUNeNWlX1JnGJS6pqNgb7jZdqH3mXyxyksJevy+Ux/dZDnXT0a/KfmadJZXWgrcyqtZ+aySk6aCgJdABILL/fktlvN5U27bx/1/phDQDtpcUg+vuirGK1LO21TIbI7omeJaS4w/f/q/TuOU7SGtuAdrad9kmX1T82SNlmS374ukEGjfJK/6GeLtXORb323m/Cxvg6ctpD4vF0bns67TuuSfLyrZaUbwn0ItfbZVssqa5ouzWU9ho3CXJTRe6WvB6BXuR2NXlcq8i3VXVgf3LMxlEyf8C3wbvtfIJ9UGfwaI8s/qpB5r0fOAvyhfur5KDjM6VokCfmrgaaRP9u6Uuy727XmvxJaA4HzvM0npP1Y9ks+fGrWcH799v9TzJ88AEJnDPEgiR6F9K/aJL86oTPAgndkq9kQ8k3snnrUlMdXl27OVhNHkoTvlZIv7O9d7262XM0qX7ase8Ee5WvWPOxbC79UUo2L5KNjQN2tkXnQ4Um0NVh+93X7Lk9C0bI2SfMkY+/uNkk3os3BXqnx0oT6KGXtpFDwk+L0R2vQ/e7R8oq1sicr+4NDpYSamvZcjO1VuEe6dB972l2n1b077frfbJ4+T9k6YqZLf6dx5NhDlK0Rn+E6BFhTdJrW50dxpxofpxwlBgAEmvdcp/8799NDTQnTE2TjKwk/nEGAJ1k9PDDZN6Ch2V5z5XiL2v63QF0xOABeyR6FuTw07LkmburzPX/vVgjv7gwW9LSu8a/+bWeppZ0aq9drpIxw4+Iy0ELLT6o2BqoHNcKcpMg3xqoMK8ub7sveVq6BBLiBZoYd0luY1V5Xo8USJK3pSGwrcwpHCDbDT/cJLrVlPFXNKsmHzs5TbxekTnvBL67d56rkdE7emXyvultLoPePccGr2vhoD3Gm3YV0IJCOM/TQhr2lJ/NlNzsooTMD9qHJHoXpC1LBvbd2UzReoRpNbU94KUebdyw8Rvp32dym0eX8/MGyYSx4adxaUsYTcY3aNsYy2famuTnDpQGX62Ulq80let5OQOkpq7U9CTXxzdu+U52mXieZGf1avF9dD722/2P5rrPVy8+f61sKV0uWRmFUlldLNlZfczgC3qqUXXtFhk6cG/zPG0zowMy6PtsLl1qBhRNT8uTvXb5XdTPpvN60N63yQG+G6Wmbqtpd7N6/RypqdliThXUz7dpy2JT0V7UawdZXzLf/GOlByu0VY3bk2aS2hs3f2f+gRsycM8W30er8A/Y40aZttfNwe9BX1vfMyujR/C0ncDydAcHd9XnaRsX+7sFACSflT80DQ6kVUET96AXOgCo7Ucfa5LoakH6HNlFOr8NB+AkHVD04BMz5e1nAlneN5+slqPOTExlvKNcLpmwflyw8nmXib+RHbb7xTa9ZEO9Jsc1Wd5YUb61qaJcbzf+zG29mrzAJfk93CZRrtfzCtxS2Nud0HYr8WY1JtHF45H9p94ge+9ytdTWVUpFecsHIkdNTJM+Az0y551aKVkTaO9SvMYnex6WKT36RB+Nolfh6OD1tRvmyco1n5jrpl0vLQnjorevf9jtM37+oWnRjNRAEr0b0o2hnUBXad4sGdRvtw6/np38tY9OD+rf9Fo9C5sP5Kl92dtDk9869e29QzCRr7RP+oghBzZ7/vjtft7OT9D0PvZyGT3sEOmM7yH0PW2RPbD0eXqQAgCQvJYuCCTRtepn7BR2rwDApvu6w/J2keXlX8hW96ZEzw5SUC9fX9nk2SA79ZohyaLPAI/59/77eQ1mwMr3/1Mj+x+TKalu4vrtpaAmT7JPP0GGDto75rYrOlinnSSvaLzU2221XfF4RfJ7NlaQFzYlyrNzA/e1Z6DMLqWhsTjDGxhfR3MtHk+mVJQXR/2Tgl5uOfiELNNuaM7bdVK60ZI3nqiWsZO9Mnm/9BaT4nrf4P5TZdW6WaaDgbbwVWNHHh2/z9bN9bT6yEFL9pXVu3hl4l6/IYGeYviVBwAAsI2DW4VWoVO5AwDhBudOMkn05Wk/JHpWkII81T6RXJFeuck1MK0eOF+3wmeSlWuX+WTVkgYZPDqFUyxa5GV5ZMSWYZIxcG+zP7N5g09W/uCTjGyX9B3kltLNlhSv9pmB1HXwzi3FfqmutNpsu2IS5IVNifLsfJcU9HRLdp4WjLHfFMkqDSSzTZ+Wdho00iu9TvXIR6/UyMZ1fvn+ywZZtdRnBiLt1a95cV7vHmNNEn32V9qWNvBd6Jn+iBeXDCzrL8P6HSGegsA2bdMGn2xc65eigW4zMOzmYr+UbQ4MkKtnc+g2prbGkn2OyjCD4SJxUngLDwAAkHj6Y1Jpxy2tmgIAhMtJ62Eufa4Gqagqpvcr2qXaG2ib4k5PrgG7Nfl7+KlZ8sw9VaYtyUev1spJl3WVg+mWbNrgl5lPttGYvFFGViBRrv3INUmul3nap7zQbR7rGsuk81jrNwau1NV36O+1DY62HFr0eb18M6vetNCZ+VSNbL9LmkzaOy3s+xg0YKp8tehR+51lYL/dpE/PcU58DLQkuOgDB59mv10rPzae0doWH8OKJFxSJNHvv/9+ueOOO2T9+vWy4447yn333Se77rpromcLAACgTaWbGiuwLH4kAkBLBuVODF7XMY0mjqUvOmJj+XxSnhmoyvX06inJxk6kv/ZYtbmtyTDtT52q/OKSH3MmyLrnA721bZk5LvE3WKb1Su/+mih3m1YseT3d0rPI3WUGVk0ankC1sat34TbF5vjd0mXYOK98+HKtbCnxy6Iv6mXF4gbZ64gM6d0/UJWuY+mNHnaYLFn+hhTkDZXp+9wZHJ8N8eEXtyxZnSfLv6+WTesD61lmtrZGCjxe0Fv7/gfaGXm8elBKWxwFJnTzJPpzzz0nl112mTz44IOy2267yT333CPTp0+XxYsXS1ERFQoAACC5rVseqB4ZOjbhu1UAkJQ0IbPz6kkyd9DXsnrdbJLoiF1dg7gsl1guSwp6DpdkpL2oNcmlvdHnvFMnJWv90nuAS/JSJJ1RX2uZBOv6nyxZXnSClKf1FGlMoPcZ4JZ9js6UzGySd52qsQLd3T98/LSO0PYfh/4yUxbOrpdvPgtUpb/1dI2M3tErU/ZLN0naA/e8Ufbe9SpJ8+ZQEBInfr9lCm/W1I+QH/ruK9VLtRd6YD0bOsYjex6eIb4G01XJfCdITgn/tXfXXXfJ2WefLWeccYa5rcn0119/Xf75z3/KVVddlejZS3mWZUlNpSWV5ZbpVaZ9W2sqRepqLTMAiB49rq/V5+mAloFT0T0e7VvmksxcHfhSe72K1FRZ0lBnBXsy6fP14KSOTK4reeB6YIUPm/RxfazxOTp5PI336XvppdclmVkuycp1ia/BkppqfS8x76fX7Q2JnvZiNiUhl00Db7b2eOC6zp/2l9KjdxmZLqmqDAx04qu3zLgdujz0PfUP7H83gq8b+tqRj4XdH1hmaRl6SpvL7GzoMquv88umjV6p3NggVVpIEbL8dDnovOl3ocvV/i50OXnSArf1tYPLV/+m8bq+r/kbv30Z+G78jbcl5Lr9mD7Xb1/6A6/r9wVipTP+wTTz1Sk654067/NEvq8lFRXpsnFFvbPfWyd8ngQtspT/QFYnxFRlRboU5zgcUwnWGevo+pWBHeC8wq6z3ADAUS6X9KwKVFSuXPuJNHy+QJKF/vuXXlYmvvxi8Xehf/+6jJra4FVvRq4kqwOPy5SX/x4oI/3p2wb56VuRPoMyZf+fWeJOdz4ZV1ka+I2vCXD9jR/4zdl4ae5r+t0e/nsxEPP2b0b9DawDgOr9RlpP8frrZNzwchlaVCk5mQ0iC0Viazbh4Gf8fpn4F/0orkF9xbPrBOlu/Osa27mkOZOy0337CVPTZegYr3z6Rq1s3uCXJfMbZN1yn+x8YLoMHO6V9LT4r18ae5qX0vitKAvkpDTXZPfY10uNy9Bci51bCuZlwq4Hcjea09De+24719SYewrkuDRponkneyZC56fpvrCfDPZt+2TT4INWzH9j/52ul1tL/KbiXNdLke1NJjbN1SBjh5TJ0KIKs575vmiavWjrm2fiduLKTK62Vt1NQpPodXV1Mm/ePLn66quD97ndbpk2bZrMmjWrxb+pra01k62srMxc+v1+M20rfQ1dsZ14rc6miXEd2EMnPfqtR7n0KKMmSJEMsvQ4f6JnAl2K/gPa2bu06NqIqW0xaJQ7Jfcf4imV96uQnIip1GS53ZJbmxO8XfHiS5LZkCnJIlt7zSZ6JhCVNaWpbVqyrvtZuSJHnJEh65b7zUCc2gqlZHWavPpIjfQZ6JaRO3il/7DmgzpGS5JrorGqLPB7XpONdtJRL6vKQxJ5Ds5/ryKRvvPekwHVP0n6OpPtS/heofXTamn4abV0V1ZGejDmnfj3L7dQZPpJGSaB/vXH9VJRaskH/6mVrNxa0zpEx/bRnvZaVOnVAWF7BNr3aI/1ds23ZUlVhSVlmyyTsNcc1daNgQM/3Tk/pQWXvVwbpd/G72RY1feStrp965lr6ABxpad1u30qfxLNX0KT6Bs3bhSfzyd9+/YNu19vf//99y3+zS233CLXX399s/tLSkqkpia2QS/a+nJKS0tNIGlCP5lVlbukeGWa1Ne6pGyTW7aWeESsFjZuLksyMi3JyLbEm25Jmk4ZgcnXoBtIS1xuPfrsMkf9dKPWUO+S2mqXuc/jbXx+umWO3ultPdrXdFTbFXaE2xyBM1Pz+00FtC9wf+C9XOb96mr0/bSvmiXpGZZ40gJV8vq+el+zI4Hmf4HPaldcB68HHw+/T9+roc4l1RUu8flckpZmSWau37yP2xP4jF57exTyfsHLyCOOwdcPnw/9XGb5VWkVemD5abW5uOolLd1jel253VZjNbgu48Zl0RA41Gq+C10ufjHfj97WzxpWQWD/rTkya0VU/9tHZq1AlXzjmQDBo7Xu8OebanRPU4V9V9IVP5NNt1H19XWSlpbufNWwK/6lu13uq+kKH8iyzMHt9PQ4xFRXXm6N8nr6pdZXLsXFiZ6T5JJK+1VIDcRUiirIlKyxO4nI6+bmluHZ0tvXX5KBxlJDQ4N4vd4udSZWV2GFlHVu2rRZKtKTJ5nSksIBIgX9RVYt9srSrzKlpsolq5b4ZdWSOsnK80t2nl8ysnXSbVjT2cH6m76qzC3VlW6prdTffq3Hov5+zczxm0Sn19v0+1l/e+pvfv1da/9uN2dlm8vw34H6O9HjtiS3h18ycwLLOcOdLa5VfRJf+uX3S9ryddIwoLdY3bTy1srKkOr+PcRq3Ll08t8/jdHdDnfJ959nyqa1XqmuEHOWfnCcn/A5kT6DG2TQdvWS19NnKr7DHrX0jAaXVG51S2WpW8o2e2TzOo/U17Y8j5qn0PjX+E3PCuSZ0jMDeR+NW43tYF5H14OQXIydcwnN0ehzNKekuZNgzqQxb+JvzLeY5afJ+5DVqtnm3u4y0NLtaH9n51winhfaGUFzLTkFfsnr4ZO8Hn7xrt0qmZ9vFvG3fz0rLdPvv77b7VOVl5dLskh4O5f20qp17aEeWok+ePBg6dOnj+Tn5zsSRLrzpK+XzEGk1lb65MevA0eubHrksLCPyxwt1AE+cgoCRxO1bQgSR+NKD/SkQlwh1WIqj5iCwzGVT0zBMam0X4XUQEylsFMHSt93XpUNG7+RjXvkytAJJ0oy/fuXT0wlJUszYM/eZa737t1HsjJ7SCro08cv/UeUSHlxT9m41pLVS31SXe42Uyy0MErbkOpvef1Nnxt26W4szIrDb/wjD5Bk4nAnnJSTG+d//4YMF6ks85uqcW2non39NZmurYDqakS2bPRL6UaRklVpZlIFvV2Sk+eSutpA4r1WW7GEp6UMncXsfJf06hcYJLNnX7dpfahtfLt1fqpfkcjkjrUoajqfq3vtU2VmJs+ZawlNovfu3Vs8Ho9s2LAh7H693a9fvxb/JiMjw0yR9At36kvXIHLy9eIlv4fIiPFeSc8Uyclzy6BRHjNKNpJTqsQVUgcxBacRU4gH4gpOI6ZSWSBxUlldklTfHzGVvLS1iU1zB6n0HWlLjLGT08S9s9v0ft603meSldqORS/1+IA9PpY+N7+nttJwm8R5t080otO2VXmFmtxuvNHC2L3rVvhk2aIG2bDKZ2K3dGNgCp8vbf3iMoPsFvZymxZGRYM8DJCZAlLh3z93Es1bQpPoesr4lClT5L333pMZM2YEj4To7QsuuCCRs5YS8nu6Zeoh3fPUJgAAAACpZejAvWXDxvlSX1+R6FlBirAktH1L6ibkMrJcMmB4yjUCAKT/UI+ZlPbq37jWZw4KpWe6TK90vdQ+6iTM0R0kfCuurVlOO+002XnnnWXXXXeVe+65RyorK+WMM85I9KwBAAAAABySk11kLn9c+Y4cJLclenaQCkIKXl1m8CcAiaJJ88GjE55GBBIm4dF//PHHmx501113naxfv14mTZokM2fObDbYKAAAAAAgdeVk9TGX6WmhnX6B1sR/wHsAAFIiia60dQvtWwAAAACg6yosGGYuG3w1iZ4VpNLAoiG9ewEASBTOhwIAAAAAxF2aJ8tc+v0N4vPXJ3p2kAKskEp0F+kLAEAC8a8QAAAAACDuvN7M4PXa2tKEzgtShBXWFD2RcwIA6OZIogMAAAAA4s7tTgte31L6U0LnBalYiU4SHQCQOCTRAQAAAABxpz2tc7P7mev+kF7XQCw90alEBwAkEkl0AAAAAECnyMwsNJeW35foWUGKoSc6ACCR+FcIAAAAANAp3C6vufRbDYmeFaRcJXoi5wQA0N2RRAcAAAAAdAqXy9M8OQpEFdoTnfQFACBx+FcIAAAAANAp3O5AEt1v0c4FbbOskCQ6PdEBAAlEEh0AAAAA0CncdiU6PdERk6YkOv1cAACJRBIdAAAAANApXMFKdHqio21UogMAkgVJdAAAAABA5w4sSiU6YmBJoHe+y0XqAgCQWPxLBAAAAADo1J7oFj3REYtgITpV6ACAxCKJDgAAAADoFK7GnuhUoqN9legk0QEAiUUSHQAAAADQuQOL0hMdsQj2RCeJDgBILJLoAAAAAIBO4XI39kSnnQtiYFmNlegk0QEACUYSHQAAAADQqZXotHNBLIJ16AwsCgBIMP4lAgAAAAB0CgYWRQdHFgUAIKFIogMAAAAAOoVdUUwlOtrVzoVKdABAggUa0gEAAAAAEGcul90TnYFFu7utZSulqrpE6huqpKGhRhp8NVJbVy719ZVSV18hG7f8IFtKf2p8Nj3RAQCJRRIdAAAAANDJ7VwCFcboOny+epP81qR4bV2ZbCiZL6UVq6W+vkrq6sqlrqFS6uurzXNKy1eI3x/7gZQ+vcbFdd4BAGgLSXQAAAAAQKcOLGrRziXpacudsoo1Ul9fYRLfgalS6uoqpKZ2q6xc+6lJlgcS59Xi89W2+z1ys/tJVlYv8XoyxOvJlPT0XElP0ylHvN5MSfPmyNCBe0tB3uC4fEYAAGJFEh0AAAAA0ClcjUl02rkkVoOv1lSHby1bIRs2LpC6+nKTINdWKtpSpaa2VDZsnN+h1zbJ8LRcycwslPzcgVLUawdJT8+TdG+2pKXplCMZ6fmSndlbcrL7OP7ZAACIB5LoAAAAAIBObefia0crD8TaSqUpEV5Vs8lc37DxG9lautxUjjdVkpeLz18X82tnZ/WWjLS8QCI8LUfS0nIlo7FivFfhaOnVY4xJjKc3Tm43aQYAQNfDv24AAAAAgE6tRP9xxduy185XJHp2UqZqXKvFa2tLTZW4JssDSfFKKd70rWwtXWb6kLefSzLS8yTNmy2D+u8u2Zm9AlXiGfmSmV5gHivIHya52UVx+FQAAKQWkugAAAAAgE7h8aSby/zcAYmelaTrP/7tD8/Lpq0/NCbKKwOJ8rpyKa9cG/PreL1Zku7NMX3G7crwzIweMnzw/qaFikmSa8/xxqpyl8sd188FAEBXQRIdAAAAANAp+vaaYC51IMquzLIsUx2uvcVr63QqM21WVq2bI5VVG6ShoVqqajab++xBO9vSu+dY00dcK8QzMgqCA3DmZBXJ4AFTzW1aqQAAEB/8CwsAAAAA6BRpaVnmcvPWpZKqtpatlJraLSZJbirGa7ZKTV2galxvl5avlLUb5nbotfNzB8nEcacEKsjTCyUjQyvG8wJ9ydPzHP8sAAAgNiTRAQAAAACdIiO9MHi9trbM9N9OhqrxhoYa8flrA4nxunKpqSuV5as+MJXkWiWu1eSaIK+q3mSux8rjTjefMSNdK8dzTBsVrRwf2HdnyczsEWix4s2SzIwC04pFrwMAgORDEh0AAAAA0CkK84cEr28tXyF9MwLtXeKdJNcWKqvWzTYV5JoUr6/XKvIKKa9cL+uKv9Rntes183IHSro32/QY1wS49h3XSnHTYiU9TzIzCmXIgD3NbZfLFbfPBgAAOgdJdAAAAABAp+lfNEXWFc+T9z79vZx41CsdSjJrYry6ZpNsLVshVTWbpK6uTBp8tbKldJkZkFMryzVJXllVHLjtq4npdb2eTElPz5WMtDxTKd6n5zjpUTBSMjPyTULc48kwLVeys3p14JMDAIBURRIdAAAAANBpBvQNJNHLKlbLG+9fKIfu93/idnuCj2/a8oMsXfG2VNdskd49x5iEtrZCqffVyPqS+bJ56xLTU12T4+2RldlLBhRNlqys3oHWKmbKk+oqv4wdvZ9kZfYImw8AAAAbSXQAAAAAQKfZecK5smnLYlm++kNZte4zefGNE+Xog/4hdQ2V8uncO2X56vebnvxj9NdxudySlzNAcrL6SEZGgbhdHvF6M6Ugf6jpO65J8pzsItNaJTuzl6SlZTd7Db/fL8XFxZKV2VPcbnecPjEAAEh1JNEBAAAAAJ1G27dM3+cuWbj4Wfl03h2yuXSpPPrifmHPGT74AMnJ7isVlesaB/usMO1ZtPf4mJFHSp8eY6VHwQiTNAcAAIg3kugAAAAAgE5PpE8Ye6KpFP9wzo1SW1dq7i/MHyb77vYH6V80OdGzCAAAEEQSHQAAAACQECOGHChDB+0jGzd/Z6rMC/IGJ3qWAAAAmiGJDgAAAABIGI87Tfr2npjo2QAAAIiKkVMAAAAAAAAAAIiCJDoAAAAAAAAAAJ2ZRF++fLmcddZZMnz4cMnKypKRI0fKH//4R6mrqwt73jfffCN77723ZGZmyuDBg+X222+Px+wAAAAAAAAAAJA8PdG///578fv98tBDD8moUaNk4cKFcvbZZ0tlZaXceeed5jllZWVy8MEHy7Rp0+TBBx+UBQsWyJlnnimFhYVyzjnnxGO2AAAAAAAAAABIfBL9kEMOMZNtxIgRsnjxYnnggQeCSfSnnnrKVKb/85//lPT0dBk/frx8/fXXctddd5FEBwAAAAAAAAB03SR6S0pLS6Vnz57B27NmzZJ99tnHJNBt06dPl9tuu022bNkiPXr0aPF1amtrzWTTinalle86bSt9DcuyHHktwEZcwWnEFJxGTCEeiCs4jZiC04gpxANxBacRU+iuMeVPovnrlCT60qVL5b777gtWoav169ebnumh+vbtG3wsWhL9lltukeuvv77Z/SUlJVJTU+PIl6MJfw0kt5txV+EM4gpOI6bgNGIK8UBcwWnEFJxGTCEeiCs4jZhCd42p8vJySckk+lVXXWUqxVvz3XffydixY4O316xZY1q7/PznPzd90bfV1VdfLZdddllYJboOStqnTx/Jz893JIhcLpd5vWQOIqQW4gpOI6bgNGIK8UBcwWnEFJxGTCEeiCs4jZhCd42pzMxMSckk+m9/+1s5/fTTW32O9j+3rV27Vvbff3/ZY4895OGHHw57Xr9+/WTDhg1h99m39bFoMjIyzBRJv3CnvnQNIidfD1DEFZxGTMFpxBTigbiC04gpOI2YQjwQV3AaMYXuGFPuJJq3diXR9eiETrHQCnRNoE+ZMkUeffTRZh966tSp8vvf/17q6+slLS3N3PfOO+/ImDFjorZyAQAAAAAAAAAg5XuiawJ9v/32k6FDh5o+6Nqv3GZXmZ900kmmt/lZZ50lv/vd72ThwoXyf//3f3L33Xe36720d0/oAKNOnM6g/Xb0dIFkOtqB1EZcwWnEFJxGTCEeiCs4jZiC04gpxANxBacRU+iuMVXWmO+1879dLomuFeU6mKhOgwYNCnvM/tAFBQXy9ttvy/nnn2+q1Xv37i3XXXednHPOOR1qMK990QEAAAAAAAAAXUd5ebnJJSeSy0qGVP42HjnR3ut5eXmml8+2sgcqXbVqlSMDlQKKuILTiCk4jZhCPBBXcBoxBacRU4gH4gpOI6bQXWPKsiyTQB8wYEDCK+bjUonemXQBRla7O0EDKJmDCKmJuILTiCk4jZhCPBBXcBoxBacRU4gH4gpOI6bQHWOqIMEV6LbkbXoDAAAAAAAAAECCkUQHAAAAAAAAACAKkugRMjIy5I9//KO5BJxCXMFpxBScRkwhHogrOI2YgtOIKcQDcQWnEVNwGjHVDQcWBQAAAAAAAAAgXqhEBwAAAAAAAAAgCpLoAAAAAAAAAABEQRIdAAAAAAAAAIAoSKIDAAAAAAAAABAFSXQAAAAAAAAAAKIgiQ6kgI0bNyZ6FgCgVVu3bk30LABAm9inApDs2KcCkAo2dsN9KpLoDlq9erW888478p///EdWrlyZ6NlBF/HVV19JUVGRfPbZZ4meFXQRbKsQj+1U7969Zf78+YmeFXQhbKvgNPap4DS2U3Aa+1SIB7ZVcNpX3XSfypvoGegqFixYIAcffLAMGjRIvvzyS9l5551ljz32kLvvvjvRs4YUpjtP++67r1xyySUmnoBtxbYK8dpOXXzxxbLjjjsmenbQRbCtgtPYp4LT2E7BaexTIR7YVsFp87vxPhWV6A4oLS2VU045RU444QRzdG/ZsmVy+OGHy9tvvy1HH310omcPKWrhwoVmg3TRRRfJXXfdJZZlmdjSI33FxcWJnj2kILZViNd2Sn/s/eUvfzH3lZSUyLfffis+ny/Rs4cUxbYKTmOfCk5jOwWnsU+FeGBbBact7O77VBa22bJly6ztttvOmj17dvC+srIy69lnn7VGjx5tnXjiiQmdP6Sempoa69BDD7W8Xm/wvsMPP9yaPHmy5XK5rF133dW68sorEzqPSD1sq+Ck8vJya++997Z69uwZvO/YY48NbqemTZtm/fWvf03oPCI1sa2Ck9inQjywnYKT2KdCvLCtgpNq2KeyqER3QH5+vtTW1ob1AsrLyzNH9n7/+9+bIzV///vfEzqPSC1paWkmdoYNGyb77LOPOf3K5XLJrbfeak7BOuigg2TmzJnmNhArtlVwktvtlrPPPlt69Oghxx13nBx66KEmvq666ir54IMPpE+fPvLEE0/I008/nehZRYphWwUnsU+FeGA7BSexT4V4YVsFJ6WxTyUuzaQneiZSXVVVlfz61782I9PecccdMn78+OBjlZWV5tSZgoICefLJJxM6n0gNukrqhkh98cUXcuqpp0pOTo68+uqrMmDAAHN/RUWF2dHS07P0fq+X4Q3QNt0e/eY3v2FbBcfU1NSYbdCVV15ptk86WFG/fv3MYxpnRx11lIwePVoef/zxRM8qUgj7VXAK+1SIF/ap4DT2qRAP7FPBKexTBVCJ3gGbNm2Sb775RpYuXSplZWWSnZ1tGurPmzdPbrzxRvnpp5+Cz9Wg0iM033//vVRXVyd0vpHc6uvrg9ftY1s66IdWHGhc6cjHSnvi5ebmynbbbWd6Tvn9/oTNM5KbxodWHXzyySdmx0m3R/a26qabbmJbhXbTvpzXX3998HZmZqYcccQRct9998l1111nKqXs7VTv3r1l0qRJsnLlyuA2DWgJ+1VwGvtUcBr7VHAa+1SIB/ap4DT2qcJ1vcMCcaYbpOOPP17q6uqkoaFB+vfvL3/9619NEL300kvm9AUNlvPOO8+MVquWLFliRkL2eDyJnn0kqcWLF8vNN98sl19+uUyYMMFsnOwjffbI7Hqan7LjSHeidGeKuEK0UdhPOukks53SyhaNq3/84x8yefLksG2VVlGxrUIs9FRQPb149erVsn79ennggQfM/bpzrqfyadzYsWNf6o68bqfsqgUgEvtVcBr7VHAa+1RwGvtUiAf2qeA09qlakOim7Klk7dq11qBBg0yj/IULF1ovvPCC9bOf/czKyMiwnn/+efOcWbNmWRMnTrSmTJli7bTTTtaMGTOs/Px8a/78+YmefSSpH3/80cRVYWGhddxxx1kLFiww9/v9/hafv3nzZuuaa66xioqKrEWLFnXy3CIVaFz07t3b+t3vfmctWbLEevzxx82AMl9//XXwOZ9++inbKrTbUUcdZV100UXWiBEjrDPPPDPq86qqqsx2qn///tb333/fqfOI1MF+FZzGPhWcxj4V4oV9KjiJfSo4jX2qltETvR3mzp0rZ5xxhrz++usyZMiQYB+pq6++Wh5++GF5+eWX5ZBDDjGnzsyfP1/+97//yeDBg2XGjBkyduzYRM8+kpCeNnXuueeaqpa99trLxFBhYaHccMMNssMOO4T1nVJvvvmmPPfcc/Luu+/Kf//7X9lpp50SOv9IPlu2bDE9E6dMmSL33HNP8P4DDzzQxJoOJDNu3DgzGAjbKrTXr371Kxk4cKCMGTNGLr74Yjn22GPlwQcfDG6PtJJFK11efPFFE1dvvPEG2ylExX4VnMQ+FZzGPhXiiX0qOIl9KjiJfaroaOfSDlu3bjW9y+zTEvRUGO0jdffdd5sBG0488UTTa2rUqFFm0n8IgdZkZWWZ06r0lD7dkdJ+UvqPnPbBszdQofQUmlWrVpnHR4wYkbD5RvLS7dNpp50W9g+X9ir78MMPZcOGDWZEbe17N2fOHJk4cSLbKsREe9xpbGmiQE8R1dPadcdJT+3TH3Xax/Pzzz83z9XT2xcuXCh/+tOfzABYQDTsV8FJ7FPBaexTIR7Yp0I8sE8FJ7FPFR2V6O1sqK+9o0aOHGl6S+koxrpx0h5A2vdHN0yHH364XHPNNcF/HIFYhB7Je+aZZ+Tvf/+7iS/dUdcRtHXjpSMc68bLjjkgmvLyclMdpZ599lm54IILTO9OPYqs8aMjaWu8aXVLeno68YSYadXKHXfcYaoNlMaUVr5Mnz5dXnnlleDz2E4hFuxXIR7Yp4KT2KdCvLBPBSexT4V4YJ+qua7/CR3k9XrlF7/4hRl8QUfN1tNj7CDRU2b0SJ823ldslBAL+xiWbpj0HzOl/8Dp0T7dGF177bXy9ddfmxG1d911V1OtwGAyaCue7B97ar/99pO3337bnKrXu3dv8w+cPXiMVrp0h3/o4AzdMcrIyDCDYKlzzjlHfvzxR7Mz/tVXX5mBjGzEFWLBfhWcxD4VnMQ+FeKJfSo4jX0qOIl9quho59IG+yidfVRFRzLWDZMeHdY+QX/4wx/MqQ5Kd6R69eplnqsB1F2CCB2Pq9AYCY0z+7S+Rx55xJxGo0eW33rrLVPhArSkpYoCHZW9X79+Zgo9kqyX2sNT/0bjjW0VYokpjZVddtlFtttuO1Mt9dNPP5m+d3pKqMbYXXfdJevWrZP+/fsndL6R3NivgtPYp4LT2KeC09inQjywTwWnsU/VNtq5xEAX0fnnn29Of9FJA0WPvOgpWLpxOvroo2XZsmXy6quvmp5422+/faJnGSkUVxo/etpe6P32RksrXnTgj48//rhZ3ykg1piy6RFiPfVK+5lpP08dyAhoT0xpEmH//feX5cuXm3/z7D6xWu2ij+npfUBb2K+C09ingtPYp4LT2KdCPLBPBaexT9U6KtFjMHv2bPn000/N0RUdjV1P17vpppvkgw8+kOeff16++OIL6dOnj3z22WdslNDhuNJTsJRumHTHSUfSnjVrlomv7rZhgrMxpfQH3tNPP21G1p45cyY/9tDumNI+i/rvn+6Ua29F7blo01NEgWgVLTqFVqiwX4V4xxT7VIhXTCn2qbCtMcU+FTqiuLjYJMkHDhwYvE8T4+xTIZ4xxT5VOCrRQ2gfsueee86cTrXHHnuYnlK5ubnmMR0hW0+3KiwsbPZ3ugh1ol8ZnIyrxx9/XHbccUeZNGlSAuYaXSmmdPukp/Xpzvvpp58uY8eOTeDcI5VjKrQCIfQ60BLtvamnpOul9ks888wzg9sf9qvQmTHFPhWciin2qeBUTLFPhfZYsWKFTJ06VQ499FDTOz/0wAv7VOjMmHq8m+9TkURvtGDBAhM8EyZMMP1+tCfZVVddZY7itWTp0qWmZxngZFxpIit04wU4ta3STb2Onq3VCUAotlOIV1wdcMAB5jRQ7e+qO9yacLrjjjtafD77VXA6pthWIV7bKfapEA3bKcTLSy+9JMcee6ypDNaK8osvvlhGjBjR4nPZp4LTMcW2KoQm0bu7lStXWmPGjLGuuOKK4H0vv/yylZOTY3377bfNnv/AAw9Y48ePtz7++ONOnlOkEuIKyRJTH330USfPKVIF2ynEw7Jly6wRI0ZYV199dfC+u+++2/rlL39pVVZWhj3X7/cTV2gTMYVkiSn2qRAN2ynE09KlS63p06dbN998szVx4kTrggsusNasWdPsecQVYkVMdUy3P6dDq+60n50eqbv88svNfdq/bOeddzZHj2tqapr9Td++fWX06NEyaNCgBMwxUgFxhWSKqcGDBydgjpHs2E4hHuyzGY444gj57W9/G1bB8u2335r4OvXUU+WRRx4x9+vp6xpvxBWiIaaQTDHFPhVawnYK8aT75xoza9eulQsvvFDOPvts0+P8//7v/8zgtJdeemnwucQVYkFMbYMOJt+7lFmzZllXXXVVs/tHjRplvfrqqy3+TXl5eSfMGVIZcQWnEVNwGjGFeNAqli+//DJ4+/rrr7fS09OtW2+91Xr44Yet4447ztpjjz2sBQsWBJ9TUVGRoLlFKiCm4DRiCk4jphBvRx11lDVv3jxz/aGHHrJ69+5t5ebmWv/+97/DnkdcIVbEVPs1DTPeDdnt4HfffXczRQ7q4fF4zOizttdee02GDx8u48ePDw64BkQiruA0YgpOI6YQD3YMDRgwwExKz2ior683Zz1o73211157mR78OujaDjvsYO7LyclJ6LwjORFTcBoxBacRU+gsOhbDJ598IpMnT5Y5c+ZIXV2dqQ7WCuKJEycG+6ATV4gVMdV+3bKdS1lZmVRWVpp/7CLHVdX7NHGgU1ZWlhQUFJj7dbRabbqfl5eXoLlGsiOu4DRiCk4jptDZcaUD7/3pT38ySQR9TCe9T09tHzhwYMLmGcmNmILTiCk4jZhCPOMqsl2Q0njSODrvvPNk5syZ8sUXX8gFF1wg//73v+Uf//iHOXADRCKmnNXtkuiLFi2SQw45xIyUrYHkdjdfBFqBp/8YasDo5Q033CD33nuvOUIzZMiQhMw3khtxBacRU3AaMYXOiCv7bIZQdqzpYzppT1itdNGzG4BIxBScRkzBacQUOiOuImOpqKhIfv3rX8tLL70kr776qmy33XZy/vnny+9+9zs555xzJC0tLYFzj2RETMWB1Y2sWLHCmjBhgpWfn29Gn/3nP//Zam+fXXfd1dphhx2sjIwMa+7cuZ06r0gdxBWcRkzBacQUkiGutA/sNddcY54/f/78Tp1XpAZiCk4jpuA0YgqdHVd+v99cbt682brzzjutr776ytz2+XwJnWckN2IqPrpNJbqervDmm2+aSjrt9aP9fW677TZ5/vnng0dk7NOw9LmlpaWyevVq07Ns7ty5MmXKlAR/AiQj4gpOI6bgNGIKiY4rtWrVKrn++uvlrbfeko8++sg8HwhFTMFpxBScRkwhEXFln+nQo0cPufjii2XSpEnmdktnlQKKmIofl2bSpZv4/vvvTVLg6KOPNrdPOeUUmTdvnlx55ZXy85//vNlgaW+88YYMHjzYDAACRENcwWnEFJxGTCEZ4uq7776TwsJC6d+/f4LmGMmOmILTiCk4jZhCMsSVz+czbRiBaIipOLG6EfuUhVAnn3yyNWbMmLBTG5588kmrvLw8AXOIVERcwWnEFJxGTCHRcdXaqe6AjZiC04gpOI2YQiLj6umnn7YqKysTMIdINcRUfHSrSvRoR1l++ctfmlPWL7/8cpk9e7a88sor5jaDqKG9iCs4jZiC04gpxANxBacRU3AaMQWnEVNIRFxpNbGeMQrEiphyTrdNoquGhgbxer3BQHrqqackJydHPvzwQ5k8eXKiZw8piriC04gpOI2YQjwQV3AaMQWnEVNwGjGFeCCu4DRiyhndumu8BpAekVE9e/Y0TfW16T4BhG1BXMFpxBScRkwhHogrOI2YgtOIKTiNmEI8EFdwGjHljG5diW7717/+Jaeddpo5pYEAglOIKziNmILTiCnEA3EFpxFTcBoxBacRU4gH4gpOI6a2TZdKouvos2+99Zace+65kpmZGfPfVVdXS0lJCf3K0CLiCk4jpuA0YgrxQFzBacQUnEZMwWnEFOKBuILTiKnECDTE6QKWLl0qe+yxh2zdutUExJ/+9Kdgv5+2ZGVlEUBoEXEFpxFTcBoxhXggruA0YgpOI6bgNGIK8UBcwWnEVOJ0iSR6RUWF3HrrrTJ9+nTZd9995cILL5T6+nq56aabYg4kIBJxBacRU3AaMYV4IK7gNGIKTiOm4DRiCvFAXMFpxFRidYklXFNTI2PHjpVhw4bJcccdJ7169ZKTTjpJXC6X3HjjjS0Gknax0ceBaIgrOI2YgtOIKcQDcQWnEVNwGjEFpxFTiAfiCk4jphLM6iI2bNgQdvvZZ5+1vF6vdeWVV1r19fXmvoaGBmv58uUJmkOkIuIKTiOm4DRiCvFAXMFpxBScRkzBacQU4oG4gtOIqcRJ+Up0+4hKUVFR2O3jjz/e3D7llFPM7WuvvVauueYaKSsrk/vvv1+ys7MTPOdIZsQVnEZMwWnEFOKBuILTiCk4jZiC04gpxANxBacRU4nn0ky6pJg1a9bI+vXrZaeddhK3293qc5977jk544wzzKkOixcvlrlz55q/AyIRV3AaMQWnEVOIB+IKTiOm4DRiCk4jphAPxBWcRkwlGSvFfPfdd1ZmZqY1YcIEa+7cuZbf72/zbw444ACrZ8+e1jfffNMp84jUQ1zBacQUnEZMIR6IKziNmILTiCk4jZhCPBBXcBoxlXxaP4yRZDZu3CgXXHCBzJgxQxoaGuTMM8+UefPmmVMYWuL3++Wqq66S999/30wTJkzo9HlG8iOu4DRiCk4jphAPxBWcRkzBacQUnEZMIR6IKziNmEpO7lQ7jWHkyJFyySWXyNdffy0+n0/OOuusqIFUUVEhAwYMMM+dOHFiQuYZyY+4gtOIKTiNmEI8EFdwGjEFpxFTcBoxhXggruA0Yio5pVRP9OrqalmyZImMHz9ePB6P1NTUyJQpU8Tr9cojjzwiO++8s3meBpc+rvSIjT4ORENcwWnEFJxGTCEeiCs4jZiC04gpOI2YQjwQV3AaMZWcUiqJHqqurk7S09PNpTbKtwNphx12kLvuuksKCgrk/PPPT/RsIsUQV3AaMQWnEVOIB+IKTiOm4DRiCk4jphAPxBWcRkwlj5RNooceZbEDKSMjQ4YOHSqvv/66OYVh++23T/QsIgURV3AaMQWnEVOIB+IKTiOm4DRiCk4jphAPxBWcRkwlh5ROoocGUnl5uRQWFprpvffek0mTJiV61pDCiCs4jZiC04gpxANxBacRU3AaMQWnEVOIB+IKTiOmEi/lm+VoAGmvoKuvvloyMzPl448/5ggMthlxBacRU3AaMYV4IK7gNGIKTiOm4DRiCvFAXMFpxFTiuaUL2Lhxo2m4//777xNAcAxxBacRU3AaMYV4IK7gNGIKTiOm4DRiCvFAXMFpxFRipXw7F6UfQUeqzcrKSvSsoAshruA0YgpOI6YQD8QVnEZMwWnEFJxGTCEeiCs4jZhKrC6RRAcAAAAAAAAAIB66RDsXAAAAAAAAAADigSQ6AAAAAAAAAABRkEQHAAAAAAAAACAKkugAAAAAAAAAAERBEh0AAAAAAAAAgChIogMAAAAAAAAAEAVJdAAAAAAAAAAAoiCJDgAAAAAAAABAFCTRAQAAAAAAAACIgiQ6AAAAAAAAAABRkEQHAAAAAAAAACAKkugAAAAAAAAAAERBEh0AAAAAAAAAgChIogMAAAAAAAAAEAVJdAAAAAAAAAAAoiCJDgAAAAAAAABAFCTRAQAAAAAAAACIgiQ6AACIuwULFshxxx0nQ4cOlczMTBk4cKAcdNBBct9990l3s3btWvnTn/4kX3/9dUzP//bbb+XnP/+5jBgxQrKzs6V3796yzz77yH//+98Oz4Pf75fHHntMjjrqKBk8eLDk5OTIDjvsIDfeeKPU1NS0+DePPPKIjBs3znx/o0ePbvG7+89//iPHH398cF7HjBkjv/3tb2Xr1q3Nnjts2DBxuVzNpl//+tcxf441a9bIL37xCyksLJT8/Hw5+uij5aeffmrxuRs2bJBzzz3XxJ5+Bn3/s846K6b3+fzzz+W8886TKVOmSFpampnPlqxatUquv/562XXXXaVHjx7mu9pvv/3k3Xffbdd3c/vtt8vw4cPNfE6cOFGeeeaZZs9radnZk65b7XX22Webvz3iiCOaPXbppZfK5MmTpWfPnuZ71TjQGK6oqHA83mP1/fffy5VXXimTJk2SvLw86d+/vxx++OEyd+7cDsdKe74/XX+iLf/169fH/DliWa9szz33nEydOtWsr/o59thjD/nf//4X0/vcdNNNZn3v27evmUf9TlrSnnU4mu+++04OOeQQyc3NNTHzy1/+UkpKSpo9b+nSpebfBV3W+l577bWXvP/++zG9x6ZNm+SOO+4w28I+ffqY5bH77rubZdSS2tpa+d3vficDBgyQrKws2W233eSdd96J+TMBAAB4Ez0DAACga/vss89k//33lyFDhphEXb9+/Uyyavbs2fJ///d/cuGFF0p3oklFTdRpElcTgG1ZsWKFlJeXy2mnnWYSQFVVVfLvf//bJMQeeughOeecc9o9D/oaZ5xxhkk6adK6qKhIZs2aJX/84x/lvffeM4m50ESxvo8+79hjj5XLLrtMPv74Y7nooovM62hiyqbzovN4yimnmO9bD5789a9/lTfeeEO+/PJLk7wKpZ9fE3Shtttuu5g+gyZwNa5KS0vlmmuuMcntu+++W/bdd1+TsO3Vq1fwuRpve+65p7mun0MT6fo9aHI8Fjr///jHP0xCW5OLP/zwQ4vPe+WVV+S2226TGTNmmO+roaFBnnjiCZPU/uc//2mWeVt+//vfy6233mrWlV122cW85kknnWS+jxNOOCH4vH/961/N/lYTyLpOHXzwwTF9rtC/06SwJnJb8sUXX8jee+9t5l+f89VXX5l51OTyRx99JG6327F4j5V+H5qA1pjUAxwaBxqnGtMzZ86UadOmtTtWOvL93XDDDeaARyhN6MYi1vVKadJb30uTzqeffrrU19fLwoULzcGBWPzhD38w296ddtpJ3nrrrajPa+86HGn16tUmsV1QUCA333yzWfZ33nmneR1d39LT04PrpB4Q8Hg8csUVV5gDA48++qiJXd0G6Wu0RrdXuq4cdthh5rN5vV6zXdR1ZNGiRSbmQukye/HFF+WSSy4xBys03vVvNWmvyXsAAIA2WQAAAHF02GGHWX369LG2bNnS7LENGzZY3c0XX3xh6S7Yo48+2uHXaGhosHbccUdrzJgxHfr72tpa69NPP212//XXX2/m7Z133gneV1VVZfXq1cs6/PDDw5578sknWzk5OdbmzZuD973//vvNXvPxxx83r/n3v/897P6hQ4c2e832uO2228zrfv7558H7vvvuO8vj8VhXX3112HMPPfRQa/jw4dbGjRs79F7r1683y0Gdf/755n1bsnDhQqukpCTsvpqaGmvs2LHWoEGD2nyf1atXW2lpaeY9bH6/39p7773N3+v33pqzzjrLcrlc1qpVq2L8ZIHXnzp1qnXmmWe26zu58847zXKYNWtW3OO9JXPnzrXKy8vD7tPvV7c1e+65Z4dipT3fn34efU39fB3RnvVKl7F+r3fddZfVUcuWLTOX+vl0vv/4xz+2+Lz2rMMt+c1vfmNlZWVZK1asCN6n2xP9+4ceeih433nnnWd5vV7r+++/D95XWVlpDR482Jo8eXKb7/PTTz9Zy5cvbxbLBxxwgJWRkWFVVFQE758zZ455/zvuuCN4X3V1tTVy5EgT+wAAALGgnQsAAIirH3/8UcaPH99idaZWQEd68sknTdsMrXjUVgBaWahVi5Huv/9+UxWsz9P2C1rFqa0XdLJ98MEHpoL3+eefN5WJWoGsrR+0mlOrUvUUf61M1PnQ1gNaaar3dWSe9H21JYpWQWrVq7Yn0PfT1hyh86PVxUrfy27/oFWR7aHVm9qGJbLFgn4mbXOhl63RalBtBRHpZz/7WbAdg00rNbV1glb7hjr//POlsrJSXn/99bBlEMtrhqqrqzOv015aVarL0l6eauzYsXLggQea79umy+PNN9801a5acaztarSKtz20BUZbFbhK41xbgITKyMgwFa9aoatnFLRGK6F13kKXtcbHb37zG/P3Wn0bjcatVuJqdfWgQYOarYM6tUQr2rWiWdt9tIdWlqvW2nzEEu8vvPBCcN3SZacV0LFUV+vf6DobSr9frZiPjLVYY6Wj35/e7/P5pD3as17dc889por84osv1qM3MbXRifZ9taUj63AojUFtCaRV7DY9K0DPMAld1rq91qp4bRdj022mnmGjFe9Llixpdbum1f/aHiyUxpaeRaDrQmirHv3+dZsZetaOnlGh7Zx0nWrp3xcAAIBIJNEBAEBcaaJj3rx5JlHXFk3knXrqqeZ0+7vuusskuO1T+0OTdQ888IBccMEFJlmoSWpNnGnyRBNdLbnllltMC4OrrrpKzjzzTNP3V9so6HVtzaGtEo455hiT3NN2Dh2ZJ7VlyxbTC3jHHXeUv/zlLyZRp20ZNImrtPextmRQmtDRBKZObbUuUJpY27hxo0mGaisKfU1NAoZ66aWXzHvoZUfYvZxDE4naukPtvPPOzZKY2sbDfrw9r2nTtjGaONNkqCb5tBVJrH3Dv/nmm2bzpPSAii4jO+Fp97PWRLguL03W6nTooYfK8uXLpTPoMtDPqVNrdFlqWwv9DiM/k/14NNpuQ+Px5JNPbvaYfu7IWFG6jDQ+tcWJJmlbo61NNP60Pcvbb79tWmjoASl73lrSVrzr+qZ9yjXBqeuotrDRdVPba7SnB3fksg6NtfbESke+Pz1gpj3W7QRwaPK3Ne1Zr3R7owcA7r33XtP/2+4Br21WOkNr63AoPfhRXFwcdVmHfiZNdLd0YMpexvpvRke2a9G2YZrE1+8pcp6U0/***************************************************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", "text/plain": ["<Figure size 1500x1200 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "\n", "def plot_timeseries_with_gaps(df, time_col, features, gap_threshold='1H'):\n", "    \"\"\"\n", "    Plot time series data with gaps removed from x-axis\n", "    \n", "    Parameters:\n", "    - df: DataFrame with time series data\n", "    - time_col: name of datetime column\n", "    - features: list of 3 feature column names\n", "    - gap_threshold: pandas timed<PERSON>ta string for gap detection\n", "    \"\"\"\n", "    \n", "    # Ensure datetime column is datetime type\n", "    df = df.copy()\n", "    df[time_col] = pd.to_datetime(df[time_col])\n", "    df = df.sort_values(time_col)\n", "    \n", "    # Find gaps in the data\n", "    time_diffs = df[time_col].diff()\n", "    gap_threshold_td = pd.Timedel<PERSON>(gap_threshold)\n", "    gap_indices = time_diffs > gap_threshold_td\n", "    \n", "    # Create segments\n", "    segments = []\n", "    start_idx = 0\n", "    \n", "    for i, is_gap in enumerate(gap_indices):\n", "        if is_gap:\n", "            if start_idx < i:\n", "                segments.append(df.iloc[start_idx:i])\n", "            start_idx = i\n", "    \n", "    # Add the last segment\n", "    if start_idx < len(df):\n", "        segments.append(df.iloc[start_idx:])\n", "    \n", "    # Create the plot\n", "    fig, axes = plt.subplots(len(segments), 1, figsize=(15, 4*len(segments)))\n", "    if len(segments) == 1:\n", "        axes = [axes]\n", "    \n", "    colors = sns.color_palette(\"husl\", len(features))\n", "    \n", "    for i, segment in enumerate(segments):\n", "        ax = axes[i]\n", "        \n", "        for j, feature in enumerate(features):\n", "            ax.plot(segment[time_col], segment[feature], \n", "                   label=feature, color=colors[j], linewidth=1.5)\n", "        \n", "        ax.set_title(f'Segment {i+1}: {segment[time_col].iloc[0].strftime(\"%Y-%m-%d %H:%M\")} to {segment[time_col].iloc[-1].strftime(\"%Y-%m-%d %H:%M\")}')\n", "        ax.legend()\n", "        ax.grid(True, alpha=0.3)\n", "        \n", "        # Rotate x-axis labels for better readability\n", "        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "df.reset_index(inplace = True)\n", "plot_timeseries_with_gaps(df, 'timestamp', ['vd_rps', 'sh', 'ewt', 'brine_in'])"]}, {"cell_type": "code", "execution_count": null, "id": "aac9a75e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/tn/8gmb5b0d3n939xr1fwtj8s2m0000gn/T/ipykernel_55341/1960175523.py:27: FutureWarning: 'H' is deprecated and will be removed in a future version. Please use 'h' instead of 'H'.\n", "  gaps = time_diffs > pd.Timedelta(gap_threshold)\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "30d0f68b00134740b55fb20682774c8c", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(HTML(value='<b>Instructions:</b> Click and drag on the plots to select time ranges to remove'),…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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************************************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****************************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", "text/plain": ["<Figure size 1500x1200 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from matplotlib.patches import Rectangle\n", "import ipywidgets as widgets\n", "from IPython.display import display, clear_output\n", "\n", "class TimeSeriesCleaner:\n", "    def __init__(self, df, time_col, features, gap_threshold='1H'):\n", "        self.df = df.copy()\n", "        self.time_col = time_col\n", "        self.features = features\n", "        self.removed_ranges = []\n", "        \n", "        self.df[time_col] = pd.to_datetime(self.df[time_col])\n", "        self.df = self.df.sort_values(time_col).reset_index(drop=True)\n", "        \n", "        self.segments = self._find_segments(gap_threshold)\n", "        self.selection_mode = False\n", "        self.start_x = None\n", "        \n", "        # Enable interactive mode\n", "        plt.ion()\n", "        \n", "    def _find_segments(self, gap_threshold):\n", "        time_diffs = self.df[self.time_col].diff()\n", "        gaps = time_diffs > pd.Timedelta(gap_threshold)\n", "        \n", "        segments = []\n", "        start = 0\n", "        \n", "        for i, is_gap in enumerate(gaps):\n", "            if is_gap:\n", "                if start < i:\n", "                    segments.append(self.df.iloc[start:i])\n", "                start = i\n", "        \n", "        if start < len(self.df):\n", "            segments.append(self.df.iloc[start:])\n", "            \n", "        return segments\n", "    \n", "    def _create_plot(self):\n", "        n_segments = len(self.segments)\n", "        self.fig, axes = plt.subplots(n_segments, 1, figsize=(15, 4*n_segments))\n", "        self.axes = [axes] if n_segments == 1 else axes\n", "        \n", "        colors = sns.color_palette(\"husl\", len(self.features))\n", "        self.patches = [[] for _ in range(n_segments)]\n", "        \n", "        for i, segment in enumerate(self.segments):\n", "            ax = self.axes[i]\n", "            \n", "            for j, feature in enumerate(self.features):\n", "                ax.plot(segment[self.time_col], segment[feature], \n", "                       label=feature, color=colors[j], linewidth=1.5)\n", "            \n", "            start_time = segment[self.time_col].iloc[0].strftime(\"%m/%d %H:%M\")\n", "            end_time = segment[self.time_col].iloc[-1].strftime(\"%m/%d %H:%M\")\n", "            ax.set_title(f'Segment {i+1}: {start_time} - {end_time}')\n", "            ax.legend()\n", "            ax.grid(True, alpha=0.3)\n", "            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)\n", "            \n", "            # Connect mouse events\n", "            ax.figure.canvas.mpl_connect('button_press_event', self._on_press)\n", "            ax.figure.canvas.mpl_connect('button_release_event', self._on_release)\n", "            ax.figure.canvas.mpl_connect('motion_notify_event', self._on_motion)\n", "        \n", "        plt.tight_layout()\n", "        return self.fig\n", "    \n", "    def _get_segment_for_axes(self, ax):\n", "        for i, segment_ax in enumerate(self.axes):\n", "            if ax == segment_ax:\n", "                return i\n", "        return None\n", "    \n", "    def _on_press(self, event):\n", "        if event.inaxes in self.axes and event.button == 1:\n", "            self.selection_mode = True\n", "            self.start_x = event.xdata\n", "            self.current_ax = event.inaxes\n", "    \n", "    def _on_motion(self, event):\n", "        if self.selection_mode and event.inaxes == self.current_ax and event.xdata:\n", "            # Clear previous selection rectangle\n", "            for patch in getattr(self, 'temp_patches', []):\n", "                patch.remove()\n", "            \n", "            # Draw new selection rectangle\n", "            ylim = self.current_ax.get_ylim()\n", "            width = event.xdata - self.start_x\n", "            patch = Rectangle((self.start_x, ylim[0]), width, ylim[1]-ylim[0], \n", "                            facecolor='red', alpha=0.2, edgecolor='red')\n", "            self.current_ax.add_patch(patch)\n", "            self.temp_patches = [patch]\n", "            self.fig.canvas.draw()\n", "    \n", "    def _on_release(self, event):\n", "        if self.selection_mode and event.inaxes == self.current_ax and event.xdata:\n", "            segment_idx = self._get_segment_for_axes(self.current_ax)\n", "            if segment_idx is not None:\n", "                xmin = min(self.start_x, event.xdata)\n", "                xmax = max(self.start_x, event.xdata)\n", "                self._add_removal(xmin, xmax, segment_idx)\n", "            \n", "            # Clear temp patches\n", "            for patch in getattr(self, 'temp_patches', []):\n", "                patch.remove()\n", "            self.temp_patches = []\n", "            \n", "        self.selection_mode = False\n", "        self.start_x = None\n", "    \n", "    def _add_removal(self, xmin, xmax, segment_idx):\n", "        start_time = pd.to_datetime(xmin, origin='unix', unit='D')\n", "        end_time = pd.to_datetime(xmax, origin='unix', unit='D')\n", "        \n", "        self.removed_ranges.append({\n", "            'segment_idx': segment_idx,\n", "            'start': start_time,\n", "            'end': end_time,\n", "            'xmin': xmin,\n", "            'xmax': xmax\n", "        })\n", "        \n", "        ax = self.axes[segment_idx]\n", "        ylim = ax.get_ylim()\n", "        patch = Rectangle((xmin, ylim[0]), xmax-xmin, ylim[1]-ylim[0], \n", "                         facecolor='red', alpha=0.4, edgecolor='darkred', linewidth=2)\n", "        ax.add_patch(patch)\n", "        self.patches[segment_idx].append(patch)\n", "        \n", "        # Add removal count text\n", "        mid_x = (xmin + xmax) / 2\n", "        mid_y = (ylim[0] + ylim[1]) / 2\n", "        text = ax.text(mid_x, mid_y, f'#{len(self.removed_ranges)}', \n", "                      ha='center', va='center', color='white', fontweight='bold')\n", "        self.patches[segment_idx].append(text)\n", "        \n", "        self.fig.canvas.draw()\n", "        self._update_status()\n", "    \n", "    def _update_status(self):\n", "        if hasattr(self, 'status_widget'):\n", "            with self.status_output:\n", "                clear_output(wait=True)\n", "                if self.removed_ranges:\n", "                    removed_count = sum(self._count_removed_rows(r) for r in self.removed_ranges)\n", "                    print(f\"Marked {len(self.removed_ranges)} ranges for removal ({removed_count:,} rows)\")\n", "                else:\n", "                    print(\"No ranges selected\")\n", "    \n", "    def _count_removed_rows(self, removal):\n", "        segment = self.segments[removal['segment_idx']]\n", "        mask = (segment[self.time_col] >= removal['start']) & \\\n", "               (segment[self.time_col] <= removal['end'])\n", "        return mask.sum()\n", "    \n", "    def clear_selections(self):\n", "        self.removed_ranges = []\n", "        for patches in self.patches:\n", "            for patch in patches:\n", "                patch.remove()\n", "            patches.clear()\n", "        self.fig.canvas.draw()\n", "        self._update_status()\n", "    \n", "    def undo_last(self):\n", "        if not self.removed_ranges:\n", "            return\n", "            \n", "        self.removed_ranges.pop()\n", "        \n", "        # Remove last two patches (rectangle and text) from any segment that has them\n", "        for patches in self.patches:\n", "            if patches:\n", "                patches.pop().remove()\n", "                if patches:\n", "                    patches.pop().remove()\n", "                break\n", "                \n", "        self.fig.canvas.draw()\n", "        self._update_status()\n", "    \n", "    def export_csv(self, filename=None):\n", "        cleaned_df = self.get_cleaned_data()\n", "        if filename is None:\n", "            filename = f\"cleaned_data_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv\"\n", "        cleaned_df.to_csv(filename, index=False)\n", "        print(f\"Exported {len(cleaned_df):,} rows to {filename}\")\n", "        return cleaned_df\n", "    \n", "    def get_cleaned_data(self):\n", "        if not self.removed_ranges:\n", "            return self.df.copy()\n", "            \n", "        mask = pd.Series(True, index=self.df.index)\n", "        \n", "        for removal in self.removed_ranges:\n", "            segment = self.segments[removal['segment_idx']]\n", "            segment_mask = (segment[self.time_col] >= removal['start']) & \\\n", "                          (segment[self.time_col] <= removal['end'])\n", "            mask.loc[segment.index[segment_mask]] = False\n", "            \n", "        return self.df[mask].copy()\n", "    \n", "    def show(self):\n", "        # Create the plot\n", "        self.fig = self._create_plot()\n", "        \n", "        # Create control widgets\n", "        clear_btn = widgets.Button(description='Clear All', button_style='warning')\n", "        undo_btn = widgets.Button(description='Undo Last', button_style='info')\n", "        export_btn = widgets.Button(description='Export CSV', button_style='success')\n", "        \n", "        clear_btn.on_click(lambda b: self.clear_selections())\n", "        undo_btn.on_click(lambda b: self.undo_last())\n", "        export_btn.on_click(lambda b: self.export_csv())\n", "        \n", "        controls = widgets.HBox([clear_btn, undo_btn, export_btn])\n", "        \n", "        # Status output\n", "        self.status_output = widgets.Output()\n", "        \n", "        # Display everything\n", "        display(widgets.VBox([\n", "            widgets.HTML(\"<b>Instructions:</b> Click and drag on the plots to select time ranges to remove\"),\n", "            controls,\n", "            self.status_output\n", "        ]))\n", "        \n", "        plt.show()\n", "        self._update_status()\n", "        \n", "        return self\n", "\n", "# Usage function\n", "def clean_timeseries(df, time_col, features, gap_threshold='1H'):\n", "    return TimeSeriesCleaner(df, time_col, features, gap_threshold).show()\n", "\n", "cleaner = TimeSeriesCleaner(df.reset_index(), 'timestamp', ['vd_rps', 'sh', 'ewt', 'brine_in'])\n", "cleaner.show()\n", "\n", "# Get cleaned data\n", "cleaned_df = cleaner.get_cleaned_data()\n"]}], "metadata": {"kernelspec": {"display_name": "eda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}