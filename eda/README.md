# Exploratory Data Analysis

Utilities for loading recorded accelerometer data into Pandas.

## Importing

```
python import.py <readings_dir>
```

`readings_dir` points to a directory containing `readings-*.json` and the
corresponding CSV files for each sensor (`*-s1.csv` and `*-s2.csv`).

Running the script creates a `datasets` directory alongside `readings` and
writes the following files:

- `sensor1.parquet`
- `sensor2.parquet`
- `dataset.json`

Each parquet file contains `time`, `batch_id`, `x`, `y` and `z` columns for the
respective sensor.
