import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns


_cache = {}


def load_dataset(name: str, sensor: int, window: int) -> pd.DataFrame:
    if (name, sensor, window) in _cache:
        return _cache[(name, sensor, window)]
    
    git_cdup = os.popen("git rev-parse --show-cdup").read().strip()
    df = pd.read_parquet(f"{git_cdup}/data/datasets/{name}/sensor{sensor}w{window}.parquet")
    df.set_index("timestamp", inplace=True)
    df.columns = df.columns.str.lower()

    _cache[(name, sensor, window)] = df
    
    return df

def load_hp_dataset(name: str) -> pd.DataFrame:
    git_cdup = os.popen("git rev-parse --show-cdup").read().strip()
    df = pd.read_parquet(f"{git_cdup}/data/datasets/{name}/heatpump.parquet")
    df.set_index("timestamp", inplace=True)
    df.columns = df.columns.str.lower()
    
    return df

