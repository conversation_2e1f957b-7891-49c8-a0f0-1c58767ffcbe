[build-system]
requires = ["flit_core>=3.4"]
build-backend = "flit_core.buildapi"

[project]
name = "eda"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "duckdb>=1.3.0",
    "fastparquet>=2024.11.0",
    "matplotlib>=3.9.0",
    "missingno>=0.5.2",
    "numpy>=2.1.0",
    "pandas>=2.2.3",
    "plotly>=6.1.1",
    "pyarrow>=20.0.0",
    "scikit-learn>=1.6.1",
    "scipy>=1.15.3",
    "seaborn>=0.13.2",
    "statsmodels>=0.14.4",
    "tsfresh>=0.21.0",
    "ydata-profiling==4.16.1",
]

[dependency-groups]
dev = [
    "dash>=3.0.4",
    "ipykernel>=6.29.5",
    "ipywidgets>=8.1.7",
    "jupyterlab>=4.4.3",
    "plotly>=6.1.1",
    "sweetviz>=2.3.1",
]
