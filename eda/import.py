import glob
import json
import os
import sys
from pathlib import PurePath

import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq


def import_sensor_write(
    sensor_root: str,
    sensor: int,
    json_files: list[str],
    heatpump_df: pd.DataFrame,
    heatpump_intervals: list[tuple[pd.Timestamp, pd.Timestamp]],
    out_path: PurePath,
):
    """
    For a single sensor, process each CSV (per JSON metadata) one at a time,
    filter and merge with heat pump data, and write directly to a Parquet file
    in append mode using pyarrow, avoiding holding all sensor data in memory.

    Arguments:
      sensor_root: directory containing sensor CSVs and JSONs
      sensor: sensor index (1 or 2)
      json_files: list of metadata JSON file paths
      heatpump_df: full heat pump DataFrame (sorted by TIME)
      heatpump_intervals: list of (start, end) times
      out_path: path to write the combined Parquet file

    Returns:
      list of batch metadata dictionaries
    """
    sensor_cols = ["batch_id", "time", "x", "y", "z"]
    hp_cols = [c for c in heatpump_df.columns if c != "TIME"]
    batches = []

    # Prepare heat pump for merge_asof: rename and sort
    hp_df = heatpump_df.rename(columns={"TIME": "hp_time"}).sort_values("hp_time").reset_index(drop=True)

    writer = None

    for i, meta_path in enumerate(json_files):
        meta = json.load(open(meta_path))
        csv_file = os.path.join(sensor_root, meta[f"sensor{sensor}_file"])

        # Read entire sensor CSV for this batch (already chunked)
        df = pd.read_csv(csv_file, sep=",")
        # Convert time_offset to UTC timestamp
        df["time"] = pd.to_datetime(df["time_offset"], unit="us", utc=True)
        df.drop("time_offset", axis=1, inplace=True)
        df["batch_id"] = i

        # Filter by any heat pump interval
        mask = pd.Series(False, index=df.index)
        for hp_start, hp_end in heatpump_intervals:
            mask |= df["time"].between(hp_start, hp_end)
        df = df[mask]
        if df.empty:
            continue

        # Record batch start/end
        tmin = df["time"].min()
        tmax = df["time"].max()
        batches.append({
            "start": tmin.isoformat(),
            "end": tmax.isoformat(),
            "file": csv_file,
            "num_rows": len(df),
            "id": i,
        })

        # Sort and merge
        df = df.sort_values("time").reset_index(drop=True)
        merged = pd.merge_asof(
            df.rename(columns={"time": "sensor_time"}),
            hp_df,
            left_on="sensor_time",
            right_on="hp_time",
            direction="nearest",
            tolerance=pd.Timedelta("1s"),
        )
        merged = merged.rename(columns={"sensor_time": "time"}).drop(columns=["hp_time"])
        merged = merged[sensor_cols + hp_cols]

        # Convert pandas DataFrame to pyarrow Table
        table = pa.Table.from_pandas(merged, preserve_index=False)

        # Initialize writer on first batch
        if writer is None:
            writer = pq.ParquetWriter(str(out_path), table.schema)

        writer.write_table(table)

    if writer:
        writer.close()

    return batches


def import_heatpump(hp_root: str):
    """
    Load all heat pump CSVs (semicolon-separated, European decimals) in one shot.
    Returns DataFrame hp_all (with "TIME" as tz-aware UTC) and intervals.
    """
    hp_cols = [
        "Time",
        "flow_brine",
        "flow_heat",
        "HP",
        "LP",
        "T_discharge",
        "T_suction",
        "T_liquid",
        "EWT",
        "LWT",
        "brine_out",
        "brine_in",
        # add more columns if needed
    ]

    heatpump_files = sorted(glob.glob(os.path.join(hp_root, "*.csv")))
    intervals = []
    parts = []

    for csv_file in heatpump_files:
        df_hp = pd.read_csv(
            csv_file,
            sep=";",
            header=1,
            skiprows=[2],
            usecols=hp_cols,
            decimal=",",
            parse_dates=["Time"],
            dayfirst=True,
            encoding="windows-1252",
        )
        if df_hp.empty:
            continue

        df_hp["TIME"] = df_hp["Time"].dt.tz_localize("Europe/Berlin").dt.tz_convert("UTC")
        df_hp = df_hp.drop(columns=["Time"]).dropna(subset=["TIME"])

        for col in hp_cols[1:]:
            df_hp[col] = pd.to_numeric(df_hp[col].str.replace(".", "").str.replace(",", "."), errors="raise")
            df_hp[col] = pd.to_numeric(df_hp[col], errors="coerce")

        start = df_hp["TIME"].min()
        end = df_hp["TIME"].max()
        intervals.append((start, end))
        parts.append(df_hp)

    if not parts:
        cols = ["TIME"] + [c for c in hp_cols if c != "Time"]
        return pd.DataFrame(columns=cols), []

    hp_all = pd.concat(parts, ignore_index=True).sort_values("TIME").reset_index(drop=True)
    return hp_all, intervals


def main():
    if len(sys.argv) < 4:
        if len(sys.argv) == 3:
            sensor_root = os.path.join(sys.argv[2], "readings")
            hp_root = os.path.join(sys.argv[2], "measurements")
        else:
            print("Usage: import.py <name> <sensor_dir> <heatpump_dir>")
            sys.exit(1)
    else:
        sensor_root = sys.argv[2]
        hp_root = sys.argv[3]

    out_dir = os.path.join("../datasets", sys.argv[1])
    os.makedirs(out_dir, exist_ok=True)

    print(f"Importing sensor data from {sensor_root}")
    print(f"Importing heat pump data from {hp_root}")

    print("  Loading heat pump CSV files...")
    hp_df, heatpump_intervals = import_heatpump(hp_root)
    if not heatpump_intervals:
        print("    No heat pump files found or empty. Exiting.")
        sys.exit(1)

    json_files = sorted(glob.glob(os.path.join(sensor_root, "readings-*.json")))
    if not json_files:
        print("No sensor metadata JSON files found")
        sys.exit(1)

    metadata0 = json.load(open(json_files[0]))

    # Process each sensor separately, writing to its own Parquet
    for s in (1, 2):
        print(f"  Processing sensor {s}...")
        out_file = PurePath(out_dir, f"sensor{s}.parquet")
        batches = import_sensor_write(sensor_root, s, json_files, hp_df, heatpump_intervals, out_file)
        # Save metadata for sensor1 (or both, if desired)
        if s == 1:
            if batches:
                start = batches[0]["start"]
                end = batches[-1]["end"]
            else:
                start = None
                end = None
            meta = {
                "batches": batches,
                "start": start,
                "end": end,
                "num_batches": len(batches),
                "scenario": metadata0.get("scenario"),
            }
            json.dump(meta, open(PurePath(out_dir, "dataset.json"), "w"))


if __name__ == "__main__":
    main()
