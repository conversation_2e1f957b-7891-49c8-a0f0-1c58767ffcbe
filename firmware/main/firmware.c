/*
 * Example: Dual GY-291 ADXL345 @ 3200 Hz, full-resolution ±16 g on ESP32-S3 DevKitC-1
 *
 * – SPI bus on SPI3_HOST (sensors)
 * – SPI bus on SPI2_HOST (SD-card)
 * – SCLK = GPIO 15 (ADXL345)
 * – MOSI (SDA) = GPIO 17 (ADXL345)
 * – MISO (SDO) = GPIO 18 (ADXL345)
 *
 * Sensor 1:
 * – CS1 = GPIO 5
 * – INT1 = GPIO 12 (Data-Ready interrupt)
 *
 * Sensor 2:
 * – CS2 = GPIO 6
 * – INT2 = GPIO 11 (Data-Ready interrupt)
 *
 * SD-card (SPI3):
 * – SCLK = GPIO 14
 * – MOSI = GPIO 13
 * – MISO = GPIO 10
 * – CS   = GPIO 9
 *
 * This firmware:
 *  1. Initializes two SPI buses (one for ADXL345, one for SD-card).
 *  2. Attempts to initialize both ADXL345 sensors.
 *  3. Attempts to mount an SD-card (but does not abort if no card/reader present).
 *  4. Aborts after 1 second if no ADXL345 sensors are detected.
 *  5. Creates separate data and rate tasks for each connected sensor.
 */

#include <stdio.h>
#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "esp_log.h"
#include "esp_err.h"
#include "adxl345.h"
#include "led_status.h"
#include "uart_transmit.h"
#include "sample_processing.h"
#include "interface.h"
#include "sdstream.h"

#define TAG "FIRMWARE"

//=== GPIO / SPI DEFINES ===
// ADXL345 (SPI3)
#define SPI_HOST_SENSORS SPI3_HOST
#define PIN_SPI_SCLK 15
#define PIN_SPI_MOSI 17 // SDA
#define PIN_SPI_MISO 18 // SDO

// Sensor 1 pins
#define PIN_SPI_CS1 5
#define PIN_INT1 12 // Data-Ready (connected to ADXL345 sensor 1 INT1)

// Sensor 2 pins
#define PIN_SPI_CS2 6
#define PIN_INT2 11 // Data-Ready (connected to ADXL345 sensor 2 INT1)

// SD-card (SPI2)
#define SPI_HOST_SD SPI2_HOST
#define PIN_SD_SCLK 14
#define PIN_SD_MOSI 13
#define PIN_SD_MISO 10
#define PIN_SD_CS 9

// Sample queue configuration
#define SAMPLE_QUEUE_SIZE 1000 // Queue depth for packed samples

//=== IMPLEMENTATION ===

/**
 * @brief Helper function to initialize a single ADXL345 sensor
 */
static esp_err_t init_sensor(adxl345_dev_t *dev, const char *sensor_name)
{
    esp_err_t ret;

    // Add the device to the SPI bus (creates spi_hdl)
    ret = adxl345_add_device(dev, SPI_HOST_SENSORS);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "Could not add %s to SPI bus: %s", sensor_name, esp_err_to_name(ret));
        return ret;
    }

    // Setup GPIO interrupt and semaphore
    ret = adxl345_setup_interrupt(dev);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "Failed to setup %s interrupt: %s", sensor_name, esp_err_to_name(ret));
        return ret;
    }

    // Initialize the sensor registers
    ret = adxl345_init(dev);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "%s init failed: %s", sensor_name, esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "%s initialized successfully", sensor_name);
    return ESP_OK;
}

/**
 * @brief Main entry: initialize SPI (sensors + SD), set up ADXL345 sensors, try mounting SD, and start tasks.
 */
void app_main(void)
{
    // Install ISR service for GPIO interrupts
    gpio_install_isr_service(0);

    esp_err_t ret;

    // 0) Initialize LED status system and set to initializing
    ret = led_status_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "LED status init failed: %s", esp_err_to_name(ret));
        // Continue without LED status indication
    }
    else
    {
        led_status_set(LED_STATUS_INITIALIZING);
    }
    vTaskDelay(pdMS_TO_TICKS(10));

    // 1) Initialize SPI bus for ADXL345 sensors (SPI2)
    ret = adxl345_spi_bus_init(SPI_HOST_SENSORS, PIN_SPI_MISO, PIN_SPI_MOSI, PIN_SPI_SCLK);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "SPI2 (sensors) bus init failed, halting.");
        return;
    }
    vTaskDelay(pdMS_TO_TICKS(10));

    // 1.1) Initialize SPI bus for SD-card (SPI3)
    ret = sdstream_spi_bus_init(SPI_HOST_SD, PIN_SD_MISO, PIN_SD_MOSI, PIN_SD_SCLK, PIN_SD_CS);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "SD-card SPI3 bus init failed (no reader?): %s", esp_err_to_name(ret));
    }
    else
    {
        // 1.2) Attempt to mount the SD card at "/sdcard"
        ret = sdstream_mount("/sdcard");
        if (ret != ESP_OK)
        {
            ESP_LOGW(TAG, "SD-card mount failed (no card or not formatted?): %s", esp_err_to_name(ret));
        }
        else
        {
            ESP_LOGI(TAG, "SD-card mounted at /sdcard");
        }
    }
    start_sdstream_writer_task(); // Start the SD writer task

    // 2) Set up ADXL345 sensor instances
    static adxl345_dev_t sensor1 = {
        .spi_hdl = NULL,
        .cs_io = PIN_SPI_CS1,
        .int_pin = PIN_INT1,
        .data_sem = NULL,
        .sample_count = 0};

    static adxl345_dev_t sensor2 = {
        .spi_hdl = NULL,
        .cs_io = PIN_SPI_CS2,
        .int_pin = PIN_INT2,
        .data_sem = NULL,
        .sample_count = 0};

    // 3) Try to initialize both sensors
    bool sensor1_ok = false;
    bool sensor2_ok = false;

    ESP_LOGI(TAG, "Attempting to initialize sensors...");

    // Try sensor 1
    if (init_sensor(&sensor1, "Sensor 1") == ESP_OK)
    {
        sensor1_ok = true;
    }

    // Try sensor 2
    if (init_sensor(&sensor2, "Sensor 2") == ESP_OK)
    {
        sensor2_ok = true;
    }

    // Check if at least one sensor is working
    if (!sensor1_ok && !sensor2_ok)
    {
        ESP_LOGE(TAG, "No sensors detected. Sleeping for 3 second before aborting...");
        vTaskDelay(pdMS_TO_TICKS(3000));
        ESP_LOGE(TAG, "Aborting - no sensors found.");
        abort();
    }

    ESP_LOGI(TAG, "Sensor init status: Sensor1=%s, Sensor2=%s",
             sensor1_ok ? "OK" : "FAILED",
             sensor2_ok ? "OK" : "FAILED");

    // 3.2) Create sample queue for UART transmission
    QueueHandle_t sample_queue = xQueueCreate(SAMPLE_QUEUE_SIZE, sizeof(adxl345_sample_t));
    if (sample_queue == NULL)
    {
        ESP_LOGE(TAG, "Failed to create sample queue");
        abort();
    }
    ESP_LOGI(TAG, "Sample queue created (size=%d)", SAMPLE_QUEUE_SIZE);

    // 3.3) Initialize UART0 for high-performance data transmission
    ret = uart_transmit_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "UART transmission init failed: %s", esp_err_to_name(ret));
        abort();
    }

    // 3.4) Create sample processing task
    ret = sample_processing_create_task(sample_queue, "sample_proc", 4096, 6);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to create sample processing task");
        abort();
    }

    // 4) Create tasks for working sensors
    if (sensor1_ok)
    {
        // DataTask for sensor 1 (higher priority) – with sample queue
        ret = adxl345_create_data_task(&sensor1, "sensor1_data", 4096,
                                       configMAX_PRIORITIES - 2, sample_queue, 0);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to create DataTask for Sensor 1.");
            abort();
        }

        // RateTask for sensor 1 (lower priority)
        ret = adxl345_create_rate_task(&sensor1, "sensor1_stats", 4096, 5);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to create RateTask for Sensor 1.");
            abort();
        }
        ESP_LOGI(TAG, "Tasks created for Sensor 1");
    }

    if (sensor2_ok)
    {
        // DataTask for sensor 2 (higher priority) – with sample queue
        ret = adxl345_create_data_task(&sensor2, "sensor2_data", 4096,
                                       configMAX_PRIORITIES - 2, sample_queue, 1);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to create DataTask for Sensor 2.");
            abort();
        }

        // RateTask for sensor 2 (lower priority)
        ret = adxl345_create_rate_task(&sensor2, "sensor2_stats", 4096, 5);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to create RateTask for Sensor 2.");
            abort();
        }
        ESP_LOGI(TAG, "Tasks created for Sensor 2");
    }

    vTaskDelay(pdMS_TO_TICKS(3500));
    sensor1_ok = sensor1_ok && (sensor1.sample_rate > 100);
    sensor2_ok = sensor2_ok && (sensor2.sample_rate > 100);
    if (sensor1_ok && sensor2_ok)
    {
        led_status_set(LED_STATUS_TWO_SENSORS);
    }
    else if (sensor1_ok)
    {
        led_status_set(LED_STATUS_SENSOR1_ONLY);
    }
    else if (sensor2_ok)
    {
        led_status_set(LED_STATUS_SENSOR2_ONLY);
    }
    else
    {
        led_status_set(LED_STATUS_INITIALIZING);
        ESP_LOGE(TAG, "No sensors detected. Sleeping for 1 second before aborting...");
        vTaskDelay(pdMS_TO_TICKS(1000));
        ESP_LOGE(TAG, "Aborting - no sensors found.");
        abort();
    }

    // 5) Update sensor ok and LED status based on sensor detection
    while (1)
    {
        vTaskDelay(pdMS_TO_TICKS(1000));
        if (sensor1_ok)
        {
            if (sensor1.sample_rate < 100)
            {
                sensor1_ok = false;
                printf("Sensor 1 disconnected\n");
                abort();
            }
        }
        if (sensor2_ok)
        {
            if (sensor2.sample_rate < 100)
            {
                sensor2_ok = false;
                printf("Sensor 2 disconnected\n");
                abort();
            }
        }
    }
}
