#ifndef SDSTREAM_H
#define SDSTREAM_H

#include "esp_err.h"
#include "driver/spi_common.h"
#include "adxl345.h" // for adxl345_sample_t

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief   Initialize SPI bus parameters for the SD-card.
     *
     * @param   host       Which SPI peripheral to use (e.g. SPI2_HOST or SPI3_HOST)
     * @param   miso_pin   GPIO number for MISO
     * @param   mosi_pin   GPIO number for MOSI
     * @param   sclk_pin   GPIO number for SCLK
     * @param   cs_pin     GPIO number for CS (chip select)
     * @return  ESP_OK on success, error code otherwise
     */
    esp_err_t sdstream_spi_bus_init(spi_host_device_t host,
                                    int miso_pin,
                                    int mosi_pin,
                                    int sclk_pin,
                                    int cs_pin);

    /**
     * @brief   Mount the SD card onto a FAT filesystem at `mount_point`.
     *          (Must be called after sdstream_spi_bus_init.)
     *
     * @param   mount_point   e.g. "/sdcard"
     * @return  ESP_OK on success, error code otherwise
     */
    esp_err_t sdstream_mount(const char *mount_point);

    /**
     * @brief   Unmount the SD card and free resources.
     *
     * @return  ESP_OK on success, error code otherwise
     */
    esp_err_t sdstream_unmount(void);

    /**
     * @brief   Write binary data to the current “hourly” file.
     *          If no file is open yet, or if the hour has rolled over,
     *          closes the old file (if any) and opens a new one named:
     *             <mount_point>/data_YYYYMMDD_HH.bin
     *
     * @param   data   pointer to the buffer
     * @param   size   number of bytes to write
     * @return  ESP_OK on success, error code otherwise
     */
    esp_err_t sdstream_write(adxl345_sample_t data);

    /**
     * @brief   Force-close the current file (if open). After this,
     *          the next call to sdstream_write() will open a fresh file
     *          (with a timestamp based on the current hour).
     *
     * @return  ESP_OK on success, error code otherwise
     */
    esp_err_t sdstream_close_file(void);

    /**
     * @brief   Check if the SD card is currently mounted.
     *
     * @return  true if mounted, false otherwise
     */
    bool sdstream_is_mounted(void);

    /**
     * @brief   Start the background task that writes samples to the SD card.
     *          This task will read from a queue of packed samples.
     */
    void start_sdstream_writer_task(void);

#ifdef __cplusplus
}
#endif

#endif // SDSTREAM_H
