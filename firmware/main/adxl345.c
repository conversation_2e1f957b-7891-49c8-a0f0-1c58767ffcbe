/*
 * ADXL345 Accelerometer Driver for ESP32
 *
 * This module provides functions to initialize and communicate with
 * ADXL345 accelerometer over SPI interface.
 */

#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <inttypes.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "driver/spi_master.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "esp_err.h"
#include "esp_timer.h"
#include "adxl345.h"

#define TAG "ADXL345"

//=== INTERNAL TYPES ===

/**
 * @brief Parameters passed to the data task
 */
typedef struct
{
    adxl345_dev_t *dev;         ///< Pointer to ADXL345 device
    QueueHandle_t sample_queue; ///< Optional queue for packed samples (can be NULL)
    uint8_t sensor_id;          ///< Sensor identifier (0 or 1)
} adxl345_data_task_params_t;

//=== STATIC FUNCTION PROTOTYPES ===
static esp_err_t adxl345_write_reg(adxl345_dev_t *dev, uint8_t reg, uint8_t value);
static esp_err_t adxl345_read_regs(adxl345_dev_t *dev, uint8_t first_reg, uint8_t *buf, size_t len);
static void IRAM_ATTR adxl345_isr_handler(void *arg);
static void adxl345_data_task(void *pvParameters);
static void adxl345_rate_task(void *pvParameters);

//=== IMPLEMENTATION ===

/**
 * @brief Initialize SPI bus for ADXL345 communication.
 */
esp_err_t adxl345_spi_bus_init(spi_host_device_t spi_host, int miso_pin, int mosi_pin, int sclk_pin)
{
    spi_bus_config_t buscfg = {
        .miso_io_num = miso_pin,
        .mosi_io_num = mosi_pin,
        .sclk_io_num = sclk_pin,
        .quadwp_io_num = -1,
        .quadhd_io_num = -1,
        .max_transfer_sz = 0 // default: small transfers
    };
    esp_err_t ret = spi_bus_initialize(spi_host, &buscfg, SPI_DMA_DISABLED);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "spi_bus_initialize failed: %s", esp_err_to_name(ret));
    }
    return ret;
}

/**
 * @brief Add one ADXL345 device to the SPI bus, creating spi_hdl.
 */
esp_err_t adxl345_add_device(adxl345_dev_t *dev, spi_host_device_t spi_host)
{
    spi_device_interface_config_t devcfg = {
        .clock_speed_hz = 2 * 1000 * 1000, // 2 MHz (ADXL345 max ~5 MHz)
        .mode = 3,                         // CPOL=1, CPHA=1 ⇒ SPI mode 3
        .spics_io_num = dev->cs_io,        // GPIO for CS
        .queue_size = 4,
        .flags = 0, // ADXL345 uses half-duplex
    };
    esp_err_t ret = spi_bus_add_device(spi_host, &devcfg, &dev->spi_hdl);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "spi_bus_add_device failed for CS=%d: %s",
                 (int)dev->cs_io, esp_err_to_name(ret));
    }
    return ret;
}

/**
 * @brief Write one byte to ADXL345 register.
 */
static esp_err_t adxl345_write_reg(adxl345_dev_t *dev, uint8_t reg, uint8_t value)
{
    // For a write: command byte = register address (0x00–0x3F). MSB=0 (write).
    uint8_t sendbuf[2] = {reg & 0x3F, value};
    spi_transaction_t t = {
        .flags = 0,      // MSB=0 ⇒ write
        .length = 8 + 8, // 1 register byte + 1 data byte = 16 bits
        .tx_buffer = sendbuf,
    };
    esp_err_t ret = spi_device_transmit(dev->spi_hdl, &t);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "SPI write_reg(0x%02X) failed: %s",
                 (unsigned int)reg, esp_err_to_name(ret));
    }
    return ret;
}

/**
 * @brief Read multiple bytes from consecutive ADXL345 registers.
 * @param first_reg: starting register (0x00–0x3F)
 * @param buf: buffer to hold 'len' bytes of data
 * @param len: number of bytes to read (<=16)
 */
static esp_err_t adxl345_read_regs(adxl345_dev_t *dev, uint8_t first_reg, uint8_t *buf, size_t len)
{
    // Command = 1<<7 (read) | (len>1 ? 1<<6 (MB) : 0) | (first_reg & 0x3F)
    uint8_t cmd = ADXL345_SPI_READ_BIT | ((len > 1) ? ADXL345_SPI_MB_BIT : 0) | (first_reg & 0x3F);

    // We send 1 byte (cmd), then read 'len' bytes back
    uint8_t recvbuf[1 + 16]; // max 16 data bytes + 1 dummy
    memset(recvbuf, 0, sizeof(recvbuf));

    spi_transaction_t t = {
        .flags = 0,
        .length = 8 * (1 + len), // bits: 1 cmd + len data
        .tx_buffer = &cmd,
        .rxlength = 8 * (1 + len),
        .rx_buffer = recvbuf,
    };
    esp_err_t ret = spi_device_transmit(dev->spi_hdl, &t);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "SPI read_regs(0x%02X, len=%d) failed: %s",
                 (unsigned int)first_reg, (int)len, esp_err_to_name(ret));
        return ret;
    }
    // Copy only the len data bytes (skip the first dummy byte)
    memcpy(buf, &recvbuf[1], len);
    return ESP_OK;
}

/**
 * @brief ISR for ADXL345 data-ready (INT1). Simply "gives" the counting semaphore.
 */
static void IRAM_ATTR adxl345_isr_handler(void *arg)
{
    adxl345_dev_t *dev = (adxl345_dev_t *)arg;
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    xSemaphoreGiveFromISR(dev->data_sem, &xHigherPriorityTaskWoken);
    if (xHigherPriorityTaskWoken == pdTRUE)
    {
        portYIELD_FROM_ISR();
    }
}

/**
 * @brief Initialize ADXL345: verify device ID, set 3200 Hz, full-res ±16 g, enable interrupts.
 */
esp_err_t adxl345_init(adxl345_dev_t *dev)
{
    esp_err_t ret;
    uint8_t whoami = 0;

    // 1) Read device ID (REG 0x00)
    ret = adxl345_read_regs(dev, ADXL345_REG_DEVID, &whoami, 1);
    if (ret != ESP_OK)
        return ret;
    if (whoami != ADXL345_DEVICE_ID)
    {
        ESP_LOGE(TAG, "Unexpected device ID: 0x%02X (expected 0x%02X)",
                 (unsigned int)whoami,
                 (unsigned int)ADXL345_DEVICE_ID);
        return ESP_FAIL;
    }
    ESP_LOGI(TAG, "ADXL345 found. DEVID=0x%02X", (unsigned int)whoami);

    // 2) Set BANDWIDTH & data rate (0x2C = BW_RATE)
    ret = adxl345_write_reg(dev, ADXL345_REG_BW_RATE, ADXL345_RATE_3200HZ);
    if (ret != ESP_OK)
        return ret;

    // 3) Set DATA_FORMAT => full resolution + ±16 g (0x31)
    {
        uint8_t datafmt = ADXL345_FULL_RES_BIT | ADXL345_RANGE_16G; // 0b00001011
        ret = adxl345_write_reg(dev, ADXL345_REG_DATA_FORMAT, datafmt);
        if (ret != ESP_OK)
            return ret;
    }

    // 4) Enable Data-Ready interrupt only (0x2E)
    ret = adxl345_write_reg(dev, ADXL345_REG_INT_ENABLE, ADXL345_INT_DATA_READY);
    if (ret != ESP_OK)
        return ret;

    // 5) Make sure Data Ready is mapped to INT1 (optional; default is INT1)
    ret = adxl345_write_reg(dev, ADXL345_REG_INT_MAP, ADXL345_INT1_DRDY);
    if (ret != ESP_OK)
        return ret;

    // 6) Set POWER_CTL => Measure mode (0x2D)
    ret = adxl345_write_reg(dev, ADXL345_REG_POWER_CTL, ADXL345_MEASURE_BIT);
    if (ret != ESP_OK)
        return ret;

    // 7) Clear any pending interrupts by reading DATAX0..DATAZ1 once
    {
        uint8_t dummy[6];
        ret = adxl345_read_regs(dev, ADXL345_REG_DATAX0, dummy, 6);
        if (ret != ESP_OK)
            return ret;
    }

    ESP_LOGI(TAG, "ADXL345 initialized (3200 Hz, ±16 g, full-res).");
    return ESP_OK;
}

/**
 * @brief Setup GPIO interrupt for ADXL345 data ready signal
 */
esp_err_t adxl345_setup_interrupt(adxl345_dev_t *dev)
{
    // Create a counting semaphore (initial count = 0, max count = large)
    dev->data_sem = xSemaphoreCreateCounting(10000, 0);
    if (dev->data_sem == NULL)
    {
        ESP_LOGE(TAG, "Failed to create counting semaphore.");
        return ESP_FAIL;
    }

    // Configure INT1 pin as input, rising-edge interrupt. we rely on the pull-up resistor on the gy-291 board
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_POSEDGE,
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = (1ULL << dev->int_pin),
        .pull_down_en = 0,
        .pull_up_en = 0};
    gpio_config(&io_conf);

    // ISR service installed in main firmware.c
    // Attach our handler
    gpio_isr_handler_add(dev->int_pin, adxl345_isr_handler, dev);

    return ESP_OK;
}

/**
 * @brief Task: Wait on data_sem, then read 6 bytes of accel data, increment sample_count.
 *        Optionally pack and push samples to a queue.
 */
static void adxl345_data_task(void *pvParameters)
{
    adxl345_data_task_params_t *params = (adxl345_data_task_params_t *)pvParameters;
    adxl345_dev_t *dev = params->dev;
    QueueHandle_t sample_queue = params->sample_queue;
    uint8_t sensor_id = params->sensor_id;

    uint8_t buf[6];
    int16_t raw_x, raw_y, raw_z;

    while (1)
    {
        // Block until ISR gives the semaphore
        if (xSemaphoreTake(dev->data_sem, portMAX_DELAY) == pdTRUE)
        {
            esp_err_t ret = adxl345_read_regs(dev, ADXL345_REG_DATAX0, buf, 6);
            if (ret == ESP_OK)
            {
                raw_x = (int16_t)((buf[1] << 8) | buf[0]);
                raw_y = (int16_t)((buf[3] << 8) | buf[2]);
                raw_z = (int16_t)((buf[5] << 8) | buf[4]);

                dev->sample_count++;

                // If a queue is provided, pack the sample and push it
                if (sample_queue != NULL)
                {
                    adxl345_sample_t packed_sample;
                    adxl345_pack_sample(&packed_sample, sensor_id, raw_x, raw_y, raw_z);

                    // Try to send to queue (non-blocking)
                    if (xQueueSend(sample_queue, &packed_sample, 0) != pdTRUE)
                    {
                        // Queue is full
                        ESP_LOGE(TAG, "Sample queue full");
                        vTaskDelay(pdMS_TO_TICKS(100)); // Brief delay before retrying
                        abort();
                    }

                    // temp hack to send twice as many samples
                    // packed_sample.data[0] ^= 0x01;
                    // if (xQueueSend(sample_queue, &packed_sample, 0) != pdTRUE)
                    // {
                    //     // Queue is full
                    //     ESP_LOGE(TAG, "Sample queue full");
                    //     vTaskDelay(pdMS_TO_TICKS(100)); // Brief delay before retrying
                    //     abort();
                    // }
                }
            }
            else
            {
                ESP_LOGE(TAG, "Failed to read accel data");
            }
        }
    }
}

/**
 * @brief Task: Print accurate samples per second by measuring actual time elapsed.
 */
static void adxl345_rate_task(void *pvParameters)
{
    adxl345_dev_t *dev = (adxl345_dev_t *)pvParameters;
    uint32_t last_count = 0;
    int64_t start_time_us = esp_timer_get_time();

    while (1)
    {

        // Sleep for approximately 1 second, but measure actual time for accuracy
        vTaskDelay(pdMS_TO_TICKS(1000));

        uint32_t curr_count = dev->sample_count;
        int64_t time_delta_us = esp_timer_get_time() - start_time_us;
        start_time_us = esp_timer_get_time();

        uint32_t sample_delta = curr_count - last_count;

        // Calculate samples per second: (samples * 1,000,000 us/s) / time_us
        float samples_per_sec = 0.0f;
        if (time_delta_us > 0)
        {
            samples_per_sec = (float)(sample_delta * 1000000LL) / (float)time_delta_us;
        }

        ESP_LOGI(TAG, "Samples/sec: %.1f", samples_per_sec);
        dev->sample_rate = (uint32_t)samples_per_sec;

        // Update for next iteration
        last_count = curr_count;
    }
}

/**
 * @brief Pack accelerometer data into tightly packed sample structure
 */
void adxl345_pack_sample(adxl345_sample_t *sample, uint8_t sensor_id, int16_t x, int16_t y, int16_t z)
{
    // ADXL345 in full-resolution mode at ±16g produces 13-bit values
    // Bits D12:0 carry the signed payload; bits D13-D15 are sign-extension copies of bit 12
    // So we mask off the top three sign-extension bits to get the actual 13-bit values
    uint16_t x13 = (uint16_t)x & 0x1FFF; // Keep bits 12:0
    uint16_t y13 = (uint16_t)y & 0x1FFF; // Keep bits 12:0
    uint16_t z13 = (uint16_t)z & 0x1FFF; // Keep bits 12:0

    // Create a 40-bit value and pack it (1 bit sensor + 3×13 bits data = 40 bits = 5 bytes)
    uint64_t packed = 0;
    packed |= (uint64_t)(sensor_id & 0x01);   // Bit 0
    packed |= (uint64_t)(x13 & 0x1FFF) << 1;  // Bits 1-13
    packed |= (uint64_t)(y13 & 0x1FFF) << 14; // Bits 14-26
    packed |= (uint64_t)(z13 & 0x1FFF) << 27; // Bits 27-39

    // Extract bytes (little-endian)
    sample->data[0] = packed & 0xFF;
    sample->data[1] = (packed >> 8) & 0xFF;
    sample->data[2] = (packed >> 16) & 0xFF;
    sample->data[3] = (packed >> 24) & 0xFF;
    sample->data[4] = (packed >> 32) & 0xFF;
}

/**
 * @brief Unpack accelerometer data from tightly packed sample structure
 */
void adxl345_unpack_sample(const adxl345_sample_t *sample, uint8_t *sensor_id, int16_t *x, int16_t *y, int16_t *z)
{
    // Reconstruct the 40-bit packed value (little-endian)
    uint64_t packed = 0;
    packed |= (uint64_t)sample->data[0];
    packed |= (uint64_t)sample->data[1] << 8;
    packed |= (uint64_t)sample->data[2] << 16;
    packed |= (uint64_t)sample->data[3] << 24;
    packed |= (uint64_t)sample->data[4] << 32;

    // Extract fields
    *sensor_id = packed & 0x01;             // Bit 0
    uint16_t x13 = (packed >> 1) & 0x1FFF;  // Bits 1-13
    uint16_t y13 = (packed >> 14) & 0x1FFF; // Bits 14-26
    uint16_t z13 = (packed >> 27) & 0x1FFF; // Bits 27-39

    // Sign-extend from 13 bits to 16 bits
    // If bit 12 is set (negative number), set bits 13-15 to 1
    if (x13 & 0x1000)
        x13 |= 0xE000; // Sign extend if bit 12 is set
    if (y13 & 0x1000)
        y13 |= 0xE000; // Sign extend if bit 12 is set
    if (z13 & 0x1000)
        z13 |= 0xE000; // Sign extend if bit 12 is set

    // Cast to signed 16-bit (no scaling needed - we preserved the original 13-bit values)
    *x = (int16_t)x13;
    *y = (int16_t)y13;
    *z = (int16_t)z13;
}

/**
 * @brief Create ADXL345 data processing task
 */
esp_err_t adxl345_create_data_task(adxl345_dev_t *dev, const char *task_name, uint32_t stack_size, UBaseType_t priority, QueueHandle_t sample_queue, uint8_t sensor_id)
{
    // Allocate memory for task parameters
    adxl345_data_task_params_t *params = malloc(sizeof(adxl345_data_task_params_t));
    if (params == NULL)
    {
        ESP_LOGE(TAG, "Failed to allocate memory for task parameters");
        return ESP_FAIL;
    }

    // Initialize task parameters
    params->dev = dev;
    params->sample_queue = sample_queue;
    params->sensor_id = sensor_id;

    BaseType_t xReturned = xTaskCreate(
        adxl345_data_task, // Task function
        task_name,         // Name for debugging
        stack_size,        // Stack size
        params,            // Parameter = pointer to params struct
        priority,          // Priority
        NULL);

    if (xReturned != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to create data task: %s", task_name);
        free(params); // Clean up allocated memory
        return ESP_FAIL;
    }

    return ESP_OK;
}

/**
 * @brief Create ADXL345 rate monitoring task
 */
esp_err_t adxl345_create_rate_task(adxl345_dev_t *dev, const char *task_name, uint32_t stack_size, UBaseType_t priority)
{
    BaseType_t xReturned = xTaskCreate(
        adxl345_rate_task, // Task function
        task_name,         // Name for debugging
        stack_size,        // Stack size
        dev,               // Parameter = pointer to dev struct
        priority,          // Priority
        NULL);

    if (xReturned != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to create rate task: %s", task_name);
        return ESP_FAIL;
    }

    return ESP_OK;
}
