#include "uart_transmit.h"
#include "interface.h"
#include "adxl345.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include <string.h>

#define TAG "UART_TX"
#define UART_PORT_NUM UART_NUM_0
#define UART_BUF_SIZE (2048)
#define UART_TX_PIN 43
#define UART_RX_PIN 44

static uint32_t s_sample_count = 0;

/**
 * @brief Initialize UART0 for high-performance data transmission
 */
esp_err_t uart_transmit_init(void)
{
    ESP_LOGI(TAG, "Initializing UART0 for data transmission at %d baud", UART_BAUD_RATE);

    // Configure UART parameters
    uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };

    // Install UART driver
    esp_err_t ret = uart_driver_install(UART_PORT_NUM, UART_BUF_SIZE * 2, 0, 0, NULL, 0);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(ret));
        return ret;
    }

    // Configure UART parameters
    ret = uart_param_config(UART_PORT_NUM, &uart_config);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to configure UART parameters: %s", esp_err_to_name(ret));
        uart_driver_delete(UART_PORT_NUM);
        return ret;
    }

    // Set UART pins (TX, RX, RTS, CTS)
    ret = uart_set_pin(UART_PORT_NUM, UART_TX_PIN, UART_RX_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(ret));
        uart_driver_delete(UART_PORT_NUM);
        return ret;
    }

    ESP_LOGI(TAG, "UART0 initialized successfully (TX=%d, RX=%d, Baud=%d)",
             UART_TX_PIN, UART_RX_PIN, UART_BAUD_RATE);
    return ESP_OK;
}

esp_err_t uart_transmit_send_sample(const adxl345_sample_t *sample)
{
    if (sample == NULL)
    {
        return ESP_ERR_INVALID_ARG;
    }

    uart_write_bytes(UART_PORT_NUM, sample->data, sizeof(sample->data));
    s_sample_count++;
    if (s_sample_count % SYNC_INTERVAL == 0)
    {
        uart_write_bytes(UART_PORT_NUM, SYNC_PACKET, 5);
    }

    return ESP_OK;
}


/**
 * @brief Deinitialize UART transmission
 */
esp_err_t uart_transmit_deinit(void)
{
    ESP_LOGI(TAG, "Deinitializing UART transmission");
    return uart_driver_delete(UART_PORT_NUM);
}
