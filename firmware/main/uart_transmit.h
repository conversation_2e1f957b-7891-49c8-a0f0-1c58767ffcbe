#ifndef UART_TRANSMIT_H
#define UART_TRANSMIT_H

#include <stdint.h>
#include "esp_err.h"
#include "adxl345.h"

/**
 * @brief Initialize UART0 for high-performance data transmission
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t uart_transmit_init(void);

/**
 * @brief Push a packed accelerometer sample to UART.
 * @param sample Pointer to packed sample
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t uart_transmit_send_sample(const adxl345_sample_t *sample);

/**
 * @brief Deinitialize UART transmission
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t uart_transmit_deinit(void);

#endif // UART_TRANSMIT_H
