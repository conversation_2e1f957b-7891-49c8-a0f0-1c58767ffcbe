#include "sample_processing.h"
#include "uart_transmit.h"
#include "sdstream.h"
#include "adxl345.h"
#include "esp_log.h"
#include "freertos/task.h"

static const char *TAG = "PROC";

typedef struct
{
    QueueHandle_t sample_queue;
} sample_processing_params_t;

static void sample_processing_task(void *pvParameters)
{
    sample_processing_params_t *params = (sample_processing_params_t *)pvParameters;
    QueueHandle_t q = params->sample_queue;
    adxl345_sample_t sample;

    ESP_LOGI(TAG, "Sample processing task started");

    while (1)
    {
        if (xQueueReceive(q, &sample, portMAX_DELAY) == pdTRUE)
        {
            uart_transmit_send_sample(&sample);
            if (sdstream_is_mounted())
            {
                sdstream_write(sample);
            }
        }
    }
}

esp_err_t sample_processing_create_task(QueueHandle_t sample_queue,
                                        const char *task_name,
                                        uint32_t stack_size,
                                        UBaseType_t priority)
{
    if (sample_queue == NULL)
    {
        ESP_LOGE(TAG, "Sample queue cannot be NULL");
        return ESP_ERR_INVALID_ARG;
    }

    sample_processing_params_t *params = malloc(sizeof(sample_processing_params_t));
    if (params == NULL)
    {
        ESP_LOGE(TAG, "Failed to allocate parameters");
        return ESP_ERR_NO_MEM;
    }
    params->sample_queue = sample_queue;

    BaseType_t ret = xTaskCreate(sample_processing_task, task_name, stack_size,
                                 params, priority, NULL);
    if (ret != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to create processing task");
        free(params);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Sample processing task created: %s", task_name);
    return ESP_OK;
}
