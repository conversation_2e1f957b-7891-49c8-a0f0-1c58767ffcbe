#ifndef SAMPLE_PROCESSING_H
#define SAMPLE_PROCESSING_H

#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"

esp_err_t sample_processing_create_task(QueueHandle_t sample_queue,
                                        const char *task_name,
                                        uint32_t stack_size,
                                        UBaseType_t priority);

#endif // SAMPLE_PROCESSING_H
