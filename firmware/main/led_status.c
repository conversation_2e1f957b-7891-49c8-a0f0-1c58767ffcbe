/**
 * @file led_status.c
 * @brief LED status indication implementation for ESP32-S3-DevKitC-1 v1.1
 */

#include "led_status.h"
#include "led_strip.h"
#include "led_strip_rmt.h"
#include "esp_log.h"
#include "esp_err.h"

#define TAG "LED_STATUS"

// LED configuration for ESP32-S3-DevKitC-1 v1.1
#define LED_STRIP_GPIO_NUM 38
#define LED_STRIP_LED_COUNT 1
#define LED_STRIP_RMT_CHANNEL 0

// RGB color definitions (0-255)
#define LED_COLOR_RED {200, 0, 0}
#define LED_COLOR_GREEN {0, 200, 0}
#define LED_COLOR_BLUE {0, 0, 200}
#define LED_COLOR_WHITE {200, 200, 200}
#define LED_COLOR_OFF {0, 0, 0}

static led_strip_handle_t led_strip = NULL;

/**
 * @brief Set RGB color on the LED strip
 */
static esp_err_t set_led_color(uint8_t red, uint8_t green, uint8_t blue)
{
    if (led_strip == NULL)
    {
        ESP_LOGE(TAG, "LED strip not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret = led_strip_set_pixel(led_strip, 0, red, green, blue);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set LED pixel: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = led_strip_refresh(led_strip);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to refresh LED strip: %s", esp_err_to_name(ret));
        return ret;
    }

    return ESP_OK;
}

esp_err_t led_status_init(void)
{
    ESP_LOGI(TAG, "Initializing LED status on GPIO%d", LED_STRIP_GPIO_NUM);

    // LED strip configuration
    led_strip_config_t strip_config = {
        .strip_gpio_num = LED_STRIP_GPIO_NUM,
        .max_leds = LED_STRIP_LED_COUNT,
        .led_model = LED_MODEL_WS2812,
        .color_component_format = LED_STRIP_COLOR_COMPONENT_FMT_GRB,
        .flags.invert_out = false,
    };

    // LED strip RMT configuration
    led_strip_rmt_config_t rmt_config = {
        .clk_src = RMT_CLK_SRC_DEFAULT,
        .resolution_hz = 10 * 1000 * 1000, // 10MHz
        .flags.with_dma = false,
    };

    esp_err_t ret = led_strip_new_rmt_device(&strip_config, &rmt_config, &led_strip);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to create LED strip: %s", esp_err_to_name(ret));
        return ret;
    }

    // Clear the LED initially
    ret = led_strip_clear(led_strip);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to clear LED strip: %s", esp_err_to_name(ret));
        led_strip_del(led_strip);
        led_strip = NULL;
        return ret;
    }

    ESP_LOGI(TAG, "LED status initialized successfully");
    return ESP_OK;
}

esp_err_t led_status_set(led_status_t status)
{
    if (led_strip == NULL)
    {
        ESP_LOGE(TAG, "LED strip not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret;

    switch (status)
    {
    case LED_STATUS_INITIALIZING:
    {
        uint8_t color[] = LED_COLOR_RED;
        ret = set_led_color(color[0], color[1], color[2]);
        ESP_LOGI(TAG, "Status: Initializing (Red)");
        break;
    }
    case LED_STATUS_TWO_SENSORS:
    {
        uint8_t color[] = LED_COLOR_WHITE;
        ret = set_led_color(color[0], color[1], color[2]);
        ESP_LOGI(TAG, "Status: Two sensors detected (White)");
        break;
    }
    case LED_STATUS_SENSOR1_ONLY:
    {
        uint8_t color[] = LED_COLOR_GREEN;
        ret = set_led_color(color[0], color[1], color[2]);
        ESP_LOGI(TAG, "Status: Sensor 1 only (Green)");
        break;
    }
    case LED_STATUS_SENSOR2_ONLY:
    {
        uint8_t color[] = LED_COLOR_BLUE;
        ret = set_led_color(color[0], color[1], color[2]);
        ESP_LOGI(TAG, "Status: Sensor 2 only (Blue)");
        break;
    }
    case LED_STATUS_OFF:
    {
        uint8_t color[] = LED_COLOR_OFF;
        ret = set_led_color(color[0], color[1], color[2]);
        ESP_LOGI(TAG, "Status: LED off");
        break;
    }
    default:
        ESP_LOGE(TAG, "Invalid LED status: %d", status);
        return ESP_ERR_INVALID_ARG;
    }

    return ret;
}

void led_status_deinit(void)
{
    if (led_strip != NULL)
    {
        led_strip_clear(led_strip);
        led_strip_del(led_strip);
        led_strip = NULL;
        ESP_LOGI(TAG, "LED status deinitialized");
    }
}
