#include "sdstream.h"
#include <stdio.h>
#include <string.h>
#include "esp_vfs_fat.h" // for esp_vfs_fat_sdspi_mount, esp_vfs_fat_sdcard_unmount
#include "driver/spi_master.h"
#include "sdmmc_cmd.h" // for sdmmc_host_t, sdspi_device_config_t, SDSPI_HOST_DEFAULT(), SDSPI_DEVICE_CONFIG_DEFAULT()
#include "esp_log.h"
#include "nvs_flash.h"
#include "nvs.h"
#include <time.h>

static const char *TAG = "sdstream";

/* --- internal state --- */
static sdmmc_card_t *s_card = NULL;
static char s_mount_point[32] = {0};
static FILE *s_cur_file = NULL;
static int s_last_hour = -1;
static bool s_is_mounted = false;
static int32_t s_file_counter = 0; // persisted in NVS

static spi_bus_config_t s_spi_bus_cfg;
static sdspi_device_config_t s_slot_cfg;
static spi_host_device_t s_spi_host; // e.g. SPI2_HOST or SPI3_HOST

static QueueHandle_t s_sample_queue = NULL; // Queue for packed samples

/* NVS namespace and key */
static const char *NVS_NAMESPACE = "sdstream";
static const char *NVS_COUNTER_KEY = "file_counter";

/**
 * @brief   Initialize SPI bus parameters for the SD-card (but do NOT mount yet).
 */
esp_err_t sdstream_spi_bus_init(spi_host_device_t host,
                                int miso_pin,
                                int mosi_pin,
                                int sclk_pin,
                                int cs_pin)
{
    s_spi_host = host;

    // 1) Fill in spi_bus_config_t so we can call spi_bus_initialize() later
    s_spi_bus_cfg.miso_io_num = miso_pin;
    s_spi_bus_cfg.mosi_io_num = mosi_pin;
    s_spi_bus_cfg.sclk_io_num = sclk_pin;
    s_spi_bus_cfg.quadwp_io_num = -1;
    s_spi_bus_cfg.quadhd_io_num = -1;
    s_spi_bus_cfg.max_transfer_sz = 8 * 1024; // 8 KB

    // 2) Start with the default SDSPI_DEVICE_CONFIG, then override CS and host_id
    s_slot_cfg.host_id = host;
    s_slot_cfg.gpio_cs = cs_pin;
    s_slot_cfg.gpio_cd = SDSPI_SLOT_NO_CD;
    s_slot_cfg.gpio_wp = SDSPI_SLOT_NO_WP;
    s_slot_cfg.gpio_int = GPIO_NUM_NC;
    s_slot_cfg.gpio_wp_polarity = SDSPI_IO_ACTIVE_LOW;

    ESP_LOGI(TAG,
             "SPI bus params set (host=%d, MISO=%d, MOSI=%d, SCLK=%d, CS=%d)",
             host, miso_pin, mosi_pin, sclk_pin, cs_pin);
    return ESP_OK;
}

/**
 * @brief   Mount the SD card over SPI at `mount_point`, initialize NVS,
 *          and load the persisted file counter.
 *
 * This uses esp_vfs_fat_sdspi_mount() instead of the deprecated all-in-one call.
 */
esp_err_t sdstream_mount(const char *mount_point)
{
    if (s_is_mounted)
    {
        ESP_LOGW(TAG, "Already mounted at %s", s_mount_point);
        return ESP_OK;
    }

    /* --- 1) Initialize NVS and load persisted counter --- */
    esp_err_t err = nvs_flash_init();
    if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_ERROR_CHECK(nvs_flash_erase());
        err = nvs_flash_init();
    }
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to init NVS (0x%X)", err);
        return err;
    }

    nvs_handle_t nvs_handle;
    err = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "nvs_open failed (0x%X)", err);
        return err;
    }
    int32_t stored = 0;
    err = nvs_get_i32(nvs_handle, NVS_COUNTER_KEY, &stored);
    if (err == ESP_ERR_NVS_NOT_FOUND)
    {
        s_file_counter = 0;
    }
    else if (err == ESP_OK)
    {
        s_file_counter = stored;
    }
    else
    {
        nvs_close(nvs_handle);
        ESP_LOGE(TAG, "nvs_get_i32 failed (0x%X)", err);
        return err;
    }
    nvs_close(nvs_handle);

    /* --- 2) Remember mount point --- */
    strncpy(s_mount_point, mount_point, sizeof(s_mount_point) - 1);
    s_mount_point[sizeof(s_mount_point) - 1] = '\0';

    /* --- 3) Initialize the SPI bus itself --- */
    //     Must do this before we call esp_vfs_fat_sdspi_mount()
    err = spi_bus_initialize(s_spi_host, &s_spi_bus_cfg, SPI_DMA_CH_AUTO);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "spi_bus_initialize failed (0x%X)", err);
        return err;
    }
    vTaskDelay(100 / portTICK_PERIOD_MS); // give it a moment to settle

    /* --- 4) Mount FAT over SPI-SD --- */
    // Prepare an sdmmc_host_t for SPI (matching the SPI bus we just initialized)
    sdmmc_host_t host = SDSPI_HOST_DEFAULT();
    host.slot = s_spi_host;    // ensure it uses the same SPI peripheral
    host.max_freq_khz = 20000; // or whatever frequency you need

    esp_vfs_fat_mount_config_t mount_cfg = {
        .format_if_mount_failed = true,
        .max_files = 5,
        .allocation_unit_size = 16 * 1024};

    err = esp_vfs_fat_sdspi_mount(
        s_mount_point, // e.g. "/sdcard"
        &host,         // <<< pass our SPI-based host instead of NULL
        &s_slot_cfg,   // CS pin, SPI mode, etc.
        &mount_cfg,
        &s_card);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "esp_vfs_fat_sdspi_mount failed (0x%X)", err);
        // Clean up SPI bus if mount fails (no card or other error)
        spi_bus_free(s_spi_host);
        return err;
    }

    ESP_LOGI(TAG,
             "SD card mounted at %s (capacity: %llu MB), counter=%ld",
             s_mount_point,
             (long long)(s_card->csd.capacity / (1024 * 1024)),
             (long)s_file_counter);

    s_is_mounted = true;
    s_last_hour = -1; // force new file on first write
    return ESP_OK;
}

/**
 * @brief   Unmount the SD card and free resources.
 *          Uses the non-deprecated esp_vfs_fat_sdcard_unmount().
 */
esp_err_t sdstream_unmount(void)
{
    if (s_cur_file)
    {
        fclose(s_cur_file);
        s_cur_file = NULL;
        s_last_hour = -1;
    }

    if (s_is_mounted)
    {
        // 1) Unmount FAT/VFS
        esp_err_t err = esp_vfs_fat_sdcard_unmount(s_mount_point, s_card);
        if (err != ESP_OK)
        {
            ESP_LOGE(TAG, "esp_vfs_fat_sdcard_unmount failed (0x%X)", err);
            return err;
        }
        ESP_LOGI(TAG, "SD card unmounted from %s", s_mount_point);

        // 2) Free the SPI bus
        err = spi_bus_free(s_spi_host);
        if (err != ESP_OK)
        {
            ESP_LOGE(TAG, "spi_bus_free failed (0x%X)", err);
            return err;
        }
        ESP_LOGI(TAG, "SPI bus freed (host=%d)", s_spi_host);

        s_is_mounted = false;
    }
    else
    {
        ESP_LOGW(TAG, "sdstream_unmount() called but nothing was mounted");
    }
    return ESP_OK;
}

/* ---------------------------------------------------------
 * Internal: open a new file named data_<counter>.bin,
 * increment counter, and persist it to NVS.
 * -------------------------------------------------------- */
static esp_err_t open_new_hourly_file(void)
{
    /* --- Increment and persist counter in NVS --- */
    s_file_counter++;

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "nvs_open failed (0x%X)", err);
        return err;
    }
    err = nvs_set_i32(nvs_handle, NVS_COUNTER_KEY, s_file_counter);
    if (err != ESP_OK)
    {
        nvs_close(nvs_handle);
        ESP_LOGE(TAG, "nvs_set_i32 failed (0x%X)", err);
        return err;
    }
    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "nvs_commit failed (0x%X)", err);
        return err;
    }

    /* --- Build filename: <mount_point>/data_00001.bin, etc. --- */
    char fname[64];
    int len = snprintf(
        fname, sizeof(fname),
        "%s/data_%05ld.bin",
        s_mount_point,
        s_file_counter);
    if (len >= (int)sizeof(fname))
    {
        ESP_LOGE(TAG, "Filename buffer too small");
        return ESP_FAIL;
    }

    /* --- Close old file if open --- */
    if (s_cur_file)
    {
        fflush(s_cur_file);
        fclose(s_cur_file);
        s_cur_file = NULL;
    }

    s_cur_file = fopen(fname, "wb");
    if (!s_cur_file)
    {
        ESP_LOGE(TAG, "Failed to open %s", fname);
        return ESP_FAIL;
    }

    if (setvbuf(s_cur_file, NULL, _IOFBF, 4096) != 0)
    {
        ESP_LOGE(TAG, "Failed to set buffer for %s", fname);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Opened new file: %s (counter=%ld)", fname, s_file_counter);

    /* --- Update last-hour for rotation logic --- */
    time_t now = time(NULL);
    struct tm tm_info;
    localtime_r(&now, &tm_info);
    s_last_hour = tm_info.tm_hour;

    return ESP_OK;
}

/* ---------------------------------------------------------
 * Write data; if hour has changed (or no file yet), rotate
 * -------------------------------------------------------- */
esp_err_t sdstream_write_internal(const void *data, size_t size)
{
    if (!s_is_mounted)
    {
        ESP_LOGD(TAG, "sdstream_write() called before mount");
        return ESP_ERR_INVALID_STATE;
    }

    time_t now = time(NULL);
    struct tm tm_info;
    localtime_r(&now, &tm_info);

    /* Rotate on first write or hour rollover */
    if (s_cur_file == NULL || tm_info.tm_hour != s_last_hour)
    {
        if (tm_info.tm_hour != s_last_hour)
        {
            ESP_LOGI(TAG, "Hour rollover");
        }

        esp_err_t ret = open_new_hourly_file();
        if (ret != ESP_OK)
        {
            return ret;
        }
    }

    size_t written = fwrite(data, 1, size, s_cur_file);
    if (written != size)
    {
        ESP_LOGE(TAG, "Wrote %u/%u bytes", (unsigned)written, (unsigned)size);
        return ESP_FAIL;
    }
    return ESP_OK;
}

esp_err_t sdstream_write(adxl345_sample_t data)
{
    static adxl345_sample_t acc[32];
    static int acc_index = 0;
    acc[acc_index++] = data;
    if (acc_index < 32)
    {
        return ESP_OK; // accumulate more samples
    }
    acc_index = 0; // reset index for next batch
    if (!xQueueSend(s_sample_queue, acc, 0))
    {
        ESP_LOGE(TAG, "Failed to send sample to queue");
        return ESP_FAIL;
    }
    return ESP_OK;
}

static void sdstream_writer_task(void *pvParameters)
{
    ESP_LOGI(TAG, "sdstream_writer_task started");
    while (1)
    {
        adxl345_sample_t samples[32];
        if (xQueueReceive(s_sample_queue, samples, portMAX_DELAY))
        {
            sdstream_write_internal(samples, sizeof(samples));
        }
    }
}

void start_sdstream_writer_task(void)
{
    s_sample_queue = xQueueCreate(8, 32 * sizeof(adxl345_sample_t));
    xTaskCreate(sdstream_writer_task, "sdstream_writer", 4096, NULL, 7, NULL);
}

bool sdstream_is_mounted(void)
{
    return s_is_mounted;
}
