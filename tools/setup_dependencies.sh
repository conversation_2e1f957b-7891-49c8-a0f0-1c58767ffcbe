#!/usr/bin/env bash
# Install local development dependencies for Ubuntu.
# This script installs typst, uv, and the ESP-IDF.
set -euo pipefail

# Install typst (typesetting tool)
if ! command -v typst >/dev/null 2>&1; then
    ARCH="$(uname -m)"
    case "$ARCH" in
        x86_64)
            TYPST_ARCH="x86_64-unknown-linux-musl"
            ;;
        aarch64|arm64)
            TYPST_ARCH="aarch64-unknown-linux-musl"
            ;;
        *)
            echo "Unsupported architecture: $ARCH" >&2
            exit 1
            ;;
    esac
    curl -L "https://github.com/typst/typst/releases/latest/download/typst-${TYPST_ARCH}.tar.xz" \
        | tar -xJf -
    sudo mv "typst-${TYPST_ARCH}/typst" /usr/local/bin/typst
    rm -rf "typst-${TYPST_ARCH}"
else
    echo "typst already installed"
fi

# Install uv (Python package manager)
if ! command -v uv >/dev/null 2>&1; then
    curl -Ls https://astral.sh/uv/install.sh | bash
else
    echo "uv already installed"
fi

# Install ESP-IDF toolchain
ESP_DIR="$HOME/esp"
IDF_PATH="$ESP_DIR/esp-idf"
ESP_IDF_VERSION="v5.3.2"

if [ ! -d "$IDF_PATH" ]; then
    sudo apt-get update
    sudo apt-get install -y git wget flex bison gperf python3 python3-pip python3-setuptools cmake ninja-build ccache libffi-dev libssl-dev dfu-util

    mkdir -p "$ESP_DIR"
    git clone --recursive https://github.com/espressif/esp-idf.git -b "$ESP_IDF_VERSION" "$IDF_PATH"
    "$IDF_PATH"/install.sh
else
    echo "ESP-IDF already installed at $IDF_PATH"
fi

# Setup environment on next shell session
if ! grep -q "source \$HOME/esp/esp-idf/export.sh" "$HOME/.bashrc" 2>/dev/null; then
    echo "source \$HOME/esp/esp-idf/export.sh" >> "$HOME/.bashrc"
fi

echo "Setup complete. Restart your shell or run 'source $HOME/esp/esp-idf/export.sh' to use ESP-IDF." 
