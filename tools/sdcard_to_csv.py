import argparse
import csv

SAMPLES_PER_SECOND = 3200


def sign_extend_13bit(value: int) -> int:
    """Sign-extend a 13-bit two's complement value to 16-bit."""
    if value & 0x1000:
        return value | ~0x1FFF & 0xFFFF
    return value & 0x1FFF


def unpack_sample(data: bytes):
    if len(data) != 5:
        raise ValueError("Sample must be exactly 5 bytes")
    packed = data[0]
    packed |= data[1] << 8
    packed |= data[2] << 16
    packed |= data[3] << 24
    packed |= data[4] << 32
    sensor_id = packed & 0x01
    x13 = (packed >> 1) & 0x1FFF
    y13 = (packed >> 14) & 0x1FFF
    z13 = (packed >> 27) & 0x1FFF
    x = sign_extend_13bit(x13)
    y = sign_extend_13bit(y13)
    z = sign_extend_13bit(z13)
    return sensor_id, x if x < 0x8000 else x - 0x10000, y if y < 0x8000 else y - 0x10000, z if z < 0x8000 else z - 0x10000


def convert(input_path: str, output_prefix: str, sample_rate: float = SAMPLES_PER_SECOND) -> None:
    out1_path = f"{output_prefix}-s1.csv"
    out2_path = f"{output_prefix}-s2.csv"
    with open(input_path, "rb") as f, \
            open(out1_path, "w", newline="") as f1, \
            open(out2_path, "w", newline="") as f2:
        w1 = csv.writer(f1)
        w2 = csv.writer(f2)
        w1.writerow(["time_offset", "x", "y", "z"])
        w2.writerow(["time_offset", "x", "y", "z"])
        sample_idx = 0
        while True:
            chunk = f.read(5)
            if len(chunk) < 5:
                break
            sid, x, y, z = unpack_sample(chunk)
            time_ms = sample_idx / sample_rate * 1000.0
            row = [f"{time_ms:.6f}", x, y, z]
            if sid == 0:
                w1.writerow(row)
            elif sid == 1:
                w2.writerow(row)
            sample_idx += 1
    print(f"Wrote {out1_path} and {out2_path}")


def main():
    parser = argparse.ArgumentParser(description="Convert SD card binary data to LabRecord CSV format")
    parser.add_argument("input", help="Path to binary data file from SD card")
    parser.add_argument("output_prefix", help="Prefix for generated CSV files")
    parser.add_argument("--sample-rate", type=float, default=SAMPLES_PER_SECOND, help="Samples per second used during recording")
    args = parser.parse_args()

    convert(args.input, args.output_prefix, args.sample_rate)


if __name__ == "__main__":
    main()
